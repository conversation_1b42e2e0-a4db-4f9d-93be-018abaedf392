# Editor cache and lock files
*.swp
*.swo

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib


# ignore all dist folders
dist/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

.env
controller/node_modules
.DS_Store
test/
deployments/
controller/package-lock.json
package-lock.json
node_modules/
.vscode/
.idea/
controller/.gitmodules
.infisical.json

# vscode file version control history
.history