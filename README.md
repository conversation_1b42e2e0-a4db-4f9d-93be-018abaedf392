# Port0 Benchmark

## Build and Run

### Before running the docker image: 

- make sure that you have correctly set the **required environment variables**.

```
docker build -f Dockerfile -t port0-steampipe .

docker run \
  -e CONFIG="$CONFIG" \
  -e GOOGLE_APPLICATION_CREDENTIALS="/credentials/key.json" \
  -v "$GOOGLE_APPLICATION_CREDENTIALS:/credentials/key.json:ro" \
  --memory="32g" --cpus="8" --rm port0-steampipe

```

## Setup Gcloud

```bash
gcloud auth login
gcloud config set project port0-dev2  && export PROJECT_ID=$(gcloud config get-value project)
export PROJECT_ID=$(gcloud config get-value project)
```

## Create Google service account

```bash
gcloud iam service-accounts create steampipe-benchmark-sa \
  --display-name "Service account for steampipe benchmark"

gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:steampipe-benchmark-sa@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/datastore.user"

# Give permission to elastic search credentials - Make sure both environment variables are set
gcloud secrets create ELASTIC_LOG_URL --data-file=<(echo -n $ELASTIC_LOG_URL)
gcloud secrets create ELASTIC_LOG_API_KEY --data-file=<(echo -n $ELASTIC_LOG_API_KEY)

gcloud beta secrets add-iam-policy-binding projects/$PROJECT_ID/secrets/ELASTIC_LOG_API_KEY --member serviceAccount:steampipe-benchmark-sa@$PROJECT_ID.iam.gserviceaccount.com --role roles/secretmanager.secretAccessor
gcloud beta secrets add-iam-policy-binding projects/$PROJECT_ID/secrets/ELASTIC_LOG_URL --member serviceAccount:steampipe-benchmark-sa@$PROJECT_ID.iam.gserviceaccount.com --role roles/secretmanager.secretAccessor

gcloud iam service-accounts keys create deployments/benchmark-$PROJECT_ID.json --iam-account steampipe-benchmark-sa@$PROJECT_ID.iam.gserviceaccount.com

export GOOGLE_APPLICATION_CREDENTIALS=~/benchmark-dev.json
```

## Run docker local

Running the container with google credentials as

```bash
docker build -f Dockerfile -t port0-steampipe .
docker run \
  -e CONFIG="$(cat ./deployments/plasan-dev.json)" \
  -e GOOGLE_APPLICATION_CREDENTIALS=/credentials/key.json \
  -v $(pwd)/deployments/benchmark-$PROJECT_ID.json:/credentials/key.json:ro \
  --memory="32g" --cpus="8" --rm port0-steampipe

```

## Deploy as cloud run job

```bash
docker buildx build --platform linux/amd64 -t gcr.io/$PROJECT_ID/benchmark .
docker push gcr.io/$PROJECT_ID/benchmark

gcloud run jobs delete steampipe-benchmark-job --region=us-central1 --quiet
gcloud run jobs create steampipe-benchmark-job \
  --image gcr.io/$PROJECT_ID/benchmark:latest \
  --region us-central1 \
  --cpu 8 \
  --memory 32Gi \
  --set-env-vars CONFIG="",FIREBASE_ENVIRONMENT=dev \
  --set-secrets ELASTIC_LOG_URL=ELASTIC_LOG_URL:latest \
  --set-secrets ELASTIC_LOG_API_KEY=ELASTIC_LOG_API_KEY:latest \
  --service-account steampipe-benchmark-sa@$PROJECT_ID.iam.gserviceaccount.com \
  --task-timeout 1800s
```

## Cleanup Cloud run

```bash
# 2. Delete the Cloud Run job
gcloud run jobs delete steampipe-benchmark-job --region=us-central1 --quiet

# 4. Delete the secret
gcloud secrets delete ELASTIC_LOG_URL --quiet
gcloud secrets delete ELASTIC_LOG_API_KEY --quiet

# 6. Delete the service account
gcloud iam service-accounts delete steampipe-benchmark-sa@$PROJECT_ID.iam.gserviceaccount.com --quiet
```

## Testing with act

- Make sure you have act installed

```bash
export DOCKER_HOST=unix:///var/run/docker.sock

act -P ubuntu-latest=catthehacker/ubuntu:act-latest \
  -j build-and-push \
  --container-architecture linux/amd64 \
  -s GCLOUD_PROJECT=your-real-project-id \
  -s GOOGLE_APPLICATION_CREDENTIALS='{}'

```

## Github Actions iam service account
```
gcloud iam service-accounts create github-actions \
  --description="Service account for GitHub Actions to push images to Artifact Registry" \
  --display-name="GitHub Actions"

gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:github-actions@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/artifactregistry.writer"

gcloud iam service-accounts keys create github-actions.json --iam-account github-actions@$PROJECT_ID.iam.gserviceaccount.com
```