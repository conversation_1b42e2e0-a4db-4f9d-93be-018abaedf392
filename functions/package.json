{"name": "trigger-functions", "version": "1.0.0", "description": "2nd Gen Cloud Functions for trigger functions in TypeScript", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "start:dev": "~/bin/infisical-run ts-node src/local-debug.ts", "build": "tsup && cp package.json dist && npm run copy-assets && npm run copy-sql-files", "copy-assets": "mkdir -p dist/instructions && cp ../benchmark/controller/src/services/vertex-ai/instructions/issue.txt dist/instructions/", "copy-sql-files": "mkdir -p dist/assets/ && cp -r ../benchmark/controller/src/big-query dist/assets/", "dev": "npm run build && npm run start", "prettier-write": "prettier --write \"**/*.ts\"", "test": "jest"}, "engines": {"node": "22"}, "dependencies": {"@clerk/backend": "^1.32.0", "@elastic/ecs-winston-format": "^1.5.3", "@google-cloud/bigquery": "^7.9.4", "p-queue": "^6.6.2", "p-retry": "^4.6.2", "@google-cloud/secret-manager": "^5.6.0", "@google-cloud/vertexai": "^1.10.0", "@types/ip": "^1.1.3", "axios": "^1.7.7", "bunyan": "^1.8.15", "coralogix-logger": "^1.1.30", "coralogix-logger-bunyan": "^1.0.10", "dotenv": "^16.4.7", "firebase-admin": "^12.6.0", "firebase-functions": "^6.1.0", "ip": "^2.0.1", "ip-range-check": "^0.2.0", "jest": "^29.7.0", "lodash": "^4.17.21", "svix": "^1.65.0", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-elasticsearch": "^0.19.0", "zod": "^3.25.51"}, "devDependencies": {"@types/bunyan": "^1.8.11", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.14", "lint-staged": "^15.4.3", "prettier": "^3.5.3", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsc-alias": "^1.8.15", "tsconfig-paths": "^4.2.0", "tsup": "^8.4.0", "typescript": "^4.9.5"}, "lint-staged": {"**/*.ts": ["prettier --write \"**/*.ts\"", "git add"]}}