module.exports = {
  rootDir: 'src',
  preset: 'ts-jest',
  testEnvironment: 'node',
  transform: {
    '^.+\\.tsx?$': 'ts-jest'
  },
  moduleNameMapper: {
    '@globalTypes/ndrBenchmarkTypes':
      '<rootDir>/../../benchmark/controller/src/globalTypes/ndrBenchmarkTypes',
    '@logger': '<rootDir>/../../benchmark/controller/src/logger'
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node']
};
