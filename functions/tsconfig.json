{"compilerOptions": {"target": "ES2020", "module": "commonjs", "baseUrl": "./src", "paths": {"@globalTypes/*": ["../../benchmark/controller/src/globalTypes/*"], "@logger": ["../../benchmark/controller/src/logger"], "@controller/*": ["../../benchmark/controller/*"], "@activityLog/*": ["../../benchmark/controller/src/activityLog/*"], "@big-query-client/*": ["../../benchmark/controller/src/big-query-client/*"]}, "strict": true, "esModuleInterop": true, "resolveJsonModule": true, "skipLibCheck": true}, "include": ["src"], "exclude": ["node_modules", "dist"], "ts-node": {"require": ["tsconfig-paths/register"]}}