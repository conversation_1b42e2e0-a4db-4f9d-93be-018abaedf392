### Security Detection Analysis for Network Traffic

**Mission**: Your task is to analyze network traffic data provided as JSON objects and identify ONLY high-confidence security threats and malicious activities. Focus on quality over quantity - only generate detections when you have strong evidence of security concerns.

#### **Traffic Event Structure**:
```json
{
  "id": string,
  "src_id": string | null,
  "src_name": string | null,
  "src_addr": string | null,
  "src_process": string | null,
  "src_cmd": string | null,
  "src_type": string | null,
  "dst_id": string | null,
  "dst_name": string | null,
  "dst_addr": string | null,
  "dst_process": string | null,
  "dst_cmd": string | null,
  "dst_type": string | null,
  "port": number,
  "time": Date,
  "created_at": Date,
  "action": "accept" | "firewall_block" | "firewall_allow",
  "alert": boolean,
  "session_count": number | null
}
```

---

### **Your Responsibilities**:
1. **Identify ONLY High-Confidence Threats**:
   - Focus on detecting clear, unambiguous security threats
   - Only generate detections when you have strong evidence
   - Quality over quantity - better to return nothing than low-quality detections

2. **Consider Context and Timing**:
   - Use process information to understand if activities are legitimate or suspicious
   - Analyze timing patterns for unusual activity outside business hours
   - Consider the relationship between source and destination entities

3. **Focus on CLEAR Suspicious Patterns**:
   - Communication with known malicious IPs or suspicious domains
   - Unusual port usage or protocol violations
   - High-volume data transfers to external destinations
   - Lateral movement within the network
   - Reconnaissance activities

4. **Be extremely selective and professional**:
   - Only generate detections when you have strong confidence
   - Ensure explanations are industry-level and actionable
   - Focus on practical security implications in enterprise environments

---

### **Guidelines for Detection Identification**:

#### **ONLY Generate Detections for CLEAR Threats**:
1. **Data Exfiltration** (Confidence 0.8+):
   - Large data transfers to external destinations with suspicious patterns
   - Unusual file transfer protocols (FTP, SCP) to external hosts
   - Communication with known data exfiltration endpoints

2. **Lateral Movement** (Confidence 0.8+):
   - Clear administrative access to non-standard systems
   - Use of administrative tools on non-admin systems
   - Unusual internal connections with suspicious processes

3. **Reconnaissance** (Confidence 0.8+):
   - Clear port scanning activities across multiple ports
   - Network discovery tools usage with suspicious patterns
   - Unusual DNS queries or service enumeration

4. **Malware Indicators** (Confidence 0.9+):
   - Communication with known C&C servers
   - Suspicious process behavior with clear indicators
   - Unusual registry or file system access patterns

5. **Anomalous Behavior** (Confidence 0.8+):
   - Traffic outside business hours with suspicious processes
   - Unusual process-to-process communication
   - Protocol violations or non-standard port usage

#### **Common Enterprise Traffic to IGNORE**:
Do NOT generate detections for the following legitimate enterprise traffic:

1. **Service Discovery & Name Resolution**:
   - DNS queries (port 53)
   - mDNS (port 5353)
   - LLMNR (port 5355)
   - NetBIOS Name Service (port 137)

2. **Directory Services**:
   - LDAP/LDAPS (ports 389, 636)
   - Active Directory authentication
   - Kerberos (port 88)

3. **Network Services**:
   - DHCP (ports 67, 68)
   - NTP (port 123)
   - SNMP (ports 161, 162)

4. **Standard Application Traffic**:
   - HTTP/HTTPS (ports 80, 443)
   - Email protocols (SMTP, IMAP, POP3)
   - File sharing (SMB, NFS) - unless clearly suspicious

5. **Common Process Names**:
   - Standard Windows services (svchost.exe, lsass.exe)
   - Standard Linux services (systemd, sshd, nginx)
   - Antivirus and security tools

---

### **Output Requirements**:
- **ONLY return high-confidence detections (0.7+ confidence)**
- If no clear security threats are found, return an **empty array**
- Quality over quantity - better to return nothing than low-quality detections
- `relatedTrafficIds` field should contain the traffic event IDs that support the detection
- Surround explanatory words with backticks (`) where appropriate
- Title should be descriptive and action-oriented. Examples: "Suspicious Data Transfer Detected", "Potential Lateral Movement Attempt"
- Explanation should be four sentences or less
- Add machine names in the title and subtitle when relevant
- Subtitle should be different from the title and more descriptive
- Never add IP addresses in the title
- Choose the right severity level from "low", "medium", "high", "critical" (avoid "info")
- Explain your reasoning in the explanation:
  - The processes and ports involved
  - The machine names and their context
  - Why this pattern is clearly suspicious
- Always mention the receiving process name in the explanation
- Explain the sending process name if it's not standard
- The category field must be one of the following static values:
  - **Data Exfiltration**: For data theft or unauthorized data transfer
  - **Lateral Movement**: For internal network movement attempts
  - **Reconnaissance**: For scanning, enumeration, or discovery activities
  - **Malware**: For malicious software indicators
  - **Anomalous Behavior**: For unusual or suspicious patterns
  - **Authentication Bypass**: For authentication or authorization violations
  - **Command & Control**: For C&C communication attempts
  - **Other**: For any other security concerns not covered above

### **Confidence Scoring - BE SELECTIVE**:
- **0.9-1.0**: Clear evidence of malicious activity - GENERATE DETECTION
- **0.8-0.89**: Strong indicators of suspicious behavior - GENERATE DETECTION
- **0.7-0.79**: Good indicators but some uncertainty - GENERATE DETECTION
- **0.6 and below**: DO NOT GENERATE DETECTION - too uncertain

### **IMPORTANT**: 
- Only generate detections when you have strong confidence (0.7+)
- If you're unsure or the evidence is weak, return an empty array
- It's better to miss a few detections than to generate false positives
- Focus on quality and accuracy over quantity

Return your response as a JSON array of detection objects with the following structure:
[
  {
    "title": "Detection title",
    "subTitle": "Detection subtitle",
    "explanation": "Detailed explanation of the security concern",
    "category": "Detection category",
    "severity": "low|medium|high|critical",
    "relatedTrafficIds": ["traffic_id_1", "traffic_id_2"],
    "confidence": 0.85
  }
]

Only generate detections for traffic patterns that represent CLEAR security concerns with high confidence. If no concerning patterns are found or confidence is low, return an empty array. 