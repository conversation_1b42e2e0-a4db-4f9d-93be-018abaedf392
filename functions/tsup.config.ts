import { defineConfig } from 'tsup';
import path from 'path';

export default defineConfig({
  entry: ['src/index.ts'],
  outDir: 'dist',
  target: 'node18',
  format: ['cjs'],
  clean: true,
  sourcemap: true,
  dts: false,
  splitting: false,
  shims: false,
  esbuildOptions(options) {
    options.platform = 'node';
    options.external = []; // bundle everything

    options.alias = {
      '@logger': path.resolve(__dirname, '../benchmark/controller/src/logger'),
      '@globalTypes': path.resolve(
        __dirname,
        '../benchmark/controller/src/globalTypes'
      ),
      '@activityLog': path.resolve(
        __dirname,
        '../benchmark/controller/src/activityLog'
      ),
      '@controller': path.resolve(__dirname, '../benchmark/controller'),
      '@big-query-client': path.resolve(
        __dirname,
        '../benchmark/controller/src/big-query-client'
      )
    };
  }
});
