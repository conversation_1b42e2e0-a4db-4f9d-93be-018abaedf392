import { UserLabel } from '@globalTypes/ndrBenchmarkTypes';
import { TableName } from '@big-query-client/bigquery/types';
import { AssetsTypes } from '@activityLog/types';

export type TriggerFunctionConfig = {
  documentPath: string;
  tableName: TableName | null;
  assetChanged: AssetsTypes;
};

export type CreateConditionalLabelRequest = {
  label: UserLabel;
  organizationId: string;
  entityId?: string;
  dryRun?: boolean;
};

// clerk types
export interface ClerkEventHttpRequest {
  client_ip: string;
  user_agent: string;
}

export interface ClerkEventAttributes {
  http_request: ClerkEventHttpRequest;
}

export interface ClerkWebhookEvent<T> {
  data: T;
  event_attributes: ClerkEventAttributes;
  object: 'event';
  timestamp: number;
  type: ClerkEventKey;
}

export interface OrganizationMembershipData {
  created_at: number;
  updated_at: number;
  id: string;
  object: 'organization_membership';
  role: string;
  organization: {
    id: string;
    name: string;
    created_by: string;
    slug: string;
    public_metadata: Record<string, unknown>;
    image_url: string;
    logo_url: string | null;
    created_at: number;
    updated_at: number;
    object: string;
  };
  public_user_data: {
    user_id: string;
    identifier: string;
    first_name: string | null;
    last_name: string | null;
    image_url: string;
    profile_image_url: string;
  };
}

export interface SessionData {
  abandon_at: number;
  client_id: string;
  created_at: number;
  expire_at: number;
  id: string;
  last_active_at: number;
  object: 'session';
  status: 'active' | 'ended' | 'removed' | 'revoked';
  updated_at: number;
  user_id: string;
}

export interface ClerkVerification {
  status: string;
  strategy: string;
  attempts?: number | null;
  expire_at?: number | null;
}

export interface ClerkEmailAddress {
  email_address: string;
  id: string;
  linked_to: any[];
  object: 'email_address';
  reserved?: boolean;
  verification: ClerkVerification;
}

export interface UserData {
  birthday: string;
  created_at: number;
  email_addresses: ClerkEmailAddress[];
  external_accounts: any[];
  external_id: string | null;
  first_name: string;
  gender: string;
  id: string;
  image_url: string;
  last_name: string | null;
  last_sign_in_at: number | null;
  object: 'user';
  password_enabled: boolean;
  phone_numbers: any[];
  primary_email_address_id: string | null;
  primary_phone_number_id: string | null;
  primary_web3_wallet_id: string | null;
  private_metadata: Record<string, unknown>;
  profile_image_url: string;
  public_metadata: Record<string, unknown>;
  two_factor_enabled: boolean;
  unsafe_metadata: Record<string, unknown>;
  updated_at: number;
  username: string | null;
  web3_wallets: any[];
}

export interface DeletedUserData {
  deleted: true;
  id: string;
  object: 'user';
}

export type OrganizationMembershipEvent =
  ClerkWebhookEvent<OrganizationMembershipData>;
export type SessionEvent = ClerkWebhookEvent<SessionData>;
export type UserCreatedEvent = ClerkWebhookEvent<UserData>;
export type UserDeletedEvent = ClerkWebhookEvent<DeletedUserData>;

export type MapEventFunctionPayload =
  | OrganizationMembershipEvent
  | SessionEvent
  | UserCreatedEvent
  | UserDeletedEvent;

export const clerkEventTypeMap = {
  ORGANIZATION_MEMBERSHIP_CREATED: 'organizationMembership.created',
  ORGANIZATION_MEMBERSHIP_DELETED: 'organizationMembership.deleted',
  ORGANIZATION_MEMBERSHIP_UPDATED: 'organizationMembership.updated',
  SESSION_CREATED: 'session.created',
  SESSION_ENDED: 'session.ended',
  SESSION_REMOVED: 'session.removed',
  SESSION_REVOKED: 'session.revoked',
  SESSION_PENDING: 'session.pending',
  USER_CREATED: 'user.created',
  USER_UPDATED: 'user.updated',
  USER_DELETED: 'user.deleted'
} as const;

export type ClerkEventKey =
  (typeof clerkEventTypeMap)[keyof typeof clerkEventTypeMap];

export const clerkEventCategory = [
  'USER_CREATED',
  'USER_UPDATED',
  'USER_DELETED',
  'SESSION_CREATED',
  'SESSION_REMOVED',
  'SESSION_STATE_CHANGES',
  'ORGANIZATION_MEMBERSHIP'
] as const;

export type clerkEventCategoryKey = (typeof clerkEventCategory)[number];

export const CLERK_EVENTS_CATEGORY_MAP: Record<
  clerkEventCategoryKey,
  ClerkEventKey[]
> = {
  USER_CREATED: ['user.created'],
  USER_UPDATED: ['user.updated'],
  USER_DELETED: ['user.deleted'],
  SESSION_CREATED: ['session.created'],
  SESSION_REMOVED: ['session.removed'],
  SESSION_STATE_CHANGES: [
    'session.removed',
    'session.revoked',
    'session.pending'
  ],
  ORGANIZATION_MEMBERSHIP: [
    'organizationMembership.created',
    'organizationMembership.deleted',
    'organizationMembership.updated'
  ]
} as const;
