export class CustomError extends Error {
  status?: number;
  origin?: string;
  responseMessage?: string;

  constructor(
    message: string,
    options?: {
      status?: number;
      origin?: string;
      responseMessage?: string;
    }
  ) {
    super(message);
    this.name = this.constructor.name;
    this.status = options?.status;
    this.origin = options?.origin;
    this.responseMessage = options?.responseMessage;

    // Ensures proper inheritance in TypeScript
    Object.setPrototypeOf(this, CustomError.prototype);
  }
}
