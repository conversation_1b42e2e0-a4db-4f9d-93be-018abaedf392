import * as functions from 'firebase-functions/v2';
import { AuthData } from 'firebase-functions/lib/common/providers/https';
import * as admin from 'firebase-admin';

import log from '@logger';
import { EndpointEntity } from '@globalTypes/ndrBenchmarkTypes';

import {
  getLabelAssignments,
  BenchmarkFirestoreWriter,
  getLabels
} from '@controller/src/firestore';
import { LabelService } from '@controller/src/services/labels/LabelService';
import { runBenchmarks } from '@controller/src/benchmark';
import { BigQueryEntitiesControl } from '@controller/src/benchmark/controls';
import { Benchmark } from '@controller/src/benchmark/types/benchmarkTypes';
import { Global } from '@controller/src/config';

import { CreateConditionalLabelRequest } from '../types';
import { HTTP_FUNCTION_OPTIONS } from '../constants';
import { getEntityById, isOrganizationAdmin, validateLabel } from '../utils';

export const calculateAndAssignLabel = functions.https.onCall(
  HTTP_FUNCTION_OPTIONS,
  handler
);

export async function handler(request: {
  data: CreateConditionalLabelRequest;
  auth?: AuthData;
}) {
  try {
    const { label, organizationId, entityId, dryRun } =
      request.data as CreateConditionalLabelRequest;

    const logger = log.child({
      context: 'calculateAndAssignLabel',
      labelId: label.id,
      organizationId,
      entityId,
      dryRun
    });
    Global.organizationId = organizationId;

    const db = admin.firestore();

    const userId = request.auth?.uid || '';

    logger.info('Verifying user permissions', { userId });

    const isOrgAdmin = await isOrganizationAdmin(organizationId, userId, db);

    if (!isOrgAdmin) {
      logger.warn('Permission denied for non-admin user', {
        userId
      });

      return {
        error: 'permission-denied, Only organization admins can sign operations'
      };
    }

    validateLabel(label);

    const labelContext = {
      labels: [label],
      entities: [] as EndpointEntity[]
    };

    if (entityId) {
      log.info(
        'message payload contains entityId, getting entity from firestore'
      );

      const entity = await getEntityById(organizationId, entityId, db);

      labelContext.entities.push(entity);
    } else {
      const controlsToExecute = [
        new BigQueryEntitiesControl('entities/entities.sql')
      ];

      const benchmark = await runBenchmarks(controlsToExecute, organizationId);

      labelContext.entities.push(...benchmark.entities);
    }

    if (dryRun) {
      const labelService = new LabelService();
      const result = await labelService.evaluateConditionalLabels(
        labelContext.labels,
        labelContext.entities
      );

      return result.labelAssignments
        .filter((as) => as.labelIds.includes(label.id))
        .map((as) => as.entityId);
    }

    const fireStoreLabels = await getLabels(organizationId);
    const fireStoreLabelAssignments = await getLabelAssignments(organizationId);

    const labelService = new LabelService();

    const result = await labelService.handle({
      existingLabels: fireStoreLabels,
      entities: labelContext.entities,
      existingLabelAssignments: fireStoreLabelAssignments
    });

    const labels = result.createdLabels.map((label) => ({
      ...label,
      userId
    }));

    const benchmark = {
      labels,
      labelAssignments: result.labelAssignments,
      entities: [],
      issues: [],
      trafficPatterns: [],
      notifications: [],
      stats: [],
      detections: [],
      aiDetections: [],
      securityGroups: []
    } as Benchmark;

    await BenchmarkFirestoreWriter.setBenchmark(benchmark, organizationId);

    return { data: { success: true } };
  } catch (error: any) {
    log.error('Error calculating and assigning label', { error });

    return { error: error.message };
  }
}
