import * as admin from 'firebase-admin';

import { createGenericDocumentTrigger } from './triggers';
import {
  LABEL_ASSIGNMENTS_DOCUMENT_PATH,
  LABEL_DOCUMENT_PATH,
  DETECTION_CONTROL_DOCUMENT_PATH
} from './constants';
import { calculateAndAssignLabel } from './labels/createConditionalLabel';
import { userActivity } from './clerk';

import { initLogger } from '@logger';
import { ASSETS_TYPES } from '@activityLog/types';
import { BIG_QUERY_TABLES } from '@big-query-client/index';
import { onDetectionControlWritten } from './triggers/onDetectionControlWritten';

if (!admin.apps.length) {
  admin.initializeApp(); // ✅ safely run once
}

initLogger({
  serviceName: 'trigger-functions',
  environment: process.env.GCLOUD_PROJECT || 'port0-dev2',
  level: process.env.LOG_LEVEL || 'debug'
});

// const onLabelsChange = createGenericDocumentTrigger({
//   documentPath: LABEL_DOCUMENT_PATH,
//   tableName: BIG_QUERY_TABLES.RAW_LABELS,
//   assetChanged: ASSETS_TYPES.LABELS
// });

// const onLabelsAssignmentsChange = createGenericDocumentTrigger({
//   documentPath: LABEL_ASSIGNMENTS_DOCUMENT_PATH,
//   tableName: BIG_QUERY_TABLES.RAW_LABEL_ASSIGNMENTS,
//   assetChanged: ASSETS_TYPES.ENTITY_LABEL
// });

const onDetectionControlChange = createGenericDocumentTrigger({
  documentPath: DETECTION_CONTROL_DOCUMENT_PATH,
  tableName: null,
  assetChanged: ASSETS_TYPES.CONTROLS
});

// listeners to collections --> self invoked
// exports.onLabelsChange = onLabelsChange;
// exports.onLabelsAssignmentsChange = onLabelsAssignmentsChange;
exports.onDetectionControlChange = onDetectionControlChange;

// cloud function --> triggerd by user sending a request
exports.calculateAndAssignLabel = calculateAndAssignLabel;

// clerk webhook functions
exports.userActivity = userActivity;

exports.onDetectionControlWritten = onDetectionControlWritten;
