import { <PERSON><PERSON>and<PERSON> } from './EventHandler';
import {
  ClerkWebhookEvent,
  OrganizationMembershipEvent,
  CLERK_EVENTS_CATEGORY_MAP,
  clerkEventTypeMap
} from '../../types';
import { AuditLog, EVENT_TYPES, ASSETS_TYPES } from '@activityLog/types';

// User Invitations
// Event: organizationMembership.created / deleted / updated
export class OrgMembershipHandler implements EventHandler {
  mapEvent(event: OrganizationMembershipEvent): AuditLog {
    const eventType =
      event.type === clerkEventTypeMap.ORGANIZATION_MEMBERSHIP_CREATED
        ? EVENT_TYPES.CREATION
        : event.type === clerkEventTypeMap.ORGANIZATION_MEMBERSHIP_DELETED
          ? EVENT_TYPES.DELETION
          : EVENT_TYPES.MODIFICATION;

    return {
      user: {
        id: event.data.public_user_data.user_id,
        ip: event.event_attributes.http_request.client_ip
      },
      eventType,
      asset: ASSETS_TYPES.USER_MANAGEMENT,
      additionalData: {
        eventData: event.data,
        requestMetadata: event.event_attributes
      }
    };
  }
  canMapEvent(event: ClerkWebhookEvent<any>): boolean {
    return CLERK_EVENTS_CATEGORY_MAP.ORGANIZATION_MEMBERSHIP.includes(
      event.type
    );
  }
}
