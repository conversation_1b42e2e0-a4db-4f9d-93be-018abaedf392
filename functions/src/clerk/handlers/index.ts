import { EventHandler } from './EventHandler';
import {
  UserCreated<PERSON>andler,
  UserUpdatedHandler,
  UserDeletedHandler
} from './UserEventHandlers';
import { OrgMembershipHandler } from './OrgMembershipHandlers';
import {
  SessionCreatedHandler,
  SessionUpdatedHandler,
  SessionRemovedHandler
} from './SessionEventHandlers';
import { ClerkWebhookEvent } from '../../types';

const handlers: EventHandler[] = [
  new UserCreatedHandler(),
  new UserUpdatedHandler(),
  new UserDeletedHandler(),
  new SessionUpdatedHandler(),
  new SessionRemovedHandler(),
  new SessionCreatedHandler(),
  new OrgMembershipHandler()
];

export const getHandler = (rawEvent: ClerkWebhookEvent<any>): EventHandler => {
  const handler = handlers.find((h) => h.canMapEvent(rawEvent));

  if (!handler) {
    const error = new Error(`No handler found for event: ${rawEvent.type}`);
    (error as any).status = 400;
    (error as any).origin = 'getHandler';
    throw error;
  }

  return handler;
};
