import { <PERSON>Hand<PERSON> } from './EventHandler';
import {
  ClerkWebhookEvent,
  SessionEvent,
  CLERK_EVENTS_CATEGORY_MAP
} from '../../types';
import { AuditLog, EVENT_TYPES, ASSETS_TYPES } from '@activityLog/types';

// User Sessions
// Event: session.created
export class SessionCreated<PERSON>and<PERSON> implements EventHandler {
  mapEvent(event: SessionEvent): AuditLog {
    return {
      user: {
        id: event.data.user_id,
        ip: event.event_attributes.http_request.client_ip
      },
      eventType: EVENT_TYPES.CREATION,
      asset: ASSETS_TYPES.USERS_SESSIONS,
      additionalData: {
        eventData: event.data,
        requestMetadata: event.event_attributes
      }
    };
  }
  canMapEvent(event: ClerkWebhookEvent<any>): boolean {
    return CLERK_EVENTS_CATEGORY_MAP.SESSION_CREATED.includes(event.type);
  }
}

// Event: session.removed
export class SessionRemoved<PERSON><PERSON><PERSON> implements EventHandler {
  mapEvent(event: SessionEvent): AuditLog {
    return {
      user: {
        id: event.data.user_id,
        ip: event.event_attributes.http_request.client_ip
      },
      eventType: EVENT_TYPES.DELETION,
      asset: ASSETS_TYPES.USERS_SESSIONS,
      additionalData: {
        eventData: event.data,
        requestMetadata: event.event_attributes
      }
    };
  }
  canMapEvent(event: ClerkWebhookEvent<any>): boolean {
    return CLERK_EVENTS_CATEGORY_MAP.SESSION_REMOVED.includes(event.type);
  }
}

// Event: session.ended / session.pending / session.revoked
export class SessionUpdatedHandler implements EventHandler {
  mapEvent(event: SessionEvent): AuditLog {
    return {
      user: {
        id: event.data.user_id,
        ip: event.event_attributes.http_request.client_ip
      },
      eventType: EVENT_TYPES.MODIFICATION,
      asset: ASSETS_TYPES.USERS_SESSIONS,
      additionalData: {
        eventData: event.data,
        requestMetadata: event.event_attributes
      }
    };
  }
  canMapEvent(event: ClerkWebhookEvent<any>): boolean {
    return CLERK_EVENTS_CATEGORY_MAP.SESSION_STATE_CHANGES.includes(event.type);
  }
}
