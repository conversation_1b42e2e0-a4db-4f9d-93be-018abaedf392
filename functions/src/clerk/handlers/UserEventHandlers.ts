import { <PERSON>Handler } from './EventHandler';
import {
  ClerkWebhookEvent,
  CLERK_EVENTS_CATEGORY_MAP,
  UserCreatedEvent,
  UserDeletedEvent
} from '../../types';
import { AuditLog, EVENT_TYPES, ASSETS_TYPES } from '@activityLog/types';

// User Managment
// Event: user.created
export class UserCreatedHand<PERSON> implements EventHandler {
  mapEvent(event: UserCreatedEvent): AuditLog {
    return {
      user: {
        id: event.data.id,
        ip: event.event_attributes.http_request.client_ip
      },
      eventType: EVENT_TYPES.CREATION,
      asset: ASSETS_TYPES.USER_MANAGEMENT,
      additionalData: {
        eventData: event.data,
        requestMetadata: event.event_attributes
      }
    };
  }
  canMapEvent(event: ClerkWebhookEvent<any>): boolean {
    return CLERK_EVENTS_CATEGORY_MAP.USER_CREATED.includes(event.type);
  }
}

// Event: user.deleted
export class UserDeleted<PERSON>and<PERSON> implements EventHandler {
  mapEvent(event: UserDeletedEvent): AuditLog {
    return {
      user: {
        id: event.data.id,
        ip: event.event_attributes.http_request.client_ip
      },
      eventType: EVENT_TYPES.DELETION,
      asset: ASSETS_TYPES.USER_MANAGEMENT,
      additionalData: {
        eventData: event.data,
        requestMetadata: event.event_attributes
      }
    };
  }
  canMapEvent(event: ClerkWebhookEvent<any>): boolean {
    return CLERK_EVENTS_CATEGORY_MAP.USER_DELETED.includes(event.type);
  }
}

// Event: user.updated
export class UserUpdatedHandler implements EventHandler {
  mapEvent(event: UserCreatedEvent): AuditLog {
    return {
      user: {
        id: event.data.id,
        ip: event.event_attributes.http_request.client_ip
      },
      eventType: EVENT_TYPES.MODIFICATION,
      asset: ASSETS_TYPES.USER_MANAGEMENT,
      additionalData: {
        eventData: event.data,
        requestMetadata: event.event_attributes
      }
    };
  }
  canMapEvent(event: ClerkWebhookEvent<any>): boolean {
    return CLERK_EVENTS_CATEGORY_MAP.USER_UPDATED.includes(event.type);
  }
}
