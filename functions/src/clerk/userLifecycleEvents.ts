import * as functions from 'firebase-functions/v2';

import FirestoreAuditLogger from '@activityLog/index';
import log from '@logger';

import { ClerkWebhookEvent } from '../types';
import {
  validateClerkRequest,
  getClerkWebhookSecretName,
  getOrganizationId
} from '../utils';
import { getHandler } from './handlers';
import {
  WEBHOOK_NAME_USER_ACTIVITY,
  CLERK_SECRET_KEY,
  HTTP_FUNCTION_OPTIONS
} from '../constants';
import { CustomError } from '../errors';

export const userActivity = functions.https.onRequest(
  {
    ...HTTP_FUNCTION_OPTIONS,
    secrets: [
      ...HTTP_FUNCTION_OPTIONS.secrets,
      getClerkWebhookSecretName(WEBHOOK_NAME_USER_ACTIVITY),
      CLERK_SECRET_KEY
    ]
  },
  async (request, response) => {
    try {
      const isValid = await validateClerkRequest(
        request,
        WEBHOOK_NAME_USER_ACTIVITY
      );

      if (!isValid) {
        response.status(401).send('Unauthorized');
        return;
      }

      const { rawEvent, orgId } = await logUserActivity(request.body);

      log.info(`Event processed successfully.`, {
        eventType: rawEvent.type,
        orgId
      });

      response
        .status(200)
        .send(`Event ${rawEvent.type} processed successfully.`);

      return;
    } catch (error) {
      // Check if it's a CustomError
      if (error instanceof CustomError) {
        const { origin, message, status = 500, responseMessage } = error;

        log.error(`Clerk webhook error: ${message}`, { origin });
        response
          .status(status)
          .send(responseMessage || 'Error processing request');
      } else {
        // Handle regular errors
        const message = error instanceof Error ? error.message : String(error);
        log.error(`Clerk webhook unhandled error: ${message}`);
      }

      return;
    }
  }
);

export const logUserActivity = async (rawEvent: ClerkWebhookEvent<any>) => {
  const handler = getHandler(rawEvent);
  const auditLog = handler.mapEvent(rawEvent);
  const orgId = await getOrganizationId(rawEvent, auditLog);
  await FirestoreAuditLogger.writeSingle(orgId, auditLog);

  return {
    rawEvent,
    orgId
  };
};
