import * as admin from 'firebase-admin';
import { isString, isBoolean, isNumber } from 'lodash';
import { HttpsError } from 'firebase-functions/v2/https';
import { Webhook } from 'svix';
import { Request } from 'firebase-functions/v2/https';
import { createClerkClient } from '@clerk/backend';

import log from '@logger';
import {
  EndpointEntity,
  MatchTypes,
  UserLabel
} from '@globalTypes/ndrBenchmarkTypes';
import { ClerkWebhookEvent } from './types';
import { WEBHOOK_SECRET_KEY, CLERK_SECRET_KEY } from './constants';
import { AuditLog } from '@controller/src/activityLog/types';
import { CustomError } from './errors';

export const clerkClient = createClerkClient({
  secretKey: process.env[CLERK_SECRET_KEY]
});

export const validateLabel = (label: UserLabel) => {
  if (!label) {
    throw new HttpsError('invalid-argument', 'label is required.');
  }

  if (label.type !== 'user') {
    throw new HttpsError(
      'invalid-argument',
      'label must be a conditional label.'
    );
  }

  if (label?.conditions) {
    if (!Array.isArray(label.conditions)) {
      throw new HttpsError(
        'invalid-argument',
        'condition values must be an array.'
      );
    }
  }

  const isWrongType = label?.conditions?.some(
    (condition) => !Object.values(MatchTypes).includes(condition.matchType)
  );

  if (isWrongType) {
    throw new HttpsError(
      'invalid-argument',
      'condition type must be one of the following: ' +
        Object.values(MatchTypes).join(', ')
    );
  }

  if (!label.conditions?.length) {
    throw new HttpsError(
      'invalid-argument',
      'condition values must be an array with at least one element.'
    );
  }

  if (
    !label.conditions.every(
      ({ value }) => isString(value) || isBoolean(value) || isNumber(value)
    )
  ) {
    throw new HttpsError(
      'invalid-argument',
      'condition value must be primitive type.'
    );
  }
};

export async function isOrganizationAdmin(
  organizationId: string,
  userId: string,
  db: admin.firestore.Firestore
): Promise<boolean> {
  const adminUser = await db
    .collection('organizations')
    .doc(organizationId)
    .collection('admins')
    .doc(userId)
    .get();

  return adminUser.exists;
}

export const getEntityById = async (
  organizationId: string,
  entityId: string,
  db: admin.firestore.Firestore
): Promise<EndpointEntity> => {
  const entity = await db
    .collection('organizations')
    .doc(organizationId)
    .collection('dashboards')
    .doc('NDR')
    .collection('entities')
    .doc(entityId)
    .get();

  if (!entity.exists) {
    throw new HttpsError('not-found', 'Entity not found');
  }

  return entity.data() as EndpointEntity;
};

export const getClerkWebhookSecretName = (webhook: string): string => {
  return `${WEBHOOK_SECRET_KEY}-${webhook}`;
};

export const getClerkWebhookSecret = async (
  webhook: string
): Promise<string | undefined> => {
  return process.env[getClerkWebhookSecretName(webhook)];
};

export const validateClerkRequest = async (
  request: Request,
  webhookName: string
): Promise<boolean> => {
  const secret = await getClerkWebhookSecret(webhookName);

  if (!secret) {
    log.info(`No secret found for webhook ${webhookName}`, { webhookName });
    return false;
  }

  const webhook = new Webhook(secret);
  const payload = JSON.stringify(request.body);

  try {
    // Convert headers to compatible format
    const headers: Record<string, string> = {};
    for (const [key, value] of Object.entries(request.headers)) {
      if (value) {
        headers[key] = Array.isArray(value) ? value[0] : value;
      }
    }

    webhook.verify(payload, headers);
  } catch (error) {
    log.error(`Verification failed`, error);
    return false;
  }

  return true;
};
export async function getLastActiveOrgId(userId: string): Promise<string> {
  const { data: sessionData } = await clerkClient.sessions.getSessionList({
    userId
  });
  if (!sessionData.length) {
    throw new CustomError('No active sessions found for user', {
      origin: 'getLastActiveOrgId',
      status: 400,
      responseMessage: 'No active sessions found'
    });
  }

  const latestSession = getLatestSessionFromData(sessionData);
  if (!latestSession || !latestSession.lastActiveOrganizationId) {
    throw new CustomError('No active organization found in user session', {
      origin: 'getLastActiveOrgId',
      status: 400,
      responseMessage: 'No active organization found'
    });
  }
  return latestSession.lastActiveOrganizationId;
}

export function getLatestSessionFromData(sessionData: any[]): any {
  if (!sessionData.length) return null;

  // Sort sessions by last activity (descending)
  const sortedSessions = [...sessionData].sort(
    (a, b) =>
      new Date(b.lastActiveAt).getTime() - new Date(a.lastActiveAt).getTime()
  );

  return sortedSessions[0];
}

export const getOrganizationId = async (
  rawEvent: ClerkWebhookEvent<any>,
  auditLog: AuditLog
): Promise<string> => {
  const orgId =
    rawEvent.data.organization?.id ??
    rawEvent.data.last_active_organization_id ??
    null;

  if (orgId) return orgId;

  const userId = auditLog.user?.id;

  if (!userId) {
    throw new CustomError(`Missing user ID for audit log: ${rawEvent.type}`, {
      origin: 'getOrganizationIdByRawEvent',
      status: 400,
      responseMessage: 'Bad Request: Missing user ID'
    });
  }

  const activeOrgId = await getLastActiveOrgId(userId);
  if (!activeOrgId) {
    throw new CustomError(
      `Missing Organization ID for audit log: ${rawEvent.type}`,
      {
        origin: 'getOrganizationIdByRawEvent',
        status: 400,
        responseMessage: 'Bad Request: Missing organization ID'
      }
    );
  }

  return activeOrgId;
};
