import { DocumentOptions } from 'firebase-functions/firestore';

const projectId = process.env.GCLOUD_PROJECT || 'port0-dev2';
const serviceAccount = `steampipe-benchmark-sa@${projectId}.iam.gserviceaccount.com`;

export const LABEL_DOCUMENT_PATH =
  'organizations/{orgId}/dashboards/NDR/labels/{labelId}';

export const LABEL_ASSIGNMENTS_DOCUMENT_PATH =
  'organizations/{orgId}/dashboards/NDR/labelAssignments/{docId}';

export const DETECTION_CONTROL_DOCUMENT_PATH =
  'organizations/{orgId}/dashboards/NDR/detectionControls/{detectionControlId}';

export const COMMON_CONFIG: Partial<DocumentOptions> = {
  serviceAccount,
  region: 'us-central1',
  secrets: ['CORALOGIX_URL', 'CORALOGIX_PRIVATE_KEY'],
  memory: '1GiB'
};

export const HTTP_FUNCTION_OPTIONS = {
  minInstances: 1,
  maxInstances: 50,
  memory: '4GiB' as any,
  serviceAccount,
  secrets: ['CORALOGIX_URL', 'CORALOGIX_PRIVATE_KEY'],
  runtime: 'nodejs22'
};

export const CLERK_SECRET_KEY = 'CLERK_SECRET_KEY';
export const WEBHOOK_SECRET_KEY = 'CLERK_SIGNATURE';
export const WEBHOOK_NAME_USER_ACTIVITY = 'userActivity';
