import log from '@logger';
import * as functions from 'firebase-functions/v2';
import {
  getDetectionControls,
  updateDetectionCategories
} from '../../../benchmark/controller/src/firestore';
import { getDetectionCategories } from '../../../benchmark/controller/src/firestore/getDetections';
import { fetchCategoriesFromDetectionControls } from '../../../benchmark/controller/src/utils/detectionUtils';
import { COMMON_CONFIG, DETECTION_CONTROL_DOCUMENT_PATH } from '../constants';

export const onDetectionControlWritten = functions.firestore.onDocumentWritten(
  {
    document: DETECTION_CONTROL_DOCUMENT_PATH,
    ...COMMON_CONFIG
  },
  async (event) => {
    const logger = log.child({
      method: 'onDetectionControlWritten',
      event: event.data
    });
    if (!event.data) {
      logger.info('No data found');

      return;
    }

    if (!event.params) {
      logger.info('No params found');

      return;
    }

    const { orgId } = event.params;

    try {
      const beforeDocument = event.data.before.data();
      const afterDocument = event.data.after.data();

      if (
        !afterDocument?.category ||
        afterDocument?.category === beforeDocument?.category
      ) {
        return;
      }

      const detectionControls = await getDetectionControls(orgId);
      const existingCategories = await getDetectionCategories(orgId);
      const categoriesFromControls =
        fetchCategoriesFromDetectionControls(detectionControls);

      // Merge both arrays and remove duplicates using Set
      const uniqueCategories = Array.from(
        new Set([...existingCategories, ...categoriesFromControls, 'AI'])
      );

      await updateDetectionCategories(orgId, uniqueCategories);
    } catch (error) {
      logger.error('Error handling detection control written', {
        error,
        orgId: event.params.orgId ?? null,
        detectionControlId: event.params.detectionControlId ?? null
      });
    }
  }
);
