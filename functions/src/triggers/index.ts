import * as functions from 'firebase-functions/v2';

import { getDocumentByOperation } from './utils';
import { COMMON_CONFIG } from '../constants';
import { TriggerFunctionConfig } from '../types';

import log from '@logger';
import { ASSETS_TYPES, AuditLog, EVENT_TYPES } from '@activityLog/types';
import FirestoreAuditLogger from '@activityLog/index';
import { DatabaseSchemas, getBigQueryClient } from '@big-query-client/index';
import { BIG_QUERY_TABLE_SCHEMAS } from '@big-query-client/definitions/databaseSchemas';

export const createGenericDocumentTrigger = (config: TriggerFunctionConfig) => {
  return functions.firestore.onDocumentWritten(
    {
      document: config.documentPath,
      ...COMMON_CONFIG
    },
    async (event) => {
      try {
        if (!event.data) {
          log.info('No data found');
          return;
        }

        if (!event.params) {
          log.info('No params found');
          return;
        }

        const { orgId } = event.params;

        const beforeDocument = event.data.before.data();
        const afterDocument = event.data.after.data();
        let asset = config.assetChanged;
        log.debug(`Change Detected for asset ${asset}.`, {
          beforeDocument,
          afterDocument,
          orgId,
          asset
        });

        const { operationType, userId, ...documentToInsert } =
          getDocumentByOperation(orgId, beforeDocument, afterDocument, asset);

        // Check if this is a modification with changed conditions
        if (
          operationType === EVENT_TYPES.MODIFICATION &&
          beforeDocument?.conditions &&
          afterDocument?.conditions &&
          JSON.stringify(beforeDocument.conditions) !==
            JSON.stringify(afterDocument.conditions)
        ) {
          asset = ASSETS_TYPES.LABEL_CONDITION;
          log.info(
            `Condition change detected, updating asset type to ${asset}`,
            { orgId }
          );
        }

        if (!documentToInsert) {
          log.warn('No query found', { orgId });
          return;
        }
        if (config.tableName) {
          const tableName = config.tableName;

          const bigQueryClient = await getBigQueryClient({
            datasetId: orgId,
            tableSchemas: {
              [tableName]: BIG_QUERY_TABLE_SCHEMAS[tableName]
            } as DatabaseSchemas,
            initialize: false
          });

          await bigQueryClient.parallelWrite(tableName, [documentToInsert]);
        }
        const auditLog: AuditLog = {
          eventType: operationType,
          asset: asset,
          additionalData: {
            beforeDocument: beforeDocument || {},
            afterDocument: afterDocument || {}
          },
          user: { id: userId }
        };
        await FirestoreAuditLogger.writeSingle(orgId, auditLog);
        return;
      } catch (error) {
        log.error('Error in document trigger', {
          error,
          orgId: event.params?.orgId,
          asset: config.assetChanged
        });

        return;
      }
    }
  );
};
