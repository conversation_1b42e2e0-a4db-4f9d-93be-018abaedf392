import { TableName } from '../writers/bigquery/schemas';

import log from '@logger';
import { AssetsTypes, EVENT_TYPES, EventTypes } from '@activityLog/types';

export const getOperationType = (
  beforeDocument: any,
  afterDocument: any
): EventTypes => {
  if (!beforeDocument && afterDocument) {
    return EVENT_TYPES.CREATION;
  }

  if (beforeDocument && !afterDocument) {
    return EVENT_TYPES.DELETION;
  }

  return EVENT_TYPES.MODIFICATION;
};

export const getDocumentByOperation = (
  orgId: string,
  beforeDocument: any,
  afterDocument: any,
  asset: AssetsTypes
): any => {
  const operationType = getOperationType(beforeDocument, afterDocument);

  const baseDocument = {
    created_at: new Date().toISOString(),
    action: operationType
  };

  switch (operationType) {
    case EVENT_TYPES.CREATION:
      log.debug(`Create operation detected for ${asset}`, { orgId });

      const { createdAt, updatedAt, conditions, icon, color, userId, ...data } =
        afterDocument;

      return {
        operationType,
        userId,
        ...data,
        ...baseDocument
      };

    case EVENT_TYPES.MODIFICATION: {
      log.debug(`Update operation detected for ${asset}`, { orgId });

      const { updatedAt, createdAt, conditions, icon, color, userId, ...data } =
        afterDocument;

      return {
        operationType,
        userId,
        ...data,
        ...baseDocument
      };
    }

    case EVENT_TYPES.DELETION: {
      log.debug(`Delete operation detected for ${asset}`, { orgId });

      const { createdAt, updatedAt, conditions, icon, color, userId, ...data } =
        beforeDocument;

      return {
        operationType,
        userId,
        ...data,
        ...baseDocument
      };
    }

    default: {
      log.warn('Unknown operation type', { orgId });
      throw new Error('No document to insert');
    }
  }
};
