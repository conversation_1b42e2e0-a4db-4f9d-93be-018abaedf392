import { MatchTypes, UserLabel } from '@globalTypes/ndrBenchmarkTypes';
import { validateLabel } from '../utils';

describe('validateLabel', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should throw when label is null or undefined', () => {
    expect(() => validateLabel(null as any)).toThrow('label is required.');
  });

  it('should throw when label type is not "user"', () => {
    const invalidTypeLabel = {
      id: '2',
      type: 'static',
      key: 'Invalid Type Label',
      values: ['Invalid Type Label']
    };

    expect(() => validateLabel(invalidTypeLabel as UserLabel)).toThrow(
      'label must be a conditional label.'
    );
  });

  it('should throw when condition has invalid matchType', () => {
    const invalidMatchTypeLabel: UserLabel = {
      id: '3',
      type: 'user',
      key: 'Invalid MatchType Label',
      values: ['Invalid MatchType Label'],
      conditions: [
        {
          fieldName: 'field',
          matchType: 'invalidType' as any,
          value: 'test',
          include: true
        }
      ]
    };

    expect(() => validateLabel(invalidMatchTypeLabel)).toThrow();
  });

  it('should throw when conditions is not an array', () => {
    const notArrayConditionsLabel: UserLabel = {
      id: '4',
      type: 'user',
      key: 'Not Array Conditions Label',
      values: ['Not Array Conditions Label'],
      conditions: 'not an array' as any
    };

    expect(() => validateLabel(notArrayConditionsLabel)).toThrow(
      'condition values must be an array.'
    );
  });

  it('should throw when conditions array is empty', () => {
    const emptyConditionsLabel: UserLabel = {
      id: '5',
      type: 'user',
      key: 'Empty Conditions Label',
      values: ['Empty Conditions Label'],
      conditions: []
    };

    expect(() => validateLabel(emptyConditionsLabel)).toThrow(
      'condition values must be an array with at least one element.'
    );
  });

  it('should throw when condition value is not string, boolean, or number', () => {
    const invalidValueTypeLabel: UserLabel = {
      id: '6',
      type: 'user',
      key: 'Invalid Value Type Label',
      values: ['Invalid Value Type Label'],
      conditions: [
        {
          fieldName: 'field',
          matchType: MatchTypes.EQUALS,
          value: { invalid: 'object' } as any,
          include: true
        }
      ]
    };

    expect(() => validateLabel(invalidValueTypeLabel)).toThrow(
      'condition value must be primitive type.'
    );
  });

  it('should accept all valid value types (string, boolean, number)', () => {
    const validTypesLabel: UserLabel = {
      id: '7',
      type: 'user',
      key: 'Valid Types Label',
      values: ['Valid Types Label'],
      conditions: [
        {
          fieldName: 'stringField',
          matchType: MatchTypes.EQUALS,
          value: 'string value',
          include: true
        },
        {
          fieldName: 'booleanField',
          matchType: MatchTypes.CONTAINS,
          value: true,
          include: false
        },
        {
          fieldName: 'numberField',
          matchType: MatchTypes.STARTS_WITH,
          value: 42,
          include: true
        }
      ]
    };

    expect(() => validateLabel(validTypesLabel)).not.toThrow();
  });

  it('should throw when conditions is undefined', () => {
    const noConditionsLabel: UserLabel = {
      id: '8',
      type: 'user',
      key: 'No Conditions Label',
      values: ['No Conditions Label']
      // conditions empty
    };

    expect(() => validateLabel(noConditionsLabel)).toThrow(
      'condition values must be an array with at least one element.'
    );
  });
});
