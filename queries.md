## For each ip count of agents
```
SELECT 
    local_addr, 
    COUNT(DISTINCT provider_information ->> 'agent') AS unique_agents
FROM 
    port0_traffic.traffic
LEFT JOIN port0_sentinel.agent as agent
    ON agent.uuid = provider_information ->> 'agent'
GROUP BY 
    local_addr
ORDER BY 
    unique_agents DESC;

SELECT 
    traffic.local_addr, 
    COUNT(DISTINCT traffic.provider_information ->> 'agent') AS agent_count,
    STRING_AGG(DISTINCT traffic.provider_information ->> 'agent', ',') AS agent
FROM 
    port0_traffic.traffic as traffic
GROUP BY 
    traffic.local_addr
ORDER BY 
    agent_count desc;

```

## Find unrecognized entities
```
SELECT 
    count(distinct remote_addr) 
FROM port0_sentinel_traffic_patterns as traffic
WHERE 
    remote_provider_information = '{}'::JSONB AND
    is_private_ip(remote_addr::INET) 

SELECT 
    count(distinct remote_addr) 
FROM port0_sentinel_traffic_patterns as traffic
LEFT JOIN port0_sentinel.agent as agent
    ON agent.last_ip_to_mgmt = traffic.remote_addr
WHERE 
    remote_provider_information = '{}'::JSONB AND
    is_private_ip(remote_addr::INET) AND
    agent IS NOT NULL
```


## Port
```
SELECT 
    port, 
    provider_information ->> 'process' AS process,
    COUNT(*) AS count
FROM 
    port0_sentinel_traffic_patterns AS traffic
WHERE
    direction = 'ingress' AND
    session_count < 10
GROUP BY
    port,
    provider_information ->> 'process'
ORDER BY 
    count DESC;

SELECT 
    agent.computer_name, 
    COUNT(*) AS count
FROM 
    port0_sentinel_traffic_patterns AS traffic
JOIN port0_sentinel.agent as agent
ON provider_information ->> 'agent' = agent.uuid
WHERE session_count > 10
GROUP BY
    provider_information ->> 'agent', agent.computer_name
ORDER BY 
    count DESC;

SELECT 
    COUNT(*) AS count
FROM 
    port0_sentinel_traffic_patterns AS traffic
JOIN port0_sentinel.agent as agent
ON provider_information ->> 'agent' = agent.uuid
WHERE session_count > 20

```

## Filter Windows
```
SELECT 
    port, 
    provider_information ->> 'process' AS process,
    COUNT(*) AS count
FROM 
    port0_sentinel_traffic_patterns AS traffic
WHERE
    NOT (
        port IN (88, 135, 139, 389, 5985) OR (port >= 49600 AND port <= 49699)
    )
GROUP BY
    port,
    provider_information ->> 'process'
ORDER BY 
    count DESC;

SELECT 
    port, 
    provider_information ->> 'process' AS process,
    session_count
FROM 
    port0_sentinel_traffic_patterns AS traffic
ORDER BY 
    session_count DESC;

```