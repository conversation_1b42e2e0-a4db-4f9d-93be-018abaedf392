{"name": "controller", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node --max-old-space-size=12288 -r ts-node/register src/index.ts", "mock": "ts-node src/mock/index.ts", "start:dev": "~/bin/infisical-run npm start", "prettier-check": "prettier --check \"**/*.ts\"", "prettier-write": "prettier --write \"**/*.ts\"", "build": "tsc && tsc-alias", "test": "jest", "prepare": "husky"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-ec2": "^3.783.0", "@elastic/ecs-winston-format": "^1.5.3", "@google-cloud/bigquery": "^7.9.2", "@google-cloud/vertexai": "^1.9.2", "@qualdesk/firestore-big-batch": "github:qualdesk/firestore-big-batch", "body-parser": "^1.20.2", "bunyan": "^1.8.15", "coralogix-logger": "^1.1.30", "coralogix-logger-bunyan": "^1.0.10", "dotenv": "^16.4.7", "express": "^4.19.2", "firebase": "^10.12.4", "firebase-admin": "^12.3.0", "firebase-functions": "^4.7.0", "googleapis": "^148.0.0", "handlebars": "^4.7.8", "ip": "^2.0.1", "ip-range-check": "^0.2.0", "ipaddr.js": "^2.2.0", "lodash": "^4.17.21", "math": "^0.0.3", "murmurhash3js": "^3.0.1", "node-sql-parser": "^5.3.10", "p-queue": "7.4.1", "p-retry": "6.2.1", "pg": "^8.13.1", "winston": "^3.17.0", "winston-elasticsearch": "^0.19.0", "zod": "^3.25.51"}, "devDependencies": {"@types/bunyan": "^1.8.11", "@types/express": "^4.17.21", "@types/ip": "^1.1.3", "@types/jest": "^29.5.0", "@types/lodash": "^4.17.14", "@types/murmurhash3js": "^3.0.7", "@types/node": "^20.14.12", "@types/pg": "^8.11.10", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.4.3", "prettier": "^3.5.3", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.4"}, "lint-staged": {"**/*.ts": ["prettier --write \"**/*.ts\"", "git add"]}}