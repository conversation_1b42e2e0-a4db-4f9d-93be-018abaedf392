import * as config from './config';
import log, { initLogger } from './logger';
import { getProjectID } from './utils/environment';
import { getBenchmarkControls } from './benchmark/consts';
import { runBenchmarksWithDependencies } from './benchmark';
import { syncPort0DetectionControls } from './utils/detectionUtils';

const serviceName = 'steampipe-benchmark';

async function main() {
  initLogger({
    serviceName,
    environment: getProjectID(),
    level: process.env.LOG_LEVEL || 'debug',
    organizationId: config.Global.organizationId
  });

  process.on('uncaughtException', function (err) {
    log.log('error', 'Fatal uncaught exception', err, () => {
      process.exit(1);
    });
  });

  log.info(
    `Initializing benchmark service for organization ID: ${config.Global.organizationId}`
  );

  try {
    // await syncPort0DetectionControls(config.Global.organizationId);

    const benchmarkControls = await getBenchmarkControls(
      config.Global.organizationId
    );

    // Use the new dependency-aware execution system
    // This will execute controls in groups, sync to firestore immediately,
    // and remove completed controls from memory
    await runBenchmarksWithDependencies(
      benchmarkControls,
      config.Global.organizationId
    );

    log.info('All benchmarks completed successfully with dependency-aware execution');
  } catch (err) {
    log.error(`Error running benchmarks: ${(err as Error).message || ''}`);
  }

  // Wait for 3 seconds before exiting to ensure all logs are flushed
  await new Promise((resolve) => setTimeout(resolve, 3000));
  process.exit(0);
}

main();
