import {
  EndpointEntity,
  Label,
  LabelAssignment,
  UserLabel
} from '@globalTypes/ndrBenchmarkTypes';
import { BigQuerySQLControl } from '../../benchmark/controls';
import { createConditionalRules, labelingRules } from './LabelFactory';
import { PortLabelingRule } from './labelingRules/PortLabelingRule';
import { ProcessLabelingRule } from './labelingRules/ProcessLabelingRule';
import { TagRule } from './labelingRules/TagRule';
import { LabelProcessor } from './LabelProcessor';
import { EntitiesTraffic, EntitiesTrafficMap } from './types';

interface LabelHandlerParams {
  existingLabels: Label[];
  entities: EndpointEntity[];
  existingLabelAssignments: LabelAssignment[];
}

export class LabelService {
  async handle({
    existingLabels,
    entities,
    existingLabelAssignments
  }: LabelHandlerParams) {
    const conditionalLabels = existingLabels.filter(
      (label): label is UserLabel =>
        label.type === 'user' && !!label?.conditions
    );

    const labelProcessor = new LabelProcessor(
      existingLabels,
      existingLabelAssignments
    );

    const trafficPatternsControl = new BigQuerySQLControl(
      'entities/entities_traffic.sql'
    );

    const entitiesTraffic: EntitiesTraffic[] =
      (await trafficPatternsControl.execute()) as EntitiesTraffic[];
    const entitiesTrafficMap = entitiesTraffic.reduce((acc, item) => {
      return Object.assign(acc, { [item.entityId]: item });
    }, {}) as EntitiesTrafficMap;

    for (const entity of entities) {
      if (entity.common) {
        labelProcessor.addEntityOperation(
          entity.id,
          entity.common,
          labelingRules
        );
        if (entity.common.tags) {
          labelProcessor.addEntityOperation(entity.id, entity.common.tags, [
            new TagRule()
          ]);
        }
      }

      labelProcessor.addEntityOperation(
        entity.id,
        { ...entity, traffic: entitiesTrafficMap[entity.id] },
        createConditionalRules(conditionalLabels)
      );
    }

    for (const item of entitiesTraffic) {
      if (Array.isArray(item.ports) && item.ports.length) {
        labelProcessor.addEntityOperation(
          item.entityId,
          { ports: item.ports },
          [new PortLabelingRule()]
        );
      }
      if (Array.isArray(item.processes) && item.processes.length) {
        labelProcessor.addEntityOperation(
          item.entityId,
          { processes: item.processes },
          [new ProcessLabelingRule()]
        );
      }
    }

    return labelProcessor.process();
  }

  async evaluateConditionalLabels(
    labelsToEvaluate: Label[],
    entities: EndpointEntity[]
  ) {
    const unConditionalLabels = labelsToEvaluate.filter(
      (label): label is UserLabel => label.type === 'user' && !label?.conditions
    );

    if (unConditionalLabels.length) {
      throw new Error(
        `Method accepts only Labels with conditions` +
          ` label: ${unConditionalLabels[0].id} doesn't have conditions`
      );
    }

    const trafficPatternsControl = new BigQuerySQLControl(
      'entities/entities_traffic.sql'
    );

    const entitiesTraffic: EntitiesTraffic[] =
      (await trafficPatternsControl.execute()) as EntitiesTraffic[];
    const entitiesTrafficMap = entitiesTraffic.reduce((acc, item) => {
      return Object.assign(acc, { [item.entityId]: item });
    }, {}) as EntitiesTrafficMap;

    const labelProcessor = new LabelProcessor([], []);

    for (const entity of entities) {
      labelProcessor.addEntityOperation(
        entity.id,
        { ...entity, traffic: entitiesTrafficMap[entity.id] },
        createConditionalRules(labelsToEvaluate as UserLabel[])
      );
    }

    return labelProcessor.process();
  }
}
