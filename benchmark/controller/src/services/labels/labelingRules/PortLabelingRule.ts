import { BaseLabelingRule } from './LabelingRule';
import { Label } from '@globalTypes/ndrBenchmarkTypes';

type PortServiceMap = {
  serviceName: string;
  labelName: string;
};

export class PortLabelingRule<
  T extends { ports: number[] }
> extends BaseLabelingRule<T> {
  private matchedPorts: number[] = [];
  private readonly PORT_SERVICE_MAP: Record<number, PortServiceMap> = {
    3306: { serviceName: 'Database', labelName: 'MySQL' },
    1433: { serviceName: 'Database', labelName: 'MS SQL' },
    1434: { serviceName: 'Database', labelName: 'MS SQL' },
    5432: { serviceName: 'Database', labelName: 'PostgreSQL' },
    27017: { serviceName: 'Database', labelName: 'MongoDB' },
    6379: { serviceName: 'Database', labelName: 'Redis' },
    1521: { serviceName: 'Database', labelName: 'Oracle' },
    1830: { serviceName: 'Database', labelName: 'Oracle' },
    22: { serviceName: 'Service', labelName: 'SSH' },
    80: { serviceName: 'Server', labelName: 'Web Server' },
    443: { serviceName: 'Server', labelName: 'Web Server' },
    3128: { serviceName: 'Server', labelName: 'HTTP proxy server' },
    8080: { serviceName: 'Server', labelName: 'Web Server' },
    2525: { serviceName: 'Server', labelName: 'SMTP' },
    9200: { serviceName: 'Database', labelName: 'Elasticsearch' },
    9300: { serviceName: 'Database', labelName: 'Elasticsearch' },
    5601: { serviceName: 'Service', labelName: 'Kibana' }
  };

  prepare(context: T): void {
    this.matchedPorts = context.ports.filter(
      (port) => port in this.PORT_SERVICE_MAP
    );
  }

  matches(context: T): boolean {
    return !!this.matchedPorts.length;
  }

  getLabels(context: T): Label[] {
    return this.matchedPorts.map((port) => {
      const { labelName, serviceName } = this.PORT_SERVICE_MAP[port];
      const id = this.getId(`${serviceName}-${labelName}`);
      return {
        id,
        key: serviceName,
        values: [labelName],
        type: this.getLabelType()
      };
    });
  }

  getLabelType(): 'ai' {
    return 'ai';
  }
}
