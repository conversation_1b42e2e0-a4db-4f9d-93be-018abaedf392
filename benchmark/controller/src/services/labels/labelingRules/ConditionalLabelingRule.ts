import { BaseLabelingRule } from './LabelingRule';
import {
  UserLabel,
  Label,
  Condition,
  MatchType,
  MatchTypes,
  EndpointEntity,
  LabelCriteriaFields
} from '@globalTypes/ndrBenchmarkTypes';
import { isIpInCidr, isIpInRange } from '../../../utils/ip';
import { EntitiesTraffic } from '../types';

export class ConditionalLabelingRule<
  T extends EndpointEntity & { traffic?: EntitiesTraffic }
> extends BaseLabelingRule<T> {
  constructor(private label: UserLabel) {
    super();
  }

  matches(context: T): boolean {
    if (!this.label.conditions?.length) {
      return false;
    }

    return this.label.conditions.every((condition) => {
      const matches = this.matchFieldConditions(condition, context);
      return condition.include ? matches : !matches;
    });
  }

  private matchFieldConditions(condition: Condition, context: any): boolean {
    const { fieldName, matchType, value } = condition;
    const fieldValue = this.getEntityFieldValue(fieldName, context);

    if (Array.isArray(fieldValue)) {
      return fieldValue.some((v) => this.matchValue(v, matchType, value));
    }

    return this.matchValue(fieldValue, matchType, condition.value);
  }

  private matchValue(
    value: string,
    matchType: MatchType,
    conditionValue: any
  ): boolean {
    if (!value) return false;

    switch (matchType) {
      case MatchTypes.EQUALS:
        return value === conditionValue;
      case MatchTypes.CONTAINS:
        return value.includes(conditionValue);
      case MatchTypes.STARTS_WITH:
        return value.startsWith(conditionValue);
      case MatchTypes.ENDS_WITH:
        return value.endsWith(conditionValue);
      case MatchTypes.WILDCARD:
        return this.wildcardMatcher(value, conditionValue);
      case MatchTypes.CIDR:
        return isIpInCidr(value, conditionValue);
      case MatchTypes.IP_RANGE:
        return this.ipRangeMatcher(value, conditionValue);
      default:
        return false;
    }
  }

  getLabels(context: T): Label[] {
    return [this.label];
  }

  getLabelType(): 'user' {
    return 'user';
  }

  ipRangeMatcher(value: string, conditionValue: string): boolean {
    const [startIp, endIp] = conditionValue.split('-');
    return isIpInRange(value, startIp.trim(), endIp.trim());
  }

  wildcardMatcher(value: string, conditionValue: string): boolean {
    const regex = new RegExp(conditionValue.replace(/\*/g, '.*'));
    return regex.test(value);
  }

  getEntityFieldValue(fieldName: string, entity: EndpointEntity): any {
    const paths = LabelCriteriaFields[fieldName];
    if (!paths) {
      throw new Error(`Unknown field name: ${fieldName}`);
    }

    const values = paths
      .map((path) =>
        path.split('.').reduce((acc: any, key: string) => acc?.[key], entity)
      )
      .flat()
      .filter((value) => value !== undefined && value !== null);

    return Array.from(new Set(values));
  }
}
