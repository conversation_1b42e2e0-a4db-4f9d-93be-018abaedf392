import { BaseLabelingRule } from './LabelingRule';
import {
  CommonEntityInfo,
  Label,
  LabelType
} from '@globalTypes/ndrBenchmarkTypes';

export class TagRule extends BaseLabelingRule<CommonEntityInfo['tags']> {
  constructor() {
    super();
  }

  private parseTags(
    context: CommonEntityInfo['tags']
  ): Record<string, string> | null {
    if (typeof context === 'string') {
      try {
        return JSON.parse(context);
      } catch {
        return null;
      }
    }
    return context || null;
  }

  matches(context: CommonEntityInfo['tags']): boolean {
    const tags = this.parseTags(context);
    return tags ? Object.keys(tags).length > 0 : false;
  }

  getLabels(context: CommonEntityInfo['tags']): Label[] {
    const tags = this.parseTags(context);

    if (!tags) {
      return [];
    }

    return Object.entries(tags).map(([key, value]) => {
      const id = this.getId(`${key}-${value}`);
      return {
        id,
        key,
        values: [value],
        type: this.getLabelType()
      };
    });
  }

  getLabelType(): LabelType {
    return 'static';
  }
}
