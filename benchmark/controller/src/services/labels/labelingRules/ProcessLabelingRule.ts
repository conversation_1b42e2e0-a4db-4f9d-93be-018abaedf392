import { BaseLabelingRule } from './LabelingRule';
import { Label } from '@globalTypes/ndrBenchmarkTypes';

type ProcessServiceMap = {
  serviceName: string;
  labelName: string;
};

export class ProcessLabelingRule<
  T extends { processes: string[] }
> extends BaseLabelingRule<T> {
  private matchedProcesses: string[] = [];
  private readonly PROCESS_SERVICE_MAP: Record<string, ProcessServiceMap> = {
    'FileZilla Server.exe': { serviceName: 'Server', labelName: 'FTP' },
    'spoolsv.exe': { serviceName: 'Service', labelName: 'Print Service' },
    'sip_server.exe': { serviceName: 'Server', labelName: 'SIP/VoIP Server' },
    'redis-server.exe': { serviceName: 'Database', labelName: 'Redis' }
  };

  prepare(context: T): void {
    this.matchedProcesses = context.processes.filter(
      (process) => process in this.PROCESS_SERVICE_MAP
    );
  }

  matches(context: T): boolean {
    return !!this.matchedProcesses.length;
  }

  getLabels(context: T): Label[] {
    return this.matchedProcesses.map((port) => {
      const { labelName, serviceName } = this.PROCESS_SERVICE_MAP[port];
      const id = this.getId(`${serviceName}-${labelName}`);
      return {
        id,
        key: serviceName,
        values: [labelName],
        type: this.getLabelType()
      };
    });
  }

  getLabelType(): 'ai' {
    return 'ai';
  }
}
