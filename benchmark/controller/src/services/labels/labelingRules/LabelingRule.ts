import { Label, LabelType } from '@globalTypes/ndrBenchmarkTypes';

export interface LabelingRule<T> {
  prepare?: (context: T) => void;
  matches: (context: T) => boolean;
  getLabels: (context: T) => Label[];
  getLabelType: () => LabelType;
}

export abstract class BaseLabelingRule<T> implements LabelingRule<T> {
  abstract matches(context: T): boolean;

  abstract getLabels(context: T): Label[];

  abstract getLabelType(): LabelType;

  getId(name: string) {
    return `${name.toLowerCase().replace(/[\s/]+/g, '-')}-${this.getLabelType()}`;
  }
}
