import { BaseLabelingRule } from './LabelingRule';
import {
  CommonEntityInfo,
  Label,
  LabelType
} from '@globalTypes/ndrBenchmarkTypes';

export class SimpleFieldRule<
  T extends CommonEntityInfo
> extends BaseLabelingRule<T> {
  constructor(private fieldName: keyof T) {
    super();
  }

  matches(context: T): boolean {
    if (Array.isArray(context[this.fieldName])) {
      return !!(context[this.fieldName] as Array<string>)?.length;
    }
    return !!context[this.fieldName];
  }

  getLabels(context: T): Label[] {
    let items = [];
    if (
      Array.isArray(context[this.fieldName]) &&
      (context[this.fieldName] as Array<string>)?.length
    ) {
      items.push(...(context[this.fieldName] as Array<string>));
    } else {
      items.push(context[this.fieldName] as string);
    }

    return items.map((name) => {
      const id = this.getId(`${this.fieldName.toString()}-${name}`);
      return {
        id,
        key: this.fieldName.toString(),
        values: [name],
        type: this.getLabelType()
      };
    });
  }

  getLabelType(): LabelType {
    return 'static';
  }
}
