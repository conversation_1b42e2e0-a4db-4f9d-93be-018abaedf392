import {
  BasicLabel,
  Label,
  LabelAssignment,
  UserLabel
} from '@globalTypes/ndrBenchmarkTypes';
import { LabelingRule } from './labelingRules/LabelingRule';
import { isEqual, uniq } from 'lodash';

export type LabelingResult = {
  createdLabels: BasicLabel[];
  assignedLabelIds: string[];
};

export type EntityOperation<T> = {
  entityId: string;
  context: T;
  rules: LabelingRule<T>[];
};

type EntityLabelChanges = {
  existingLabelIds: string[];
  newLabelIds: string[];
};

export class LabelProcessor {
  private operations: EntityOperation<any>[] = [];
  private assignmentsMap: Map<string, EntityLabelChanges> = new Map();
  private createdLabels: BasicLabel[] = [];
  private userDefinedLabelsMap: Map<string, string[]> = new Map();
  private labelMap: Map<string, BasicLabel>;

  constructor(
    labels: Label[],
    private labelAssignments: LabelAssignment[]
  ) {
    this.labelMap = new Map(labels.map((label) => [label.id, label]));
    this.initAssignments();
    this.initUserDefinedLabelsMap();
  }

  private initAssignments() {
    this.assignmentsMap = new Map(
      this.labelAssignments.map((la) => [
        la.entityId,
        {
          existingLabelIds: la.labelIds,
          newLabelIds: []
        }
      ])
    );
  }

  private initUserDefinedLabelsMap(): void {
    for (const assignment of this.labelAssignments) {
      const userLabelIds = (assignment.labelIds || []).filter((labelId) => {
        const label = this.labelMap.get(labelId);
        const userLabel = label as UserLabel;
        return (
          label &&
          label.type === 'user' &&
          (!userLabel.conditions || userLabel.conditions.length === 0)
        );
      });

      if (userLabelIds.length > 0) {
        this.userDefinedLabelsMap.set(assignment.entityId, userLabelIds);
      }
    }
  }

  addEntityOperation<T>(
    entityId: string,
    context: T,
    rules: LabelingRule<T>[]
  ): void {
    this.operations.push({ entityId, context, rules });
  }

  private labelEntity<T>(context: T, rules: LabelingRule<T>[]): LabelingResult {
    const assignedLabelIds: string[] = [];
    const createdLabels: BasicLabel[] = [];
    for (const rule of rules) {
      rule.prepare?.(context);

      if (rule.matches(context)) {
        rule.getLabels(context).forEach((label) => {
          if (!this.labelMap.has(label.id)) {
            this.labelMap.set(label.id, label);
            createdLabels.push(label);
          }
          assignedLabelIds.push(label.id);
        });
      }
    }

    return {
      createdLabels,
      assignedLabelIds
    };
  }

  private storeEntityAssignments(entityId: string, labelIds: string[]): void {
    const assignment = this.assignmentsMap.get(entityId) || {
      existingLabelIds: [],
      newLabelIds: []
    };

    this.assignmentsMap.set(entityId, {
      existingLabelIds: assignment.existingLabelIds,
      newLabelIds: [...assignment.newLabelIds, ...labelIds]
    });
  }

  process(): {
    createdLabels: Label[];
    labelAssignments: LabelAssignment[];
  } {
    for (const operation of this.operations) {
      const { createdLabels, assignedLabelIds } = this.labelEntity(
        operation.context,
        operation.rules
      );

      this.storeEntityAssignments(operation.entityId, assignedLabelIds);
      this.createdLabels.push(...createdLabels);
    }

    const finalAssignments = this.generateFinalAssignments();

    return {
      createdLabels: this.createdLabels as Label[],
      labelAssignments: finalAssignments
    };
  }

  private generateFinalAssignments(): LabelAssignment[] {
    const finalAssignments: LabelAssignment[] = [];

    for (const [entityId, assignment] of this.assignmentsMap.entries()) {
      const userLabelIds = this.userDefinedLabelsMap.get(entityId) || [];
      const newLabels = uniq([
        ...assignment.newLabelIds,
        ...userLabelIds
      ]).sort();

      if (!isEqual(newLabels, uniq(assignment.existingLabelIds).sort())) {
        finalAssignments.push({
          entityId,
          labelIds: newLabels
        });
      }
    }

    return finalAssignments;
  }
}
