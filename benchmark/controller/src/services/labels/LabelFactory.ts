import { CommonEntityInfo, UserLabel } from '@globalTypes/ndrBenchmarkTypes';
import { LabelingRule } from './labelingRules/LabelingRule';
import { SimpleFieldRule } from './labelingRules/SimpleFieldRule';
import { ConditionalLabelingRule } from './labelingRules/ConditionalLabelingRule';

export const labelingRules: LabelingRule<CommonEntityInfo>[] = [
  new SimpleFieldRule('deviceType'),
  new SimpleFieldRule('osName'),
  new SimpleFieldRule('cloudProvider'),
  new SimpleFieldRule('deployment'),
  new SimpleFieldRule('manufacturer'),
  new SimpleFieldRule('activeDirectoryOu')
];

export function createConditionalRules(labels: UserLabel[]) {
  return labels.map((label) => new ConditionalLabelingRule(label));
}
