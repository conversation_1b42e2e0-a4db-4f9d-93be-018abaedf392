import { ConditionalLabelingRule } from '../labelingRules/ConditionalLabelingRule';
import {
  UserLabel,
  MatchTypes,
  EndpointEntity
} from '@globalTypes/ndrBenchmarkTypes';

describe('ConditionalLabelingRule', () => {
  it('should match when the field value matches the condition', () => {
    const entity = { common: { osName: 'Windows' } } as EndpointEntity;

    const label: UserLabel = {
      id: 'test-label',
      key: 'Test Label',
      values: ['Win'],
      type: 'user',
      conditions: [
        {
          fieldName: 'osName',
          matchType: MatchTypes.EQUALS,
          value: 'Windows',
          include: true
        }
      ]
    };

    const rule = new ConditionalLabelingRule(label);
    expect(rule.matches(entity)).toBe(true);
  });

  it('should match when ALL INCLUSION condition are met', () => {
    const entity = {
      common: {
        osName: 'Windows',
        ipAddresses: ['***********', '********']
      }
    } as EndpointEntity;

    const label: UserLabel = {
      id: 'test-label',
      key: 'Test Label',
      values: ['Win'],
      type: 'user',
      conditions: [
        {
          fieldName: 'osName',
          matchType: MatchTypes.EQUALS,
          value: 'Windows',
          include: true
        },
        {
          fieldName: 'ipAddresses',
          matchType: MatchTypes.IP_RANGE,
          value: '***********-***********0',
          include: true
        }
      ]
    };

    const rule = new ConditionalLabelingRule(label);
    expect(rule.matches(entity)).toBe(true);
  });

  it('should NOT match when the EXCLUSION condition matches', () => {
    const entity = { common: { osName: 'Windows' } } as EndpointEntity;

    const label: UserLabel = {
      id: 'test-label',
      key: 'Test Label',
      values: ['NotWin'],
      type: 'user',
      conditions: [
        {
          fieldName: 'osName',
          matchType: MatchTypes.EQUALS,
          value: 'Windows',
          include: false
        }
      ]
    };

    const rule = new ConditionalLabelingRule(label);
    expect(rule.matches(entity)).toBe(false);
  });

  it('should match when met the INCLUSION condition and NOT met EXCLUSION condition', () => {
    const entity = {
      common: {
        osName: 'Windows',
        ipAddresses: ['***********', '********']
      }
    } as EndpointEntity;

    const label: UserLabel = {
      id: 'test-label',
      key: 'Test Label',
      values: ['Win'],
      type: 'user',
      conditions: [
        {
          fieldName: 'osName',
          matchType: MatchTypes.EQUALS,
          value: 'Windows',
          include: true
        },
        {
          fieldName: 'osName',
          matchType: MatchTypes.CONTAINS,
          value: 'Linux',
          include: false
        }
      ]
    };

    const rule = new ConditionalLabelingRule(label);
    expect(rule.matches(entity)).toBe(true);
  });

  it('should match when at least one value in the array matches the inclusion condition', () => {
    const entity = {
      common: {
        osName: 'Windows',
        ipAddresses: ['***********', '********']
      }
    } as EndpointEntity;

    const label: UserLabel = {
      id: 'test-label',
      key: 'Test Label',
      values: ['Win'],
      type: 'user',
      conditions: [
        {
          fieldName: 'ipAddresses',
          matchType: MatchTypes.EQUALS,
          value: '********',
          include: true
        }
      ]
    };

    const rule = new ConditionalLabelingRule(label);
    expect(rule.matches(entity)).toBe(true);
  });

  it('should match when the IP address is within the specified range', () => {
    const entity = {
      common: {
        osName: 'Windows',
        ipAddresses: ['***********']
      }
    } as EndpointEntity;

    const label: UserLabel = {
      id: 'test-label',
      key: 'Test Label',
      values: ['Win'],
      type: 'user',
      conditions: [
        {
          fieldName: 'ipAddresses',
          matchType: MatchTypes.IP_RANGE,
          value: '***********-***********0',
          include: true
        }
      ]
    };

    const rule = new ConditionalLabelingRule(label);
    expect(rule.matches(entity)).toBe(true);
  });

  it('should match when the IP address is in the CIDR range', () => {
    const entity = {
      common: {
        osName: 'Windows',
        ipAddresses: ['***********']
      }
    } as EndpointEntity;

    const label: UserLabel = {
      id: 'test-label',
      key: 'Test Label',
      values: ['Win'],
      type: 'user',
      conditions: [
        {
          fieldName: 'ipAddresses',
          matchType: MatchTypes.CIDR,
          value: '***********/24',
          include: true
        }
      ]
    };

    const rule = new ConditionalLabelingRule(label);
    expect(rule.matches(entity)).toBe(true);
  });

  it('should NOT match when the inclusion condition matches, but the exclusion condition also matches', () => {
    const entity = {
      common: {
        osName: 'Windows',
        ipAddresses: ['***********', '********']
      }
    } as EndpointEntity;

    const label: UserLabel = {
      id: 'test-label',
      key: 'Test Label',
      values: ['NotWin'],
      type: 'user',
      conditions: [
        {
          fieldName: 'osName',
          matchType: MatchTypes.EQUALS,
          value: 'Windows',
          include: true
        },
        {
          fieldName: 'ipAddresses',
          matchType: MatchTypes.EQUALS,
          value: '***********',
          include: false
        }
      ]
    };

    const rule = new ConditionalLabelingRule(label);
    expect(rule.matches(entity)).toBe(false);
  });

  it('should match when the value matches the wildcard pattern', () => {
    const entity = {
      common: {
        osName: 'Windows 10 Pro',
        ipAddresses: ['***********']
      }
    } as EndpointEntity;

    // Test for wildcard at the beginning
    const label1: UserLabel = {
      id: 'test-label-1',
      key: 'Test Label 1',
      values: ['Pro'],
      type: 'user',
      conditions: [
        {
          fieldName: 'osName',
          matchType: MatchTypes.WILDCARD,
          value: '*Pro',
          include: true
        }
      ]
    };

    // Test for wildcard at the end
    const label2: UserLabel = {
      id: 'test-label-2',
      key: 'Test Label 2',
      values: ['Win'],
      type: 'user',
      conditions: [
        {
          fieldName: 'osName',
          matchType: MatchTypes.WILDCARD,
          value: 'Windows*',
          include: true
        }
      ]
    };

    // Test for wildcard in the middle
    const label3: UserLabel = {
      id: 'test-label-3',
      key: 'Test Label 3',
      values: ['Win'],
      type: 'user',
      conditions: [
        {
          fieldName: 'osName',
          matchType: MatchTypes.WILDCARD,
          value: 'Windows*Pro',
          include: true
        }
      ]
    };

    // Test for multiple wildcards
    const label4: UserLabel = {
      id: 'test-label-4',
      key: 'Test Label 4',
      values: ['Win'],
      type: 'user',
      conditions: [
        {
          fieldName: 'osName',
          matchType: MatchTypes.WILDCARD,
          value: 'Win*10*',
          include: true
        }
      ]
    };

    const rule1 = new ConditionalLabelingRule(label1);
    const rule2 = new ConditionalLabelingRule(label2);
    const rule3 = new ConditionalLabelingRule(label3);
    const rule4 = new ConditionalLabelingRule(label4);

    expect(rule1.matches(entity)).toBe(true);
    expect(rule2.matches(entity)).toBe(true);
    expect(rule3.matches(entity)).toBe(true);
    expect(rule4.matches(entity)).toBe(true);
  });

  it('should not match when the value does not match the wildcard pattern', () => {
    const entity = {
      common: {
        osName: 'Ubuntu 20.04 LTS',
        ipAddresses: ['***********']
      }
    } as EndpointEntity;

    const label: UserLabel = {
      id: 'test-label',
      key: 'Test Label',
      values: ['Win'],
      type: 'user',
      conditions: [
        {
          fieldName: 'osName',
          matchType: MatchTypes.WILDCARD,
          value: 'Win*',
          include: true
        }
      ]
    };

    const rule = new ConditionalLabelingRule(label);
    expect(rule.matches(entity)).toBe(false);
  });

  describe('ConditionalLabelingRule with field mapping', () => {
    it('should match nested fields (common.osName)', () => {
      const entity = {
        id: 'entity-123',
        common: {
          osName: 'Windows 10 Pro',
          ipAddresses: ['***********']
        }
      } as EndpointEntity;

      const label: UserLabel = {
        id: 'test-label',
        key: 'Test Label',
        values: ['Win'],
        type: 'user',
        conditions: [
          {
            fieldName: 'osName',
            matchType: MatchTypes.EQUALS,
            value: 'Windows 10 Pro',
            include: true
          }
        ]
      };

      const rule = new ConditionalLabelingRule(label);
      expect(rule.matches(entity)).toBe(true);
    });

    it('should match the top-level field (integrationType)', () => {
      const entity = {
        id: 'entity-456',
        integrationType: 'azure',
        common: {
          osName: 'Linux'
        }
      } as unknown as EndpointEntity;

      const label: UserLabel = {
        id: 'test-label',
        key: 'Test Label',
        values: ['Azure'],
        type: 'user',
        conditions: [
          {
            fieldName: 'integrationType',
            matchType: MatchTypes.EQUALS,
            value: 'azure',
            include: true
          }
        ]
      };

      const rule = new ConditionalLabelingRule(label);
      expect(rule.matches(entity)).toBe(true);
    });

    it('should handle multiple fields', () => {
      const entity = {
        id: 'entity-456',
        traffic: { ips: ['**********'] },
        common: {
          ipAddresses: [] //explicitly empty to make sure traffic.ips handles
        }
      } as unknown as EndpointEntity;

      const label: UserLabel = {
        id: 'test-label',
        key: 'Test Label',
        values: ['VPN'],
        type: 'user',
        conditions: [
          {
            fieldName: 'ipAddresses',
            matchType: MatchTypes.CIDR,
            value: '**********/21',
            include: true
          }
        ]
      };

      const rule = new ConditionalLabelingRule(label);
      // uses common.ipAddresses and traffic.ips based on labelCriteriaCategoryMapping
      expect(rule.matches(entity)).toBe(true);
    });

    it('should throw an error for an unknown field', () => {
      const entity = {
        id: 'entity-123',
        common: {
          osName: 'Windows'
        }
      } as unknown as EndpointEntity;

      const label: UserLabel = {
        id: 'test-label',
        key: 'Test Label',
        values: ['Win'],
        type: 'user',
        conditions: [
          {
            fieldName: 'nonExistentField',
            matchType: MatchTypes.EQUALS,
            value: 'someValue',
            include: true
          }
        ]
      };

      const rule = new ConditionalLabelingRule(label);
      expect(() => rule.matches(entity)).toThrow(
        'Unknown field name: nonExistentField'
      );
    });
  });
});
