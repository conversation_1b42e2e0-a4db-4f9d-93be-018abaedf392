import { LabelProcessor } from '../LabelProcessor';
import {
  EndpointEntity,
  Label,
  LabelAssignment,
  UserLabel
} from '@globalTypes/ndrBenchmarkTypes';
import { TestStaticRule } from './labelingRules/TestStaticRule';
import { ConditionalLabelingRule } from '../labelingRules/ConditionalLabelingRule';

const labels: Label[] = [
  { id: 'label-static', key: 'osType', values: ['Ubuntu'], type: 'static' },
  { id: 'label-user', key: 'User Label', values: ['CRM'], type: 'user' }
];

describe('LabelProcessor ', () => {
  it('should return only changed assignments', () => {
    const assignments: LabelAssignment[] = [
      {
        entityId: 'entity-1',
        labelIds: ['label-static', 'label-user'] // initial user + static
      }
    ];

    const processor = new LabelProcessor(labels, assignments);

    const newLabel = {
      id: 'new-label-static',
      key: 'New osType Label',
      values: ['Windows'],
      type: 'static'
    } as Label;

    // Add operation with rule that adds a new label
    processor.addEntityOperation('entity-1', {}, [
      new TestStaticRule([newLabel])
    ]);

    const result = processor.process();

    // Should return one assignment with merged new + user labels
    expect(result.labelAssignments).toHaveLength(1);
    expect(result.labelAssignments[0]).toEqual({
      entityId: 'entity-1',
      labelIds: ['label-user', 'new-label-static'] // static label is gone, new-label + user remain, sorted
    });

    // New label should be included in createdLabels
    expect(result.createdLabels).toEqual([newLabel]);
  });

  it('should return no assignments if nothing changed', () => {
    const assignments: LabelAssignment[] = [
      {
        entityId: 'entity-1',
        labelIds: ['label-static'] // already assigned
      }
    ];

    const processor = new LabelProcessor(labels, assignments);
    const sameLabel = {
      id: 'label-static',
      key: 'New osType Label',
      values: ['Windows'],
      type: 'static'
    } as Label;

    // Rule returns the same label that already exists
    processor.addEntityOperation('entity-1', {}, [
      new TestStaticRule([sameLabel])
    ]);

    const result = processor.process();

    // Should not produce new assignments since labels are unchanged
    expect(result.labelAssignments).toHaveLength(0);

    // Should also not produce any createdLabels (label already exists)
    expect(result.createdLabels).toHaveLength(0);
  });

  it('should return result of assignments if previously assigned label is no longer matched', () => {
    const assignments: LabelAssignment[] = [
      {
        entityId: 'entity-1',
        labelIds: ['label-static'] // assigned initially
      }
    ];

    const processor = new LabelProcessor(labels, assignments);

    const noLabels = [] as Label[];

    // Rule does not return labels anymore
    processor.addEntityOperation('entity-1', {}, [
      new TestStaticRule(noLabels)
    ]);

    const result = processor.process();

    expect(result.labelAssignments).toHaveLength(1);
    expect(result.labelAssignments[0]).toEqual({
      entityId: 'entity-1',
      labelIds: [] // no labels remains
    });
    expect(result.createdLabels).toHaveLength(0);
  });

  it('should return assignment for a new entity', () => {
    const assignments: LabelAssignment[] = [
      {
        entityId: 'entity-1',
        labelIds: ['label-static'] // assigned initially
      }
    ];

    const processor = new LabelProcessor(labels, assignments);

    const newLabel = {
      id: 'label-static',
      key: 'New osType Label',
      values: ['Windows'],
      type: 'static'
    } as Label;

    // New entity with matching rule
    processor.addEntityOperation('entity-1', {}, [
      new TestStaticRule([newLabel])
    ]);
    processor.addEntityOperation('new-entity', {}, [
      new TestStaticRule([newLabel])
    ]);

    const result = processor.process();

    expect(result.labelAssignments).toHaveLength(1);
    expect(result.labelAssignments[0]).toEqual({
      entityId: 'new-entity',
      labelIds: ['label-static']
    });

    // Since label already exists, it should not be added to createdLabels
    expect(result.createdLabels).toHaveLength(0);
  });

  it('should return empty assignment if entity was removed', () => {
    const assignments: LabelAssignment[] = [
      {
        entityId: 'entity-1',
        labelIds: ['label-static'] // assigned initially
      },
      {
        entityId: 'entity-2',
        labelIds: ['label-static'] // assigned initially
      }
    ];

    const processor = new LabelProcessor(labels, assignments);

    const newLabel = {
      id: 'label-static',
      key: 'New osType Label',
      values: ['Windows'],
      type: 'static'
    } as Label;

    //only entity-1, without entity-2
    const fetchedEntities = ['entity-1'];

    for (const entity of fetchedEntities) {
      processor.addEntityOperation(entity, {}, [
        new TestStaticRule([newLabel])
      ]);
    }

    const result = processor.process();

    expect(result.labelAssignments).toHaveLength(1);
    expect(result.labelAssignments[0]).toEqual({
      entityId: 'entity-2',
      labelIds: []
    });

    // Since label already exists, it should not be added to createdLabels
    expect(result.createdLabels).toHaveLength(0);
  });

  describe('Users Labels', () => {
    it('should preserve user-defined labels even if not returned by any rule', () => {
      const assignments: LabelAssignment[] = [
        { entityId: 'entity-1', labelIds: ['label-user', 'label-static'] }
      ];

      const processor = new LabelProcessor(labels, assignments);

      // Simulate that no rules return any labels
      const noLabels = [] as Label[];
      processor.addEntityOperation('entity-1', {}, [
        new TestStaticRule(noLabels)
      ]);

      const result = processor.process();

      expect(result.labelAssignments).toHaveLength(1);
      expect(result.labelAssignments[0]).toEqual({
        entityId: 'entity-1',
        labelIds: ['label-user'] // user label still present
      });

      expect(result.createdLabels).toHaveLength(0);
    });

    it('should remove user-defined labels if label itself is not exist', () => {
      const assignments: LabelAssignment[] = [
        { entityId: 'entity-1', labelIds: ['label-user', 'label-static'] }
      ];
      const labels: Label[] = [
        // there is no users label anymore
      ];

      const processor = new LabelProcessor(labels, assignments);

      // Simulate that no rules return any labels
      const noLabels = [] as Label[];
      processor.addEntityOperation('entity-1', {}, [
        new TestStaticRule(noLabels)
      ]);

      const result = processor.process();

      expect(result.labelAssignments).toHaveLength(1);
      expect(result.labelAssignments[0]).toEqual({
        entityId: 'entity-1',
        labelIds: [] // empty labels
      });

      expect(result.createdLabels).toHaveLength(0);
    });
  });

  describe('Users Conditional Labels', () => {
    it('should apply conditional labels', () => {
      const entity = {
        id: 'entity-1',
        common: {
          osName: 'Windows'
        }
      } as EndpointEntity;

      const assignments: LabelAssignment[] = [
        { entityId: 'entity-1', labelIds: ['label-user', 'label-static'] }
      ];

      const processor = new LabelProcessor(labels, assignments);

      const newLabel = {
        id: 'new-label-user',
        key: 'New User Label',
        values: ['CRM'],
        type: 'user',
        conditions: [
          {
            matchType: 'equals',
            fieldName: 'osName',
            value: 'Windows',
            include: true
          }
        ]
      } as UserLabel;

      processor.addEntityOperation(entity.id, entity, [
        new ConditionalLabelingRule(newLabel)
      ]);

      const result = processor.process();

      expect(result.labelAssignments).toHaveLength(1);
      expect(result.labelAssignments[0]).toEqual({
        entityId: 'entity-1',
        labelIds: ['label-user', 'new-label-user'] // user label still present
      });

      expect(result.createdLabels).toHaveLength(1);
    });

    it('should remove conditional label when condition is updated to exclude entity', () => {
      const entity = {
        id: 'entity-1',
        common: {
          osName: 'Windows'
        }
      } as EndpointEntity;

      const assignments: LabelAssignment[] = [
        { entityId: 'entity-1', labelIds: ['label-user', 'label-static'] }
      ];

      const processor = new LabelProcessor(labels, assignments);

      // First add a label with include: true
      const newLabel = {
        id: 'new-label-user',
        key: 'New User Label',
        values: ['CRM'],
        type: 'user',
        conditions: [
          {
            matchType: 'equals',
            fieldName: 'osName',
            value: 'Windows',
            include: true
          }
        ]
      } as UserLabel;

      processor.addEntityOperation(entity.id, entity, [
        new ConditionalLabelingRule(newLabel)
      ]);

      const firstResult = processor.process();

      // Verify label was added
      expect(firstResult.labelAssignments).toHaveLength(1);
      expect(firstResult.labelAssignments[0].labelIds).toContain(
        'new-label-user'
      );

      // Now update the label to have include: false
      const updatedLabel = {
        ...newLabel,
        conditions: [
          {
            matchType: 'equals',
            fieldName: 'osName',
            value: 'Windows',
            include: false
          }
        ]
      } as UserLabel;

      // Create new processor with the updated assignments
      const updatedProcessor = new LabelProcessor(
        [...labels, updatedLabel],
        firstResult.labelAssignments
      );

      // Apply the updated rule
      updatedProcessor.addEntityOperation(entity.id, entity, [
        new ConditionalLabelingRule(updatedLabel)
      ]);

      const finalResult = updatedProcessor.process();

      // Verify the conditional label was removed
      expect(finalResult.labelAssignments).toHaveLength(1);
      expect(finalResult.labelAssignments[0]).toEqual({
        entityId: 'entity-1',
        labelIds: ['label-user'] // conditional label should be gone
      });
    });
  });
});
