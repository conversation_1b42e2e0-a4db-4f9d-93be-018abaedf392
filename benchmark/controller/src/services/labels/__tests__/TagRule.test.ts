import { TagRule } from '../labelingRules/TagRule';

describe('TagRule', () => {
  let tagRule: TagRule;

  beforeEach(() => {
    tagRule = new TagRule();
  });

  describe('matches', () => {
    it('should return true when tags object has key-value pairs', () => {
      const context = {
        environment: 'production',
        team: 'security'
      };

      expect(tagRule.matches(context)).toBe(true);
    });

    it('should return true when tags string has valid JSON with key-value pairs', () => {
      const context = JSON.stringify({
        environment: 'production',
        team: 'security'
      });

      expect(tagRule.matches(context)).toBe(true);
    });

    it('should return false when tags object is empty', () => {
      const context = {};

      expect(tagRule.matches(context)).toBe(false);
    });

    it('should return false when tags string is invalid JSON', () => {
      const context = 'invalid json';

      expect(tagRule.matches(context)).toBe(false);
    });
  });

  describe('getLabels', () => {
    it('should return labels for all key-value pairs in tags object', () => {
      const context = {
        environment: 'production',
        team: 'security',
        region: 'us-west-1'
      };

      const labels = tagRule.getLabels(context);

      expect(labels).toHaveLength(3);
      expect(labels).toEqual([
        {
          id: 'environment-production-static',
          key: 'environment',
          values: ['production'],
          type: 'static'
        },
        {
          id: 'team-security-static',
          key: 'team',
          values: ['security'],
          type: 'static'
        },
        {
          id: 'region-us-west-1-static',
          key: 'region',
          values: ['us-west-1'],
          type: 'static'
        }
      ]);
    });

    it('should return labels for all key-value pairs in tags string', () => {
      const context = JSON.stringify({
        environment: 'production',
        team: 'security'
      });

      const labels = tagRule.getLabels(context);

      expect(labels).toHaveLength(2);
      expect(labels).toEqual([
        {
          id: 'environment-production-static',
          key: 'environment',
          values: ['production'],
          type: 'static'
        },
        {
          id: 'team-security-static',
          key: 'team',
          values: ['security'],
          type: 'static'
        }
      ]);
    });

    it('should return empty array when tags object is empty', () => {
      const context = {};

      const labels = tagRule.getLabels(context);

      expect(labels).toHaveLength(0);
    });

    it('should return empty array when tags string is invalid JSON', () => {
      const context = 'invalid json';

      const labels = tagRule.getLabels(context);

      expect(labels).toHaveLength(0);
    });
  });

  describe('getLabelType', () => {
    it('should return static', () => {
      expect(tagRule.getLabelType()).toBe('static');
    });
  });
});
