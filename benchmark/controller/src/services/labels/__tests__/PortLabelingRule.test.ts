import { PortLabelingRule } from '../labelingRules/PortLabelingRule';

describe('PortLabelingRule', () => {
  it('should match and return label for a single known port', () => {
    const entity = { ports: [22] };
    const rule = new PortLabelingRule<typeof entity>();

    rule.prepare(entity);
    expect(rule.matches(entity)).toBe(true);

    const labels = rule.getLabels(entity);
    expect(labels).toEqual([
      {
        id: expect.any(String),
        key: 'Service',
        values: ['SSH'],
        type: 'ai'
      }
    ]);
  });

  it('should match and return labels for multiple known ports', () => {
    const entity = { ports: [22, 443, 3306] };
    const rule = new PortLabelingRule<typeof entity>();

    rule.prepare(entity);
    expect(rule.matches(entity)).toBe(true);

    const labels = rule.getLabels(entity);
    const labelKeys = labels.map((l) => l.values[0]);
    expect(labelKeys).toEqual(
      expect.arrayContaining(['SSH', 'Web Server', 'MySQL'])
    );
  });

  it('should not match if no known ports are present', () => {
    const entity = { ports: [9999, 8888] };
    const rule = new PortLabelingRule<typeof entity>();

    rule.prepare(entity);
    expect(rule.matches(entity)).toBe(false);

    const labels = rule.getLabels(entity);
    expect(labels).toEqual([]);
  });

  it('should handle empty ports array gracefully', () => {
    const entity = { ports: [] };
    const rule = new PortLabelingRule<typeof entity>();

    rule.prepare(entity);
    expect(rule.matches(entity)).toBe(false);
    expect(rule.getLabels(entity)).toEqual([]);
  });

  it('should return correct label type', () => {
    const rule = new PortLabelingRule<{ ports: number[] }>();
    expect(rule.getLabelType()).toBe('ai');
  });
});
