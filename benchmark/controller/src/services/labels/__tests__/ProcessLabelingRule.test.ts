import { ProcessLabelingRule } from '../labelingRules/ProcessLabelingRule';

describe('ProcessLabelingRule', () => {
  it('should match and return label for a single known process', () => {
    const entity = { processes: ['FileZilla Server.exe'] };
    const rule = new ProcessLabelingRule<typeof entity>();

    rule.prepare(entity);
    expect(rule.matches(entity)).toBe(true);

    const labels = rule.getLabels(entity);
    expect(labels).toEqual([
      {
        id: expect.any(String),
        key: 'Server',
        values: ['FTP'],
        type: 'ai'
      }
    ]);
  });

  it('should match and return labels for multiple known processes', () => {
    const entity = { processes: ['FileZilla Server.exe', 'spoolsv.exe'] };
    const rule = new ProcessLabelingRule<typeof entity>();

    rule.prepare(entity);
    expect(rule.matches(entity)).toBe(true);

    const labels = rule.getLabels(entity);
    const labelKeys = labels.map((l) => l.values[0]);
    expect(labelKeys).toEqual(expect.arrayContaining(['FTP', 'Print Service']));
  });

  it('should not match if no known processes are present', () => {
    const entity = { processes: ['unknown.exe', 'something.exe'] };
    const rule = new ProcessLabelingRule<typeof entity>();

    rule.prepare(entity);
    expect(rule.matches(entity)).toBe(false);

    const labels = rule.getLabels(entity);
    expect(labels).toEqual([]);
  });

  it('should handle empty processes array gracefully', () => {
    const entity = { processes: [] };
    const rule = new ProcessLabelingRule<typeof entity>();

    rule.prepare(entity);
    expect(rule.matches(entity)).toBe(false);
    expect(rule.getLabels(entity)).toEqual([]);
  });

  it('should return correct label type', () => {
    const rule = new ProcessLabelingRule<{ processes: string[] }>();
    expect(rule.getLabelType()).toBe('ai');
  });
});
