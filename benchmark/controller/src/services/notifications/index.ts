import log from '../../logger';
import { Notification } from '../../globalTypes/ndrBenchmarkTypes';
import { Severity } from '../../globalTypes/ndrBenchmarkTypes/issueTypes';
import {
  NOTIFICATION_STATISTICS_TYPES,
  NotificationStatisticsType,
  StatObject,
  BarChartData,
  LineGraphData,
  PieChartData,
  StatisticsObject
} from '../../benchmark/controls/types';

const DAY = 24 * 60 * 60 * 1000;
const WEEK = 7 * DAY;

export function generateBarChartData(
  notifications: Notification[]
): BarChartData[] {
  const notificationCounts = new Map<string, number>();

  for (const notification of notifications) {
    const currentCount = notificationCounts.get(notification.title) || 0;
    notificationCounts.set(notification.title, currentCount + 1);
  }

  const data = Array.from(notificationCounts).map(([title, count]) => ({
    key: title,
    data: count
  }));

  log.info('Generated bar chart data');

  return data;
}

export function calculateCountByTime(
  notifications: Notification[],
  timeScope: '24h' | 'week'
): number {
  const now = Date.now();
  const timeFrame = timeScope === 'week' ? WEEK : DAY;

  const count = notifications.filter(
    (notification) => new Date(notification.time).getTime() > now - timeFrame
  ).length;

  return count;
}

export function generateLineGraphData(
  notifications: Notification[]
): LineGraphData[] {
  const formatDate = (date: Date): string => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const formatBackToDate = (date: string): Date => {
    const [day, month, year] = date.split('/');
    return new Date(Number(year), Number(month) - 1, Number(day));
  };

  const data: Array<{ key: Date; data: number }> = [];
  const notificationCount: { [key: string]: number } = {};

  const earliestDate = new Date(
    notifications[notifications.length - 1]?.time || Date.now()
  );
  let latestDate = new Date();

  while (latestDate.getTime() > earliestDate.getTime()) {
    const date = formatDate(latestDate);
    notificationCount[date] = 0;
    latestDate = new Date(latestDate.setDate(latestDate.getDate() - 1));
  }

  notifications.forEach((notification) => {
    const date = formatDate(new Date(notification.time));
    notificationCount[date] = (notificationCount[date] || 0) + 1;
  });

  Object.entries(notificationCount).forEach(([date, count]) => {
    data.push({ key: formatBackToDate(date), data: count });
  });

  log.info(`Generated line graph notification`);

  return data.sort((a, b) => a.key.getTime() - b.key.getTime());
}

export function generatePieChartData(
  notifications: Notification[]
): PieChartData[] {
  const labels: Severity[] = ['info', 'low', 'medium', 'high', 'critical'];
  const severityMap = new Map<string, number>();

  labels.forEach((label) => severityMap.set(label, 0));

  notifications.forEach((notification) => {
    const key = notification.severity;
    const currentCount = severityMap.get(key) || 0;
    severityMap.set(key, currentCount + 1);
  });

  const data = Array.from(severityMap.entries()).map(([key, data]) => ({
    key,
    data
  }));

  return data;
}

export const calculateTotalCount = (notifications: Notification[]): number =>
  notifications.length;

export const mapStatisticsToResult = (
  notificationsStatistics: StatisticsObject
): StatObject[] => {
  return Object.entries(notificationsStatistics).map(([type, data]) => ({
    stat: {
      type: type as NotificationStatisticsType,
      data
    }
  }));
};

export const getStatistics = (notifications: Notification[]) => {
  const {
    BAR_CHART,
    COUNT_LAST_24H,
    COUNT_LAST_WEEK,
    PIE_CHART,
    TOTAL_COUNT,
    LINE_GRAPH
  } = NOTIFICATION_STATISTICS_TYPES;

  const validNotifications = notifications.filter((notification) => {
    return notification.time && !isNaN(new Date(notification.time).getTime());
  });

  const notificationsStatistics: StatisticsObject = {
    [BAR_CHART]: generateBarChartData(validNotifications),
    [COUNT_LAST_WEEK]: calculateCountByTime(validNotifications, 'week'),
    [COUNT_LAST_24H]: calculateCountByTime(validNotifications, '24h'),
    [LINE_GRAPH]: generateLineGraphData(validNotifications),
    [PIE_CHART]: generatePieChartData(validNotifications),
    [TOTAL_COUNT]: calculateTotalCount(validNotifications)
  };

  const statisticsToStore = mapStatisticsToResult(notificationsStatistics);

  return statisticsToStore;
};
