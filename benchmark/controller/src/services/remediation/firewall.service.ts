import { UniqueTraffic } from '../../globalTypes/ndrBenchmarkTypes/trafficPatternTypes';
import {
  FirewallRemediationData,
  Remediation
} from '../../globalTypes/ndrBenchmarkTypes/issueTypes';
import {
  FirewallRule,
  IDENTIFIER_TYPES
} from '../../globalTypes/FirewallTypes';
import {
  FIREWALL_DIRECTIONS,
  POLICY_TYPES
} from '../../globalTypes/ndrBenchmarkTypes/firewall';

export class FirewallRemediationService {
  public createRemediation(
    uniqueTrafficPatterns: UniqueTraffic[]
  ): Remediation {
    const remediationData = this.createFirewallRemediationData(
      uniqueTrafficPatterns
    );
    return {
      type: 'firewall',
      description: 'Firewall remediation',
      data: remediationData
    };
  }

  public createFirewallRemediationData(
    uniqueTrafficPatterns: UniqueTraffic[]
  ): FirewallRemediationData {
    const remediationData: FirewallRemediationData = {};

    // Group traffic patterns by destination entity
    const trafficByDst = this.groupTrafficByDestination(uniqueTrafficPatterns);

    // Process each destination entity
    for (const [entityId, patterns] of Object.entries(trafficByDst)) {
      remediationData[entityId] = this.createRulesForEntity(entityId, patterns);
    }

    return remediationData;
  }

  private groupTrafficByDestination(
    patterns: UniqueTraffic[]
  ): Record<string, UniqueTraffic[]> {
    return patterns.reduce(
      (acc, traffic) => {
        if (!acc[traffic.dst.id]) {
          acc[traffic.dst.id] = [];
        }
        acc[traffic.dst.id].push(traffic);
        return acc;
      },
      {} as Record<string, UniqueTraffic[]>
    );
  }

  private createRulesForEntity(
    entityId: string,
    patterns: UniqueTraffic[]
  ): FirewallRule[] {
    const rules: FirewallRule[] = [];
    const portToIps = this.groupByPortAndCollectIPs(patterns);

    // Get destination IP from the first pattern (they all have same destination)
    const dstIp = patterns[0].dst.addr;

    // Create rules for each port
    for (const [port, ips] of Object.entries(portToIps)) {
      rules.push(
        this.createAllowRule(entityId, port, Array.from(ips), dstIp),
        this.createBlockRule(entityId, port, dstIp)
      );
    }

    return rules;
  }

  /*
   * Groups traffic by port and collects source IPs for each port
   * @param patterns - The traffic patterns to group which are grouped by entity
   * @returns A map of port to set of source IPs
   */
  private groupByPortAndCollectIPs(
    patterns: UniqueTraffic[]
  ): Record<string, Set<string>> {
    return patterns.reduce(
      (acc, pattern) => {
        if (!acc[pattern.port]) {
          acc[pattern.port] = new Set<string>();
        }
        acc[pattern.port].add(pattern.src.addr);
        return acc;
      },
      {} as Record<string, Set<string>>
    );
  }

  private createAllowRule(
    entityId: string,
    port: string,
    ips: string[],
    localIp: string
  ): FirewallRule {
    return {
      policy: POLICY_TYPES.ACCEPT,
      direction: FIREWALL_DIRECTIONS.INBOUND,
      remoteIdentifiers: ips.map((ip) => ({
        type: IDENTIFIER_TYPES.REMOTE_IP,
        value: ip
      })),
      portRanges: [port],
      alert: false,
      isActive: false,
      isSuggested: true,
      notes: '',
      localIdentifier: {
        type: IDENTIFIER_TYPES.LOCAL_IP,
        value: localIp
      },
      entityId
    };
  }

  private createBlockRule(
    entityId: string,
    port: string,
    localIp: string
  ): FirewallRule {
    return {
      policy: POLICY_TYPES.BLOCK,
      direction: FIREWALL_DIRECTIONS.INBOUND,
      remoteIdentifiers: [
        {
          type: IDENTIFIER_TYPES.REMOTE_IP,
          value: '0.0.0.0'
        }
      ],
      portRanges: [port],
      alert: false,
      isActive: false,
      isSuggested: true,
      notes: '',
      localIdentifier: {
        type: IDENTIFIER_TYPES.LOCAL_IP,
        value: localIp
      },
      entityId
    };
  }
}
