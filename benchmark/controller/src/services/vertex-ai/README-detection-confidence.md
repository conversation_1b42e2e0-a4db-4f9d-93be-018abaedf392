# Detection Confidence Scoring with Vertex AI

This system extends your existing Vertex AI integration to provide confidence scoring for security detections based on entity labels, traffic patterns, and detection context.

## Overview

The detection confidence scoring system takes created detections and analyzes them using:
- **Detection data**: Title, category, severity, explanation, tags
- **Related entities**: Primarily their assigned labels (business context)
- **Traffic patterns**: Network communication data that triggered the detection

The system generates a confidence score (0-100) and detailed explanation to help security teams prioritize their response.

## Key Components

### 1. DetectionConfidenceService
- **Location**: `src/services/vertex-ai/detection-confidence.service.ts`
- **Purpose**: Handles the confidence scoring logic and Vertex AI integration
- **Key Methods**:
  - `generateConfidenceScore()`: Score a single detection
  - `generateConfidenceScores()`: Batch process multiple detections

### 2. Vertex AI Model Configuration
- **Location**: `src/services/vertex-ai/config/vertex-client.ts`
- **Model**: `detectionConfidenceModel` using Gemini 1.5 Flash
- **Configuration**: Lower temperature (0.3) for more consistent scoring

### 3. Detection Confidence Control
- **Location**: `src/benchmark/controls/DetectionConfidenceControl.ts`
- **Purpose**: Integrates confidence scoring into your benchmark flow
- **Integration**: Runs after DetectionsControl to enrich detections with confidence scores

### 4. AI Instructions
- **Location**: `src/services/vertex-ai/instructions/detection-confidence.txt`
- **Purpose**: Guides the AI model on how to analyze detections and assign confidence scores
- **Focus**: Emphasizes entity labels as the primary factor for confidence assessment

## Data Flow

```
1. DetectionsControl generates detections with traffic patterns
2. DetectionConfidenceControl processes the detections:
   - Retrieves labels and label assignments from Firestore
   - Calls DetectionConfidenceService for each detection
   - Enriches detections with confidence scores
3. Results are stored in benchmark and can be synced to Firestore
```

## Entity Labels Focus

The system is optimized to focus on entity labels rather than full entity data:

- **Static Labels**: System purpose (e.g., "Database Server", "Web Server")
- **User Labels**: Business context (e.g., "Production", "Finance", "Critical")
- **AI Labels**: Automated classifications

Labels provide the business context needed to assess detection reliability.

## Confidence Scoring Criteria

### High Confidence (80-100)
- Critical infrastructure entities with clear labels
- Well-defined business context through labels
- Suspicious traffic patterns on critical ports
- Strong correlation between detection and entity purpose

### Medium Confidence (40-79)
- Some entity labeling and context
- Potentially suspicious traffic patterns
- Moderate alignment between detection and entity types

### Low Confidence (0-39)
- Minimal or no entity labeling
- Ambiguous traffic patterns
- High likelihood of false positive

## Integration Example

```typescript
// In your benchmark flow
const controls = [
  // ... other controls
  new DetectionsControl(organizationId, true),
  new DetectionConfidenceControl(organizationId) // Add after detections
];

const benchmark = await runBenchmarks(controls, organizationId);

// Access enriched detections with confidence scores
const highConfidenceDetections = benchmark.detections.filter(
  detection => detection.confidence?.confidenceScore >= 80
);
```

## Usage Examples

See `src/services/vertex-ai/examples/detection-confidence-example.ts` for complete usage examples including:
- Single detection confidence scoring
- Batch processing multiple detections
- Filtering by confidence levels

## Benefits

1. **Prioritization**: Focus on high-confidence detections first
2. **False Positive Reduction**: Identify low-confidence detections for review
3. **Context-Aware**: Uses business labels to understand entity importance
4. **Scalable**: Batch processing for multiple detections
5. **Integrated**: Works seamlessly with your existing benchmark flow

## Configuration

The system uses your existing Vertex AI configuration and requires no additional setup beyond the code integration. It automatically:
- Retrieves labels and assignments from Firestore
- Processes detections in the benchmark flow
- Enriches detection data with confidence scores

## Output Format

Each detection gets enriched with a `confidence` object containing:
```typescript
{
  confidenceScore: number;        // 0-100
  explanation: string;            // Detailed reasoning
  riskFactors: string[];         // Specific factors identified
  entityAnalysis: {
    criticalEntities: string[];  // Important entities found
    entityTypes: string[];       // Types of entities involved
  };
}
```

This system provides actionable intelligence to help security teams focus on the most reliable and important detections first.
