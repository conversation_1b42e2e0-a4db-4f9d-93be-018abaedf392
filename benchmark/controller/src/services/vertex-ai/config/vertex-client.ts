import { GenerativeModelPreview, VertexAI } from '@google-cloud/vertexai';
import { readFileSync } from 'fs';
import path from 'path';
import { getProjectID } from '../../../utils/environment';
import {
  detectionConfidenceSchema,
  detectionSchema,
  issueSchema
} from '../models/schemas';
import log from '@logger';

const ISSUE_INSTRUCTION = readFileSync(
  path.resolve(__dirname, '../instructions/issue.txt')
).toString();

const DETECTION_INSTRUCTION = readFileSync(
  path.resolve(__dirname, '../instructions/detection.txt')
).toString();

const DETECTION_CONFIDENCE_INSTRUCTION = readFileSync(
  path.resolve(__dirname, '../instructions/detection-confidence.txt')
).toString();

export const vertexClient = new VertexAI({
  location: 'us-central1',
  project: getProjectID()
});

export const issueModelPromise: Promise<GenerativeModelPreview> = (async () => {
  try {
    const cachedContentModel = await vertexClient.preview.cachedContents.create(
      {
        model: 'gemini-2.5-flash',
        ttl: '600s',
        systemInstruction: ISSUE_INSTRUCTION
      }
    );

    return vertexClient.preview.getGenerativeModel({
      model: 'gemini-2.5-flash',
      cachedContent: cachedContentModel,
      generationConfig: {
        maxOutputTokens: 8192,
        temperature: 1,
        topP: 0.95,
        responseSchema: issueSchema,
        responseMimeType: 'application/json'
      }
    });
  } catch (err) {
    throw err;
  }
})();

export async function getIssueModel() {
  try {
    return await issueModelPromise;
  } catch (err) {
    log.error('Failed to initialize issue model:', err);
    throw err;
  }
}

const detectionConfidenceModelPromise: Promise<GenerativeModelPreview> =
  (async () => {
    try {
      const cachedContentModel =
        await vertexClient.preview.cachedContents.create({
          model: 'gemini-2.5-flash',
          ttl: '600s',
          systemInstruction: DETECTION_CONFIDENCE_INSTRUCTION
        });

      return vertexClient.preview.getGenerativeModel({
        model: 'gemini-2.5-flash',
        cachedContent: cachedContentModel,
        generationConfig: {
          maxOutputTokens: 2048,
          temperature: 0,
          topP: 0.7,
          responseSchema: detectionConfidenceSchema,
          responseMimeType: 'application/json'
        }
      });
    } catch (err) {
      throw err;
    }
  })();

export async function getDetectionConfidenceModel() {
  try {
    return await detectionConfidenceModelPromise;
  } catch (err) {
    log.error('Failed to initialize detection confidence model:', err);
    throw err;
  }
}

export const detectionModel = vertexClient.preview.getGenerativeModel({
  model: 'gemini-1.5-flash-002',
  generationConfig: {
    temperature: 0.3,
    topP: 0.8,
    responseSchema: detectionSchema,
    responseMimeType: 'application/json'
  },
  safetySettings: [],
  systemInstruction: DETECTION_INSTRUCTION
});
