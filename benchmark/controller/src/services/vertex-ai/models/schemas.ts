import { SchemaType } from '@google-cloud/vertexai';

export const issueSchema = {
  type: SchemaType.OBJECT,
  properties: {
    title: {
      type: SchemaType.STRING,
      description: 'The title of the issue.'
    },
    category: {
      type: SchemaType.STRING,
      description: 'The category of the issue.'
    },
    subTitle: {
      type: SchemaType.STRING,
      description: 'A subtitle providing additional context.'
    },
    explanation: {
      type: SchemaType.STRING,
      description: 'Detailed explanation of the issue.'
    },
    severity: {
      type: SchemaType.STRING,
      description:
        "The severity level of the issue. Choose from 'info', 'low', 'medium', 'high', 'critical'.",
      enum: ['info', 'low', 'medium', 'high', 'critical']
    },
    trafficPatternsIds: {
      type: SchemaType.ARRAY,
      description: 'Array of traffic pattern IDs that caused the issue.',
      items: {
        type: SchemaType.STRING,
        description: 'The unique identifier of the traffic pattern'
      }
    }
  },
  required: [
    'title',
    'explanation',
    'severity',
    'trafficPatternsIds',
    'subTitle',
    'category'
  ],
  description: 'Schema for a Vertex Issue Control Result object.'
};

export const detectionSchema = {
  type: SchemaType.ARRAY,
  description: 'Array of security detections generated by VertexAI.',
  items: {
    type: SchemaType.OBJECT,
    properties: {
      title: {
        type: SchemaType.STRING,
        description: 'The title of the detection.'
      },
      subTitle: {
        type: SchemaType.STRING,
        description:
          'A subtitle providing additional context for the detection.'
      },
      explanation: {
        type: SchemaType.STRING,
        description: 'Detailed explanation of the security concern.'
      },
      category: {
        type: SchemaType.STRING,
        description:
          'The category of the detection (e.g., "Data Exfiltration", "Lateral Movement", "Reconnaissance").'
      },
      severity: {
        type: SchemaType.STRING,
        description:
          'The severity level of the detection. Choose from "info", "low", "medium", "high", "critical".',
        enum: ['info', 'low', 'medium', 'high', 'critical']
      },
      relatedTrafficIds: {
        type: SchemaType.ARRAY,
        description: 'Array of traffic event IDs that support this detection.',
        items: {
          type: SchemaType.STRING,
          description: 'The unique identifier of the traffic event'
        }
      },
      confidence: {
        type: SchemaType.NUMBER,
        description:
          'Confidence score indicating how certain the AI is about this detection (0-1).'
      }
    },
    required: [
      'title',
      'subTitle',
      'explanation',
      'category',
      'severity',
      'relatedTrafficIds',
      'confidence'
    ],
    description: 'Schema for a Vertex Detection object.'
  }
};

export const detectionConfidenceSchema = {
  type: SchemaType.OBJECT,
  properties: {
    confidenceScore: {
      type: SchemaType.NUMBER,
      description:
        'Confidence score between 0 and 100 indicating the reliability of the detection.'
    },
    explanation: {
      type: SchemaType.STRING,
      description:
        'Detailed explanation of why this confidence score was assigned, including analysis of entities, labels, and traffic patterns.'
    },
    riskFactors: {
      type: SchemaType.ARRAY,
      description:
        'Array of risk factors that influenced the confidence score.',
      items: {
        type: SchemaType.STRING,
        description: 'Individual risk factor identified in the analysis'
      }
    },
    entityAnalysis: {
      type: SchemaType.OBJECT,
      description: 'Analysis of the entities involved in the detection.',
      properties: {
        criticalEntities: {
          type: SchemaType.ARRAY,
          description: 'List of critical entities identified in the detection.',
          items: {
            type: SchemaType.STRING,
            description: 'Entity name or identifier'
          }
        },
        entityTypes: {
          type: SchemaType.ARRAY,
          description:
            'Types of entities involved (e.g., database, web server, workstation).',
          items: {
            type: SchemaType.STRING,
            description: 'Entity type classification'
          }
        }
      },
      required: ['criticalEntities', 'entityTypes']
    }
  },
  required: ['confidenceScore', 'explanation', 'riskFactors', 'entityAnalysis'],
  description: 'Schema for detection confidence scoring analysis.'
};
