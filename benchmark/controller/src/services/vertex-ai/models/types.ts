export type VertexIssue = {
  title: string;
  subTitle: string;
  explanation: string;
  category: string;
  severity: 'info' | 'low' | 'medium' | 'high' | 'critical';
  trafficPatternsIds: string[];
};

export type VertexDetection = {
  title: string;
  subTitle: string;
  explanation: string;
  category: string;
  severity: 'info' | 'low' | 'medium' | 'high' | 'critical';
  relatedTrafficIds: string[];
  confidence: number;
};

export type DetectionConfidenceInput = {
  detection: {
    id: string;
    title: string;
    subTitle: string;
    explanation: string;
    category: string;
    severity: 'info' | 'low' | 'medium' | 'high' | 'critical';
    tags: string[];
  };
  relatedEntities: {
    id: string;
    name: string;
    type: string;
    common: {
      osName?: string;
      deviceType?: string;
      deployment?: string;
      cloudProvider?: string;
      ipAddresses?: string[];
    };
    labels: string[];
  }[];
  trafficPatterns: {
    id: string;
    src: {
      id: string;
      name: string;
      addr: string;
      process: string;
      type: string;
    };
    dst: {
      id: string;
      name: string;
      addr: string;
      process: string;
      type: string;
    };
    port: number;
    sessionCount: number;
  }[];
};
