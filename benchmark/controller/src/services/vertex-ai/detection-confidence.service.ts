import {
  DetectionConfidence,
  DetectionWithControlResults,
  EndpointEntity,
  Label,
  LabelAssignment
} from '../../globalTypes/ndrBenchmarkTypes';
import { getDetectionConfidenceModel } from './config/vertex-client';
import { DetectionConfidenceInput } from './models/types';
import { StreamGenerateContentResult } from '@google-cloud/vertexai';
import log from '../../logger';


export class DetectionConfidenceService {
  private readonly maxRetries = 3;
  private readonly retryDelay = 1000; // 1 second
  private readonly batchSize = 100; // Process 5 detections at a time
  private readonly requestDelay = 500; // 500ms delay between requests

  constructor(private readonly organizationId: string) {}

  /**
   * Sleep utility for adding delays between requests
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Retry wrapper for Vertex AI requests with exponential backoff
   */
  private async retryWithBackoff<T>(
    operation: () => Promise<T>,
    context: string
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        const isRetryableError = this.isRetryableError(error);

        log.warn(
          `Attempt ${attempt}/${this.maxRetries} failed for ${context}`,
          {
            error: lastError.message,
            retryable: isRetryableError,
            organizationId: this.organizationId
          }
        );

        if (!isRetryableError || attempt === this.maxRetries) {
          throw lastError;
        }

        // Exponential backoff: 1s, 2s, 4s
        const delay = this.retryDelay * Math.pow(2, attempt - 1);
        await this.sleep(delay);
      }
    }

    throw lastError!;
  }

  /**
   * Check if error is retryable (rate limits, temporary failures)
   */
  private isRetryableError(error: any): boolean {
    const errorMessage = error?.message?.toLowerCase() || '';
    const errorCode = error?.code || error?.status;

    // Common retryable conditions
    return (
      errorMessage.includes('rate limit') ||
      errorMessage.includes('quota') ||
      errorMessage.includes('timeout') ||
      errorMessage.includes('temporarily unavailable') ||
      errorMessage.includes('service unavailable') ||
      errorCode === 429 || // Too Many Requests
      errorCode === 503 || // Service Unavailable
      errorCode === 502 || // Bad Gateway
      errorCode === 504 // Gateway Timeout
    );
  }

  /**
   * Resolves entity labels from label assignments and labels data
   */
  private resolveEntityLabels(
    entityId: string,
    labels: Label[],
    labelAssignments: LabelAssignment[]
  ): string[] {
    const assignment = labelAssignments.find((la) => la.entityId === entityId);
    if (!assignment) {
      return [];
    }

    const labelMap = new Map(labels.map((label) => [label.id, label]));
    const entityLabels: string[] = [];

    for (const labelId of assignment.labelIds) {
      const label = labelMap.get(labelId);
      if (label) {
        // Include both the label key and values for context
        entityLabels.push(label.key);
        entityLabels.push(...label.values);
      }
    }

    return entityLabels;
  }

  /**
   * Transforms detection and related data into the format expected by Vertex AI
   * Prioritizes traffic patterns (controlResults) as the primary evidence,
   * with entity labels providing business context
   */
  private prepareDetectionInput(
    detection: DetectionWithControlResults,
    relatedEntities: EndpointEntity[],
    labels: Label[],
    labelAssignments: LabelAssignment[]
  ): DetectionConfidenceInput {
    // Create entity map for quick lookup
    const entityMap = new Map(
      relatedEntities.map((entity) => [entity.id, entity])
    );

    // Transform related entities with focus on labels and minimal context
    const transformedEntities = detection.relatedEntitiesIds
      .map((entityId) => {
        const entity = entityMap.get(entityId);
        if (!entity) {
          log.warn(`Entity ${entityId} not found in related entities`);
          return null;
        }

        const entityLabels = this.resolveEntityLabels(
          entityId,
          labels,
          labelAssignments
        );

        return {
          id: entity.id,
          name: entity.name,
          type: entity.type,
          // Only include essential common properties for context
          common: {
            osName: entity.common?.osName,
            deviceType: entity.common?.deviceType,
            deployment: entity.common?.deployment,
            cloudProvider: entity.common?.cloudProvider,
            ipAddresses: entity.common?.ipAddresses || []
          },
          // This is the main focus - the entity labels
          labels: entityLabels
        };
      })
      .filter((entity) => entity !== null);

    return {
      detection: {
        id: detection.id,
        title: detection.title,
        subTitle: detection.subTitle,
        explanation: detection.explanation,
        category: detection.category,
        severity: detection.severity,
        tags: detection.tags
      },
      relatedEntities: transformedEntities,
      // Traffic patterns are the core evidence - these are the actual network communications
      // that triggered this detection and are critical for confidence assessment
      trafficPatterns: detection.controlResults.map((traffic) => ({
        id: traffic.id,
        src: {
          id: traffic.src.id,
          name: traffic.src.name,
          addr: traffic.src.addr,
          process: traffic.src.process, // Critical: what process initiated this traffic
          type: traffic.src.type,
          cmd: traffic.src.cmd
        },
        dst: {
          id: traffic.dst.id,
          name: traffic.dst.name,
          addr: traffic.dst.addr,
          process: traffic.dst.process, // Critical: what process received this traffic
          type: traffic.dst.type,
          cmd: traffic.dst.cmd
        },
        port: traffic.port, // Critical: what port/service was accessed
        sessionCount: traffic.sessionCount // Critical: frequency indicates legitimacy
      }))
    };
  }

  /**
   * Generates confidence score for a detection using Vertex AI with retry logic
   */
  public async generateConfidenceScore(
    detection: DetectionWithControlResults,
    relatedEntities: EndpointEntity[],
    labels: Label[],
    labelAssignments: LabelAssignment[]
  ): Promise<DetectionConfidence> {
    const detectionConfidenceModel = await getDetectionConfidenceModel();
    const context = `confidence score for detection ${detection.id}`;

    return this.retryWithBackoff(async () => {
      log.info(`Generating confidence score for detection ${detection.id}`);

      const input = this.prepareDetectionInput(
        detection,
        relatedEntities,
        labels,
        labelAssignments
      );

      const resp = await detectionConfidenceModel.generateContent(
        JSON.stringify(input, null, 2)
      );
      if (resp.response.candidates?.[0]?.finishReason == "MAX_TOKENS") {
        log.error(`No confidence score found for detection ${detection.id}, because of MAX_TOKENS`);
        return {} as DetectionConfidence;
      }
      const confidenceContent =
        resp.response.candidates?.[0]?.content?.parts?.[0]?.text || '';

      const confidence = JSON.parse(
        confidenceContent || '{}'
      ) as DetectionConfidence;

      log.info(
        `Generated confidence score ${confidence.confidenceScore} for detection ${detection.id}`,
        {
          detectionId: detection.id,
          confidenceScore: confidence.confidenceScore,
          riskFactorsCount: confidence.riskFactors?.length || 0
        }
      );

      return confidence;
    }, context);
  }

  /**
   * Batch process multiple detections for confidence scoring with rate limiting
   */
  public async generateConfidenceScores(
    detections: DetectionWithControlResults[],
    relatedEntities: EndpointEntity[],
    labels: Label[],
    labelAssignments: LabelAssignment[]
  ): Promise<Map<string, DetectionConfidence>> {
    const results = new Map<string, DetectionConfidence>();

    log.info(
      `Processing ${detections.length} detections for confidence scoring in batches of ${this.batchSize}`
    );

    // Process detections in batches to avoid overwhelming Vertex AI
    for (let i = 0; i < detections.length; i += this.batchSize) {
      const batch = detections.slice(i, i + this.batchSize);
      const batchNumber = Math.floor(i / this.batchSize) + 1;
      const totalBatches = Math.ceil(detections.length / this.batchSize);

      log.info(
        `Processing batch ${batchNumber}/${totalBatches} (${batch.length} detections)`
      );

      // Process batch with limited concurrency
      const batchPromises = batch.map(async (detection, index) => {
        try {
          // Add staggered delay to avoid hitting rate limits
          if (index > 0) {
            await this.sleep(this.requestDelay);
          }

          const confidence = await this.generateConfidenceScore(
            detection,
            relatedEntities,
            labels,
            labelAssignments
          );
          results.set(detection.id, confidence);

          log.debug(
            `Successfully scored detection ${detection.id}: ${confidence.confidenceScore}/100`
          );
        } catch (error) {
          log.error(
            `Failed to generate confidence score for detection ${detection.id}`,
            { error: (error as any)?.message, detectionId: detection.id }
          );
          // Continue processing other detections even if one fails
        }
      });

      // Wait for current batch to complete
      await Promise.all(batchPromises);

      // Add delay between batches if not the last batch
      if (i + this.batchSize < detections.length) {
        log.info(`Waiting ${this.requestDelay}ms before next batch...`);
        await this.sleep(this.requestDelay);
      }
    }

    log.info(
      `Completed confidence scoring for ${results.size}/${detections.length} detections`
    );

    return results;
  }
}
