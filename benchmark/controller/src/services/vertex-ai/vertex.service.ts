import {
  SQLTrafficEvent,
  UniqueTraffic
} from '../../globalTypes/ndrBenchmarkTypes/trafficPatternTypes';
import { detectionModel, getIssueModel } from './config/vertex-client';
import { VertexIssue, VertexDetection } from './models/types';
import { StreamGenerateContentResult } from '@google-cloud/vertexai';
import log from '../../logger';

// Optimal batch size for VertexAI detection processing
// Based on model context limits and typical traffic event size (~500-1000 bytes per event)
const OPTIMAL_BATCH_SIZE = 50;

async function parseVertexResponse(response: StreamGenerateContentResult) {
  let result = '';
  for await (const item of response.stream) {
    result += item['candidates']![0]['content']['parts'].map(
      (part: any) => part['text']
    ).join('');
  }
  return result;
}

export class VertexService {
  public async generateIssue(
    uniqueTraffic: UniqueTraffic[]
  ): Promise<VertexIssue> {
    const issueModel = await getIssueModel();
    const streamingResp = await issueModel.generateContentStream(
      JSON.stringify(uniqueTraffic, null, 2)
    );

    const issueContent = await parseVertexResponse(streamingResp);
    return JSON.parse(issueContent) as VertexIssue;
  }

  public async generateDetections(
    trafficEvents: SQLTrafficEvent[]
  ): Promise<VertexDetection[]> {
    if (trafficEvents.length === 0) {
      return [];
    }

    // Split traffic events into batches
    const batches = this.createBatches(trafficEvents, OPTIMAL_BATCH_SIZE);

    log.info(
      `Processing ${trafficEvents.length} traffic events in ${batches.length} batches of size ${OPTIMAL_BATCH_SIZE}`
    );
    // Process all batches concurrently with Promise.allSettled
    const batchResults = await Promise.allSettled(
      batches.map((batch, batchIndex) => this.processBatch(batch, batchIndex))
    );

    // Collect all successful detections from all batches
    const allDetections: VertexDetection[] = [];

    batchResults.forEach((result, batchIndex) => {
      if (result.status === 'fulfilled') {
        allDetections.push(...result.value);
      }
    });

    log.info(`Generating Detections Finished`, {
      totalDetections: allDetections.length,
      totalBatches: batches.length,
      totalTrafficEvents: trafficEvents.length
    });

    return allDetections;
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  private async processBatch(
    trafficEvents: SQLTrafficEvent[],
    batchIndex: number
  ): Promise<VertexDetection[]> {
    try {
      const streamingResp = await detectionModel.generateContentStream(
        JSON.stringify(trafficEvents, null, 2)
      );

      const detectionContent = await parseVertexResponse(streamingResp);
      const detections = JSON.parse(detectionContent) as VertexDetection[];

      return detections;
    } catch (error) {
      log.error(`Error processing detections batch`, {
        error,
        batchIndex: batchIndex,
        trafficEventsCount: trafficEvents.length
      });
      throw error;
    }
  }
}

// Export the detection confidence service
export { DetectionConfidenceService } from './detection-confidence.service';
