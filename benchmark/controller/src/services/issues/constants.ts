import { Issue, Severity } from '../../globalTypes/ndrBenchmarkTypes';
import {
  IssueType,
  IssueTypeConfig,
  SQLAwsSecurityGroupRuleWithName
} from './types';
import { getPermissivePortExplanationMessage } from './utils';

export const MAX_ISSUES_TO_CLUSTER = 50;

export const ISSUE_TYPES = {
  RULES_WITHOUT_MATCHING_ACCEPTED_TRAFFIC: 'Redundant Security Group Rules',
  INGRESS_SG_RULES_WITH_EXCESSIVE_CIDR_ALLOWANCE: 'Overly Permissive CIDR',
  INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS: 'Unnecessary Open Ports',
  INGRESS_SG_RULE_WITH_EXCESSIVE_SG_ALLOWANCE: 'Excessive Security Group'
} as const;

export const ISSUE_TYPE_TO_TITLE = {
  [ISSUE_TYPES.RULES_WITHOUT_MATCHING_ACCEPTED_TRAFFIC]:
    'Security Group Rule Without Matching Traffic',
  [ISSUE_TYPES.INGRESS_SG_RULES_WITH_EXCESSIVE_CIDR_ALLOWANCE]:
    'Overly Permissive CIDR in Security Group Rule',
  [ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS]:
    'Unnecessary Open Ports in Security Group Rule',
  [ISSUE_TYPES.INGRESS_SG_RULE_WITH_EXCESSIVE_SG_ALLOWANCE]:
    'Excessive Security Group Rule'
};

export const ISSUE_TYPE_TO_SUB_TITLE = {
  [ISSUE_TYPES.RULES_WITHOUT_MATCHING_ACCEPTED_TRAFFIC]:
    'has no matching accepted traffic.',
  [ISSUE_TYPES.INGRESS_SG_RULES_WITH_EXCESSIVE_CIDR_ALLOWANCE]:
    'allows a wide CIDR.',
  [ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS]:
    'has ports with no matching traffic.',
  [ISSUE_TYPES.INGRESS_SG_RULE_WITH_EXCESSIVE_SG_ALLOWANCE]:
    'grants broad access with low activity.'
};

export const ISSUE_TYPE_TO_SEVERITY: Record<string, Severity> = {
  [ISSUE_TYPES.RULES_WITHOUT_MATCHING_ACCEPTED_TRAFFIC]: 'medium',
  [ISSUE_TYPES.INGRESS_SG_RULES_WITH_EXCESSIVE_CIDR_ALLOWANCE]: 'critical',
  [ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS]: 'medium',
  [ISSUE_TYPES.INGRESS_SG_RULE_WITH_EXCESSIVE_SG_ALLOWANCE]: 'critical'
} as const;

export const BASIC_ISSUE_TEMPLATE: Partial<Issue> = {
  category: undefined,
  title: undefined,
  subTitle: undefined,
  explanation: undefined,
  relatedEntitiesIds: [],
  trafficPatterns: [],
  remediation: {
    description: '',
    type: 'firewall',
    data: {}
  },
  recommendation: {
    accessSuggestion: [],
    description: '',
    platformSteps: {}
  },
  status: 'open',
  createdTime: new Date()
};

const getSubTitle = (
  issueType: IssueType,
  rule: SQLAwsSecurityGroupRuleWithName
): string => `Rule \`${rule.id}\` ${ISSUE_TYPE_TO_SUB_TITLE[issueType]}`;

export const ISSUES_TYPE_CONFIGS: Record<IssueType, IssueTypeConfig> = {
  [ISSUE_TYPES.RULES_WITHOUT_MATCHING_ACCEPTED_TRAFFIC]: {
    title:
      ISSUE_TYPE_TO_TITLE[ISSUE_TYPES.RULES_WITHOUT_MATCHING_ACCEPTED_TRAFFIC],
    severity:
      ISSUE_TYPE_TO_SEVERITY[
        ISSUE_TYPES.RULES_WITHOUT_MATCHING_ACCEPTED_TRAFFIC
      ],
    getSubTitle: (rule) =>
      getSubTitle(ISSUE_TYPES.RULES_WITHOUT_MATCHING_ACCEPTED_TRAFFIC, rule),
    getExplanation: (rule) =>
      `Rule \`${rule.id}\` allows \`${rule.direction}\` traffic${
        rule.remote_type === 'security_group'
          ? ` from security group \`${rule.remote}\``
          : ` from IP range \`${rule.remote}\``
      } on ports \`${rule.from_port}-${rule.to_port}\`, but no matching traffic was observed.`
  },

  [ISSUE_TYPES.INGRESS_SG_RULES_WITH_EXCESSIVE_CIDR_ALLOWANCE]: {
    title:
      ISSUE_TYPE_TO_TITLE[
        ISSUE_TYPES.INGRESS_SG_RULES_WITH_EXCESSIVE_CIDR_ALLOWANCE
      ],
    severity:
      ISSUE_TYPE_TO_SEVERITY[
        ISSUE_TYPES.INGRESS_SG_RULES_WITH_EXCESSIVE_CIDR_ALLOWANCE
      ],
    getSubTitle: (rule) =>
      getSubTitle(
        ISSUE_TYPES.INGRESS_SG_RULES_WITH_EXCESSIVE_CIDR_ALLOWANCE,
        rule
      ),
    getExplanation: (rule) =>
      `Rule \`${rule.id}\` is allowing \`${rule.direction}\` traffic from a broad IP range (\`${rule.remote}\`) on ports \`${rule.from_port}-${rule.to_port}\`, which increases the risk of unauthorized access.`
  },

  [ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS]: {
    title:
      ISSUE_TYPE_TO_TITLE[
        ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS
      ],
    severity:
      ISSUE_TYPE_TO_SEVERITY[
        ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS
      ],
    getSubTitle: (rule) =>
      getSubTitle(ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS, rule),
    getExplanation: (rule, trafficPatterns) => {
      return getPermissivePortExplanationMessage(trafficPatterns || [], rule);
    }
  },

  [ISSUE_TYPES.INGRESS_SG_RULE_WITH_EXCESSIVE_SG_ALLOWANCE]: {
    title:
      ISSUE_TYPE_TO_TITLE[
        ISSUE_TYPES.INGRESS_SG_RULE_WITH_EXCESSIVE_SG_ALLOWANCE
      ],
    severity:
      ISSUE_TYPE_TO_SEVERITY[
        ISSUE_TYPES.INGRESS_SG_RULE_WITH_EXCESSIVE_SG_ALLOWANCE
      ],
    getSubTitle: (rule) =>
      getSubTitle(
        ISSUE_TYPES.INGRESS_SG_RULE_WITH_EXCESSIVE_SG_ALLOWANCE,
        rule
      ),
    getExplanation: (rule) => {
      const sgName = rule.security_group_name || rule.security_group_id;
      return `Rule \`${rule.id}\` allows \`${rule.direction}\` traffic from security group \`${sgName}\` on ports \`${rule.from_port}-${rule.to_port}\`, but some entities don't have traffic to this security group.`;
    }
  }
};
