import {
  CloudEntity,
  Issue,
  ISSUE_STATUS,
  UniqueTraffic
} from '../../globalTypes/ndrBenchmarkTypes';
import { BASIC_ISSUE_TEMPLATE, ISSUES_TYPE_CONFIGS } from './constants';
import { IssueType, SQLAwsSecurityGroupRuleWithName } from './types';

export const createIssue = (
  issueType: IssueType,
  entityId: string,
  trafficPatterns: UniqueTraffic[],
  securityGroupRule: SQLAwsSecurityGroupRuleWithName,
  relatedEntities: CloudEntity[]
): Issue => {
  const config = ISSUES_TYPE_CONFIGS[issueType];

  if (!config) {
    throw new Error(`Invalid issue type: ${issueType}`);
  }

  const status = ISSUE_STATUS.OPEN;
  const createdTime = new Date();
  const ruleId = securityGroupRule.id;

  return {
    ...BASIC_ISSUE_TEMPLATE,
    entityId,
    category: issueType,
    title: config.title,
    subTitle: config.getSubTitle(securityGroupRule),
    explanation: config.getExplanation(securityGroupRule, trafficPatterns),
    severity: config.severity,
    trafficPatterns,
    relatedEntitiesIds: relatedEntities.map((entity) => entity.id),
    status,
    createdTime,
    ruleId,
    rule: securityGroupRule
  };
};
