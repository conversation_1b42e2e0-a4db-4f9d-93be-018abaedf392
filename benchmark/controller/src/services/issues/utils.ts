import * as ipaddr from 'ipaddr.js';
import { isEqual } from 'lodash';
import {
  Issue,
  SQLAwsSecurityGroupRule,
  SQLTrafficEvent,
  UniqueTraffic
} from '@globalTypes/ndrBenchmarkTypes';
import { SecurityGroupIssue, SQLAwsSecurityGroupRuleWithName } from './types';

export const MAX_TRAFFIC_PATTERNS_PER_ENTITY = 50;

export const isAddressTypeSameAsRemoteType = (
  address: string,
  remoteType: string
): boolean => {
  try {
    if (!['cidr_ipv4', 'cidr_ipv6'].includes(remoteType)) {
      return false;
    }

    if (remoteType === 'cidr_ipv4') {
      return ipaddr.parse(address).kind() === 'ipv4';
    }

    return ipaddr.parse(address).kind() === 'ipv6';
  } catch (e) {
    console.log(`Error parsing address ${address}`, e);
    return false;
  }
};

export function countIpsInCidr(cidr: string): number {
  try {
    const [ip, prefixLength] = ipaddr.parseCIDR(cidr);

    if (ip.kind() === 'ipv4') {
      const total = 2 ** (32 - prefixLength);
      return total;
    } else if (ip.kind() === 'ipv6') {
      // IPv6 ranges can be huge — this returns a string if it's too big
      const total = BigInt(2) ** BigInt(128 - prefixLength);
      return total > BigInt(Number.MAX_SAFE_INTEGER)
        ? Number.MAX_SAFE_INTEGER
        : Number(total); // cap at max safe integer
    } else {
      return 0;
    }
  } catch (e) {
    console.log('Error parsing CIDR', e);
    return 0; // safty fallback
  }
}

export function isPortInRange(
  port: number,
  from_port: number,
  to_port: number
): boolean {
  return port >= from_port && port <= to_port;
}

export function getRuleOpenPorts(rule: SQLAwsSecurityGroupRule): number[] {
  const fromPort = Number(rule.from_port);
  const toPort = Number(rule.to_port);
  return Array.from({ length: toPort - fromPort + 1 }, (_, i) => fromPort + i);
}

export function getDistinctPorts(
  trafficPatterns: SQLTrafficEvent[] | UniqueTraffic[]
): number[] {
  return (
    Array.from(
      new Set(trafficPatterns?.map((traffic) => Number(traffic.port)))
    ) || []
  );
}

export function getDistinctAddresses(
  direction: 'ingress' | 'egress',
  trafficPatterns: SQLTrafficEvent[]
): string[] {
  return (
    Array.from(
      new Set(
        trafficPatterns?.map((traffic) =>
          direction === 'ingress'
            ? String(traffic.src_addr)
            : String(traffic.dst_addr)
        )
      )
    ) || []
  );
}

export function getDistinctIds(
  direction: 'ingress' | 'egress',
  trafficPatterns: SQLTrafficEvent[]
): string[] {
  return (
    Array.from(
      new Set(
        trafficPatterns?.map((traffic) =>
          direction === 'ingress'
            ? String(traffic.src_id)
            : String(traffic.dst_id)
        )
      )
    ) || []
  );
}

export function getIssuesCountByEntityId(
  issues: Issue[] | SecurityGroupIssue[]
): Record<string, number> {
  try {
    const result: Record<string, number> = {};

    issues.forEach((issue) => {
      // Handle SecurityGroupIssue type
      if ('entitiesIds' in issue) {
        const securityGroupIssue = issue as SecurityGroupIssue;
        securityGroupIssue.entitiesIds.forEach((entityId) => {
          result[entityId] = (result[entityId] || 0) + 1;
        });
      }
      // Handle regular Issue type
      else if ('entityId' in issue) {
        const entityId = issue.entityId;
        result[entityId] = (result[entityId] || 0) + 1;
      }
    });

    return result;
  } catch (error) {
    console.error(
      'Error grouping issues by entity ID for issuesCount collection',
      error
    );
    return {};
  }
}

export function transformSQLTrafficPatterns(
  pattern: SQLTrafficEvent
): UniqueTraffic {
  return {
    id: pattern.id,
    src: {
      id: pattern.src_id || '',
      name: pattern.src_name || '',
      process: pattern.src_process || '',
      type: pattern.src_type || '',
      addr: pattern.src_addr || '',
      cmd: pattern.src_cmd || ''
    },
    dst: {
      id: pattern.dst_id || '',
      name: pattern.dst_name || '',
      process: pattern.dst_process || '',
      type: pattern.dst_type || '',
      addr: pattern.dst_addr || '',
      cmd: pattern.dst_cmd || ''
    },
    sessionCount: pattern.session_count || 0,
    port: Number(pattern.port)
  };
}

export const getPermissivePortExplanationMessage = (
  trafficPatterns: UniqueTraffic[],
  securityGroupRule: SQLAwsSecurityGroupRuleWithName
): string => {
  const distinctPorts = Array.from(
    new Set(trafficPatterns.map((tp) => tp.port).filter(Boolean))
  ).sort((a, b) => Number(a) - Number(b));

  const fromPort = Number(securityGroupRule.from_port);
  const toPort = Number(securityGroupRule.to_port);

  if (distinctPorts.length === 1) {
    return `Rule \`${securityGroupRule.id}\` allows \`${securityGroupRule.direction}\` traffic on ports \`${fromPort}-${toPort}\`, but only port \`${distinctPorts[0]}\` is being used.`;
  }

  if (distinctPorts.length === 2) {
    return `Rule \`${securityGroupRule.id}\` allows \`${securityGroupRule.direction}\` traffic on ports \`${fromPort}-${toPort}\`, but only ports \`${distinctPorts.join(', ')}\` are being used.`;
  }

  return `Rule \`${securityGroupRule.id}\` allows \`${securityGroupRule.direction}\` traffic on ports \`${fromPort}-${toPort}\`, but only a few ports are being used.`;
};

export const isAwsSecurityGroupIssueHasChanged = (
  existingData: Issue,
  newData: Issue
): boolean => {
  const newDataToCompare = {
    entitiesIds: newData.entitiesIds,
    trafficPatterns: newData.trafficPatterns,
    relatedEntitiesIds: newData.relatedEntitiesIds,
    rule: newData.rule
  } as Partial<Issue>;

  const baseExistingDataToCompare = {
    entitiesIds: existingData.entitiesIds,
    trafficPatterns: existingData.trafficPatterns,
    relatedEntitiesIds: existingData.relatedEntitiesIds,
    rule: existingData.rule
  } as Partial<Issue>;

  return !isEqual(baseExistingDataToCompare, newDataToCompare);
};
