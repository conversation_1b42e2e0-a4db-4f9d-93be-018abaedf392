import { SecurityGroupIssueAnalyzer } from '../securityGroupIssues';
import { assertIssueTypes } from './utils/testHelpers';
import { ISSUE_TYPES } from '../constants';
import { Benchmark } from '../../../benchmark/types/benchmarkTypes';
import {
  countIpsInCidr,
  isAddressTypeSameAsRemoteType,
  isAwsSecurityGroupIssueHasChanged,
  transformSQLTrafficPatterns
} from '../utils';
import { isIpInCidr } from '../../../utils/ip';

// Mock the BigQuerySQLControl class
jest.mock('../../../benchmark/controls', () => ({
  BigQuerySQLControl: jest.fn().mockImplementation((query) => {
    return {
      execute: jest.fn().mockResolvedValue([])
    };
  })
}));

// set a mock to the logger
jest.mock('../../../logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  },
  initLogger: jest.fn()
}));

// Create a hard-coded benchmark object
const createDefaultBenchmark = (): Benchmark => ({
  entities: [],
  issues: [],
  trafficPatterns: [],
  notifications: [],
  stats: [],
  labels: [],
  labelAssignments: [],
  detections: [],
  aiDetections: [],
  securityGroups: []
});

describe('SecurityGroupIssueAnalyzer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rules Without Matching Traffic', () => {
    it('should identify rules without traffic', async () => {
      // Arrange - Create a test scenario with 1 entity
      // Create explicit test objects instead of using the generator for better control
      const entityId = 'entity-0';
      const sgId = 'sg-0';
      const sgName = 'Security Group 0';
      const ruleId = 'sgr-0';

      // Create a single entity with a security group
      const entity = {
        id: entityId,
        type: 'aws_ec2_instance',
        name: 'Test Instance',
        info: {
          securityGroups: [{ id: sgId, name: sgName }]
        }
      };

      // Create a security group rule that opens port 22
      const securityGroupRule = {
        id: ruleId,
        security_group_id: sgId,
        security_group_name: sgName,
        direction: 'ingress' as const,
        remote_type: 'cidr_ipv4' as const,
        remote: '*******/0',
        from_port: 22,
        to_port: 22,
        region: 'us-east-1',
        account_id: 'test-account',
        description: 'Test security group rule',
        protocol: 'tcp'
      };

      // Create traffic pattern that uses a completely different port (port 80)
      // This won't match the rule (port 22) when filtered
      const trafficPattern = {
        entity_id: entityId,
        traffic_type: 'ingress',
        tps: [
          {
            src_addr: '************0',
            dst_addr: '********',
            src_id: '***************',
            dst_id: entityId,
            port: 80,
            src_name: 'test-src',
            dst_name: 'test-dst',
            src_process: 'test-src-process',
            dst_process: 'test-dst-process',
            src_type: 'test-src-type',
            dst_type: 'test-dst-type',
            time: new Date(),
            id: 'test-id',
            src_cmd: 'test-src-cmd',
            dst_cmd: 'test-dst-cmd'
          }
        ]
      };

      const entities = [entity];
      const securityGroupRules = [securityGroupRule];
      const trafficPatterns = [trafficPattern];

      // Create a map from the entities array
      const entitiesMap = new Map();
      entities.forEach((entity) => entitiesMap.set(entity.id, entity));

      const benchmark = createDefaultBenchmark();

      const analyzer = new SecurityGroupIssueAnalyzer(
        benchmark,
        securityGroupRules,
        entitiesMap
      );

      // Mock the trafficPatternsControl.execute method
      (analyzer as any).trafficPatternsControl = {
        execute: jest.fn().mockResolvedValue(trafficPatterns)
      };

      const issues = await analyzer.raiseIssues();

      expect(issues).toBeDefined();
      expect(issues.length).toBe(1); // One clustered issue for the rule
      expect(issues[0].category).toBe(
        ISSUE_TYPES.RULES_WITHOUT_MATCHING_ACCEPTED_TRAFFIC
      );
      expect(issues[0].ruleId).toBe(ruleId);
      expect(issues[0].entitiesIds).toEqual([entityId]); // All entities in the cluster
      expect(issues[0].relatedEntitiesIds).toEqual([entityId]);
      // Verify type is security-group-issue
      expect(issues[0].type).toBe('security-group-issue');
      // Check that trafficPatterns is an object with entity IDs as keys
      expect(issues[0].trafficPatterns).toEqual(expect.objectContaining({}));
    });
  });

  describe('Excessive CIDR Allowance', () => {
    it('should identify rules with excessive CIDR', async () => {
      // Arrange - Create a test scenario with 2 entities
      // Create explicit test objects instead of using the generator for better control
      const entityId1 = 'entity-0';
      const entityId2 = 'entity-1';
      const sgId = 'sg-0';
      const sgName = 'Security Group 0';
      const ruleId = 'sgr-0';

      // Create two entities with the same security group
      const entity1 = {
        id: entityId1,
        type: 'aws_ec2_instance',
        name: 'Test Instance 1',
        info: {
          securityGroups: [{ id: sgId, name: sgName }]
        }
      };

      const entity2 = {
        id: entityId2,
        type: 'aws_ec2_instance',
        name: 'Test Instance 2',
        info: {
          securityGroups: [{ id: sgId, name: sgName }]
        }
      };

      // Create a security group rule with a large CIDR range
      const securityGroupRule = {
        id: ruleId,
        security_group_id: sgId,
        security_group_name: sgName,
        direction: 'ingress' as const,
        remote_type: 'cidr_ipv4' as const,
        remote: '***********/16', // Large CIDR range with many potential IPs
        from_port: 80,
        to_port: 80,
        region: 'us-east-1',
        account_id: 'test-account',
        description: 'Test security group rule',
        protocol: 'tcp'
      };

      // Create traffic patterns that only use a small subset of the allowed CIDR range
      const trafficPattern1 = {
        entity_id: entityId1,
        traffic_type: 'ingress',
        tps: [
          {
            src_addr: '************', // Only using one IP from the large range
            dst_addr: '********',
            src_id: 'src-1',
            dst_id: entityId1,
            port: 80,
            src_name: 'test-src-1',
            dst_name: 'test-dst-1',
            src_process: 'test-src-process',
            dst_process: 'test-dst-process',
            src_type: 'test-src-type',
            dst_type: 'test-dst-type',
            time: new Date(),
            id: 'traffic-1',
            src_cmd: 'test-src-cmd',
            dst_cmd: 'test-dst-cmd'
          }
        ]
      };

      const trafficPattern2 = {
        entity_id: entityId2,
        traffic_type: 'ingress',
        tps: [
          {
            src_addr: '***********1', // Only using one IP from the large range
            dst_addr: '********',
            src_id: 'src-2',
            dst_id: entityId2,
            port: 80,
            src_name: 'test-src-2',
            dst_name: 'test-dst-2',
            src_process: 'test-src-process',
            dst_process: 'test-dst-process',
            src_type: 'test-src-type',
            dst_type: 'test-dst-type',
            time: new Date(),
            id: 'traffic-2',
            sessionCount: 0,
            src_cmd: 'test-src-cmd',
            dst_cmd: 'test-dst-cmd'
          }
        ]
      };

      const entities = [entity1, entity2];
      const securityGroupRules = [securityGroupRule];
      const trafficPatterns = [trafficPattern1, trafficPattern2];

      // Create a map from the entities array
      const entitiesMap = new Map();
      entities.forEach((entity) => entitiesMap.set(entity.id, entity));

      const benchmark = createDefaultBenchmark();

      const analyzer = new SecurityGroupIssueAnalyzer(
        benchmark,
        securityGroupRules,
        entitiesMap
      );

      // Mock the trafficPatternsControl.execute method
      (analyzer as any).trafficPatternsControl = {
        execute: jest.fn().mockResolvedValue(trafficPatterns)
      };

      // Act
      const issues = await analyzer.raiseIssues();

      // Assert - Explicit expectations for clustered issues
      expect(issues).toBeDefined();
      expect(issues.length).toBe(1); // One clustered issue for the rule
      expect(issues[0].category).toBe(
        ISSUE_TYPES.INGRESS_SG_RULES_WITH_EXCESSIVE_CIDR_ALLOWANCE
      );
      expect(issues[0].ruleId).toBe(ruleId);

      // The entitiesIds array should contain both entities
      expect(issues[0].entitiesIds.sort()).toEqual(
        [entityId1, entityId2].sort()
      );

      // Verify type is security-group-issue
      expect(issues[0].type).toBe('security-group-issue');

      // Traffic patterns should now be a dictionary with entity IDs as keys
      expect(Object.keys(issues[0].trafficPatterns).sort()).toEqual(
        [entityId1, entityId2].sort()
      );

      // Transform the original traffic patterns for comparison
      const mappedTrafficPattern1 = transformSQLTrafficPatterns(
        trafficPattern1.tps[0]
      );
      const mappedTrafficPattern2 = transformSQLTrafficPatterns(
        trafficPattern2.tps[0]
      );

      // Check that the traffic patterns for each entity are correct
      expect(issues[0].trafficPatterns[entityId1]).toContainEqual(
        mappedTrafficPattern1
      );
      expect(issues[0].trafficPatterns[entityId2]).toContainEqual(
        mappedTrafficPattern2
      );
    });
  });

  describe('Unused Ports', () => {
    it('should identify rules with unused ports', async () => {
      // Arrange - Create a test scenario with 2 entities
      // Create explicit test objects instead of using the generator for better control
      const entityId1 = 'entity-0';
      const entityId2 = 'entity-1';
      const sgId = 'sg-0';
      const sgName = 'Security Group 0';
      const ruleId = 'sgr-0';

      // Create two entities with the same security group
      const entity1 = {
        id: entityId1,
        type: 'aws_ec2_instance',
        name: 'Test Instance 1',
        info: {
          securityGroups: [{ id: sgId, name: sgName }]
        }
      };

      const entity2 = {
        id: entityId2,
        type: 'aws_ec2_instance',
        name: 'Test Instance 2',
        info: {
          securityGroups: [{ id: sgId, name: sgName }]
        }
      };

      // Create a security group rule that allows a wide port range (e.g., 1-1024)
      const securityGroupRule = {
        id: ruleId,
        security_group_id: sgId,
        security_group_name: sgName,
        direction: 'ingress' as const,
        remote_type: 'cidr_ipv4' as const,
        remote: '0.0.0.0/0',
        from_port: 1,
        to_port: 1024, // Wide port range
        region: 'us-east-1',
        account_id: 'test-account',
        description: 'Test security group rule with wide port range',
        protocol: 'tcp'
      };

      // Create traffic patterns that only use a few ports from the allowed range
      const trafficPattern1 = {
        entity_id: entityId1,
        traffic_type: 'ingress',
        tps: [
          {
            src_addr: '************',
            dst_addr: '********',
            src_id: 'src-1',
            dst_id: entityId1,
            port: 80, // Only port 80 is used
            src_name: 'test-src-1',
            dst_name: 'test-dst-1',
            src_process: 'test-src-process',
            dst_process: 'test-dst-process',
            src_type: 'test-src-type',
            dst_type: 'test-dst-type',
            time: new Date(),
            id: 'traffic-1',
            src_cmd: 'test-src-cmd',
            dst_cmd: 'test-dst-cmd'
          }
        ]
      };

      const trafficPattern2 = {
        entity_id: entityId2,
        traffic_type: 'ingress',
        tps: [
          {
            src_addr: '***********1',
            dst_addr: '********',
            src_id: 'src-2',
            dst_id: entityId2,
            port: 443, // Only port 443 is used
            src_name: 'test-src-2',
            dst_name: 'test-dst-2',
            src_process: 'test-src-process',
            dst_process: 'test-dst-process',
            src_type: 'test-src-type',
            dst_type: 'test-dst-type',
            time: new Date(),
            id: 'traffic-2',
            sessionCount: 0,
            src_cmd: 'test-src-cmd',
            dst_cmd: 'test-dst-cmd'
          }
        ]
      };

      const entities = [entity1, entity2];
      const securityGroupRules = [securityGroupRule];
      const trafficPatterns = [trafficPattern1, trafficPattern2];

      // Create a map from the entities array
      const entitiesMap = new Map();
      entities.forEach((entity) => entitiesMap.set(entity.id, entity));

      const benchmark = createDefaultBenchmark();

      const analyzer = new SecurityGroupIssueAnalyzer(
        benchmark,
        securityGroupRules,
        entitiesMap
      );

      // Mock the trafficPatternsControl.execute method
      (analyzer as any).trafficPatternsControl = {
        execute: jest.fn().mockResolvedValue(trafficPatterns)
      };

      // Act
      const issues = await analyzer.raiseIssues();

      // Filter the issues to get only unused ports issues
      const unusedPortsIssues = issues.filter(
        (issue: any) =>
          issue.category ===
          ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS
      );

      // Assert - Explicit expectations for clustered issues
      expect(unusedPortsIssues).toBeDefined();
      expect(unusedPortsIssues.length).toBe(1); // One clustered issue for the rule
      expect(unusedPortsIssues[0].category).toBe(
        ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS
      );
      expect(unusedPortsIssues[0].ruleId).toBe(ruleId);

      // The entitiesIds array should contain both entities
      expect(unusedPortsIssues[0].entitiesIds.sort()).toEqual(
        [entityId1, entityId2].sort()
      );

      // Verify type is security-group-issue
      expect(unusedPortsIssues[0].type).toBe('security-group-issue');

      // Traffic patterns should now be a dictionary with entity IDs as keys
      expect(Object.keys(unusedPortsIssues[0].trafficPatterns).sort()).toEqual(
        [entityId1, entityId2].sort()
      );

      // Check that the traffic patterns for each entity exist
      expect(unusedPortsIssues[0].trafficPatterns[entityId1]).toBeDefined();
      expect(unusedPortsIssues[0].trafficPatterns[entityId2]).toBeDefined();

      assertIssueTypes(unusedPortsIssues, [
        ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS
      ]);
    });
  });

  describe('Excessive Security Group Allowance', () => {
    it('should identify rules with excessive security group allowance', async () => {
      // Arrange - Create a test scenario with multiple entities
      // Create explicit test objects instead of using the generator for better control
      const entityId1 = 'entity-1';
      const entityId2 = 'entity-2';
      const entityId3 = 'entity-3';
      const sgId1 = 'sg-0';
      const sgId2 = 'sg-remote'; // This is the remote security group
      const sgName1 = 'Security Group 0';
      const sgName2 = 'Remote Security Group';
      const ruleId = 'sgr-0';

      // Create three entities with the first security group
      const entity1 = {
        id: entityId1,
        type: 'aws_ec2_instance',
        name: 'Test Instance 1',
        info: {
          securityGroups: [{ id: sgId1, name: sgName1 }]
        }
      };

      const entity2 = {
        id: entityId2,
        type: 'aws_ec2_instance',
        name: 'Test Instance 2',
        info: {
          securityGroups: [{ id: sgId1, name: sgName1 }]
        }
      };

      const entity3 = {
        id: entityId3,
        type: 'aws_ec2_instance',
        name: 'Test Instance 3',
        info: {
          securityGroups: [{ id: sgId1, name: sgName1 }]
        }
      };

      // Create two more entities that are in the remote security group
      const remoteEntity1 = {
        id: 'remote-entity-1',
        type: 'aws_ec2_instance',
        name: 'Remote Instance 1',
        info: {
          securityGroups: [{ id: sgId2, name: sgName2 }]
        }
      };

      const remoteEntity2 = {
        id: 'remote-entity-2',
        type: 'aws_ec2_instance',
        name: 'Remote Instance 2',
        info: {
          securityGroups: [{ id: sgId2, name: sgName2 }]
        }
      };

      // Create a security group rule that references another security group
      const securityGroupRule = {
        id: ruleId,
        security_group_id: sgId1,
        security_group_name: sgName1,
        direction: 'ingress' as const,
        remote_type: 'security_group' as const, // This is the special case
        remote: sgId2, // References the remote security group
        from_port: 443,
        to_port: 443,
        region: 'us-east-1',
        account_id: 'test-account',
        description: 'Security group reference rule',
        protocol: 'tcp'
      };

      // Create traffic patterns that only use one of the remote entities
      // Even though the rule allows traffic from both remote entities
      const trafficPattern1 = {
        entity_id: entityId1,
        traffic_type: 'ingress',
        tps: [
          {
            src_addr: '**********',
            dst_addr: '********',
            src_id: 'remote-entity-1', // Only traffic from one of the remote entities
            dst_id: entityId1,
            port: 443,
            src_name: 'Remote Instance 1',
            dst_name: 'Test Instance 1',
            src_process: 'test-src-process',
            dst_process: 'test-dst-process',
            src_type: 'test-src-type',
            dst_type: 'test-dst-type',
            time: new Date(),
            id: 'traffic-1',
            src_cmd: 'test-src-cmd',
            dst_cmd: 'test-dst-cmd'
          }
        ]
      };

      const trafficPattern2 = {
        entity_id: entityId2,
        traffic_type: 'ingress',
        tps: [
          {
            src_addr: '**********',
            dst_addr: '********',
            src_id: 'remote-entity-1', // Only traffic from one of the remote entities
            dst_id: entityId2,
            port: 443,
            src_name: 'Remote Instance 1',
            dst_name: 'Test Instance 2',
            src_process: 'test-src-process',
            dst_process: 'test-dst-process',
            src_type: 'test-src-type',
            dst_type: 'test-dst-type',
            time: new Date(),
            id: 'traffic-2',
            sessionCount: 0,
            src_cmd: 'test-src-cmd',
            dst_cmd: 'test-dst-cmd'
          }
        ]
      };

      const trafficPattern3 = {
        entity_id: entityId3,
        traffic_type: 'ingress',
        tps: [
          {
            src_addr: '**********',
            dst_addr: '10.0.0.3',
            src_id: 'remote-entity-1', // Only traffic from one of the remote entities
            dst_id: entityId3,
            port: 443,
            src_name: 'Remote Instance 1',
            dst_name: 'Test Instance 3',
            src_process: 'test-src-process',
            dst_process: 'test-dst-process',
            src_type: 'test-src-type',
            dst_type: 'test-dst-type',
            time: new Date(),
            id: 'traffic-3',
            src_cmd: 'test-src-cmd',
            dst_cmd: 'test-dst-cmd'
          }
        ]
      };

      const entities = [
        entity1,
        entity2,
        entity3,
        remoteEntity1,
        remoteEntity2
      ];
      const securityGroupRules = [securityGroupRule];
      const trafficPatterns = [
        trafficPattern1,
        trafficPattern2,
        trafficPattern3
      ];

      // Create a map from the entities array
      const entitiesMap = new Map();
      entities.forEach((entity) => entitiesMap.set(entity.id, entity));

      const benchmark = createDefaultBenchmark();

      const analyzer = new SecurityGroupIssueAnalyzer(
        benchmark,
        securityGroupRules,
        entitiesMap
      );

      // Mock the trafficPatternsControl.execute method
      (analyzer as any).trafficPatternsControl = {
        execute: jest.fn().mockResolvedValue(trafficPatterns)
      };

      // Act
      const issues = await analyzer.raiseIssues();

      // Assert - Explicit expectations for clustered issues
      expect(issues).toBeDefined();
      expect(issues.length).toBe(1); // One clustered issue for the rule

      // The issue should be of the expected type
      assertIssueTypes(issues, [
        ISSUE_TYPES.INGRESS_SG_RULE_WITH_EXCESSIVE_SG_ALLOWANCE
      ]);

      // The entitiesIds array should contain all three entities
      expect(issues[0].entitiesIds.sort()).toEqual(
        [entityId1, entityId2, entityId3].sort()
      );

      // The issue should reference the security group rule
      expect(issues[0].ruleId).toBe(ruleId);

      // Verify type is security-group-issue
      expect(issues[0].type).toBe('security-group-issue');

      // Traffic patterns should now be a dictionary with entity IDs as keys
      expect(Object.keys(issues[0].trafficPatterns).sort()).toEqual(
        [entityId1, entityId2, entityId3].sort()
      );

      // Check that traffic patterns exist for each entity
      expect(issues[0].trafficPatterns[entityId1]).toBeDefined();
      expect(issues[0].trafficPatterns[entityId2]).toBeDefined();
      expect(issues[0].trafficPatterns[entityId3]).toBeDefined();
    });
  });

  describe('isIpInCidr', () => {
    test('returns true for IPv4 inside CIDR', () => {
      expect(isIpInCidr('************', '***********/24')).toBe(true);
    });

    test('returns false for IPv4 outside CIDR', () => {
      expect(isIpInCidr('********', '***********/24')).toBe(false);
    });

    test('returns true for IPv6 inside CIDR', () => {
      expect(isIpInCidr('2001:db8::1', '2001:db8::/32')).toBe(true);
    });

    test('returns false for IPv6 outside CIDR', () => {
      expect(isIpInCidr('2001:db9::1', '2001:db8::/32')).toBe(false);
    });

    test('returns false for IPv4 in IPv6-only CIDR', () => {
      expect(isIpInCidr('**************', '::/0')).toBe(false);
    });

    test('returns false on invalid IP', () => {
      expect(isIpInCidr('invalid-ip', '***********/24')).toBe(false);
    });

    test('returns false on invalid CIDR', () => {
      expect(isIpInCidr('***********', 'invalid-cidr')).toBe(false);
    });

    test('returns true for IPv4 inside 0.0.0.0/0', () => {
      expect(isIpInCidr('*******', '0.0.0.0/0')).toBe(true);
      expect(isIpInCidr('127.0.0.1', '0.0.0.0/0')).toBe(true);
      expect(isIpInCidr('***************', '0.0.0.0/0')).toBe(true);
    });

    test('returns false for IPv6 in 0.0.0.0/0', () => {
      expect(isIpInCidr('::1', '0.0.0.0/0')).toBe(false);
      expect(isIpInCidr('2001:db8::1', '0.0.0.0/0')).toBe(false);
    });

    test('IPv4: /32 exact match', () => {
      expect(isIpInCidr('***********', '***********/32')).toBe(true);
    });

    // IPv4 edge case: highest IP in range
    test('IPv4: last IP in /30 block', () => {
      expect(isIpInCidr('***********', '***********/30')).toBe(true);
    });

    // IPv6 short notation inside /64
    test('IPv6: short format inside /64', () => {
      expect(isIpInCidr('2001:db8::abcd', '2001:db8::/64')).toBe(true);
    });

    // IPv6 upper boundary inside CIDR
    test('IPv6: high end of /127', () => {
      expect(isIpInCidr('2001:db8::1', '2001:db8::/127')).toBe(true);
    });

    // Entire IPv4 space
    test('IPv4: inside 0.0.0.0/0', () => {
      expect(isIpInCidr('*******', '0.0.0.0/0')).toBe(true);
    });

    // Entire IPv6 space
    test('IPv6: inside ::/0', () => {
      expect(isIpInCidr('2001:4860:4860::8888', '::/0')).toBe(true);
    });
  });

  describe('countIpsInCidr', () => {
    test('counts IPv4 /24 range', () => {
      expect(countIpsInCidr('***********/24')).toBe(256);
    });

    test('counts IPv4 /32 (single address)', () => {
      expect(countIpsInCidr('********/32')).toBe(1);
    });

    test('counts IPv4 /0 (entire IPv4 space)', () => {
      expect(countIpsInCidr('0.0.0.0/0')).toBe(4294967296);
    });

    test('counts small IPv6 range (/120)', () => {
      expect(countIpsInCidr('2001:db8::/120')).toBe(256);
    });

    test('counts IPv6 /128 (single address)', () => {
      expect(countIpsInCidr('2001:db8::1/128')).toBe(1);
    });

    test('counts IPv6 /64 (large but under safe integer)', () => {
      expect(countIpsInCidr('2001:db8::/64')).toBeLessThanOrEqual(
        Number.MAX_SAFE_INTEGER
      );
    });

    test('caps huge IPv6 range (/0) at MAX_SAFE_INTEGER', () => {
      expect(countIpsInCidr('::/0')).toBe(Number.MAX_SAFE_INTEGER);
    });

    test('returns 0 for invalid CIDR', () => {
      expect(countIpsInCidr('not-a-cidr')).toBe(0);
    });

    test('returns 0 for malformed CIDR prefix', () => {
      expect(countIpsInCidr('***********/abc')).toBe(0);
    });
  });

  describe('isAddressTypeSameAsRemoteType', () => {
    test('returns true for IPv4 address and cidr_ipv4 type', () => {
      expect(isAddressTypeSameAsRemoteType('***********', 'cidr_ipv4')).toBe(
        true
      );
    });

    test('returns false for IPv6 address and cidr_ipv4 type', () => {
      expect(isAddressTypeSameAsRemoteType('2001:db8::1', 'cidr_ipv4')).toBe(
        false
      );
    });

    test('returns true for IPv6 address and non-cidr_ipv4 type', () => {
      expect(isAddressTypeSameAsRemoteType('2001:db8::1', 'cidr_ipv6')).toBe(
        true
      );
    });

    test('returns false for IPv4 address and non-cidr_ipv4 type', () => {
      expect(isAddressTypeSameAsRemoteType('********', 'cidr_ipv6')).toBe(
        false
      );
    });

    test('returns true for IPv4-mapped IPv6 address and cidr_ipv6 type', () => {
      expect(
        isAddressTypeSameAsRemoteType('::ffff:***********', 'cidr_ipv6')
      ).toBe(true);
    });

    test('returns false for IPv4-mapped IPv6 address and cidr_ipv4 type', () => {
      expect(
        isAddressTypeSameAsRemoteType('::ffff:***********', 'cidr_ipv4')
      ).toBe(false);
    });

    test('returns false for invalid IP input', () => {
      expect(isAddressTypeSameAsRemoteType('invalid-ip', 'cidr_ipv4')).toBe(
        false
      );
    });

    test('returns true for loopback IPv4 and cidr_ipv4', () => {
      expect(isAddressTypeSameAsRemoteType('127.0.0.1', 'cidr_ipv4')).toBe(
        true
      );
    });

    test('returns true for loopback IPv6 and cidr_ipv6', () => {
      expect(isAddressTypeSameAsRemoteType('::1', 'cidr_ipv6')).toBe(true);
    });
  });

  describe('isAwsSecurityGroupIssueHasChanged', () => {
    const baseIssue = {
      entitiesIds: ['i-123', 'i-456'],
      trafficPatterns: {
        'i-123': [{ port: 22, protocol: 'tcp' }],
        'i-456': [{ port: 80, protocol: 'tcp' }]
      },
      relatedEntitiesIds: ['sg-aaa', 'sg-bbb'],
      rule: {
        portRange: [22, 80],
        protocol: 'tcp',
        cidr: '0.0.0.0/0'
      },
      // additional irrelevant fields
      createdTime: new Date(),
      updatedTime: new Date(),
      type: 'AWS'
    };

    it('returns false when all compared fields are equal', () => {
      const existing = { ...baseIssue } as any;
      const current = { ...baseIssue } as any;
      expect(isAwsSecurityGroupIssueHasChanged(existing, current)).toBe(false);
    });

    it('returns true when entitiesIds has changed', () => {
      const existing = { ...baseIssue } as any;
      const current = {
        ...baseIssue,
        entitiesIds: ['i-123', 'i-789'] // changed
      } as any;
      expect(isAwsSecurityGroupIssueHasChanged(existing, current)).toBe(true);
    });

    it('returns true when trafficPatterns has changed', () => {
      const existing = { ...baseIssue } as any;
      const current = {
        ...baseIssue,
        trafficPatterns: {
          ...baseIssue.trafficPatterns,
          'i-456': [{ port: 443, protocol: 'tcp' }] // changed port
        }
      } as any;
      expect(isAwsSecurityGroupIssueHasChanged(existing, current)).toBe(true);
    });

    it('returns true when relatedEntitiesIds has changed', () => {
      const existing = { ...baseIssue } as any;
      const current = {
        ...baseIssue,
        relatedEntitiesIds: ['sg-aaa', 'sg-ccc'] // changed
      } as any;
      expect(isAwsSecurityGroupIssueHasChanged(existing, current)).toBe(true);
    });

    it('returns true when rule has changed', () => {
      const existing = { ...baseIssue } as any;
      const current = {
        ...baseIssue,
        rule: {
          ...baseIssue.rule,
          cidr: '***********/24' // changed
        }
      } as any;
      expect(isAwsSecurityGroupIssueHasChanged(existing, current)).toBe(true);
    });

    it('returns false when non-compared fields change', () => {
      const existing = { ...baseIssue } as any;
      const current = {
        ...baseIssue,
        updatedTime: new Date(Date.now() + 1000), // updated field not being compared
        type: 'AWS'
      } as any;
      expect(isAwsSecurityGroupIssueHasChanged(existing, current)).toBe(false);
    });

    it('returns true when relatedEntitiesIds has different values', () => {
      const existing = {
        ...baseIssue,
        relatedEntities: [{ id: 'sg-aaa' }, { id: 'sg-bbb' }]
      } as any;

      const current = {
        ...baseIssue,
        relatedEntitiesIds: ['sg-aaa', 'sg-ccc'] // different value
      } as any;
      expect(isAwsSecurityGroupIssueHasChanged(existing, current)).toBe(true);
    });
  });
});
