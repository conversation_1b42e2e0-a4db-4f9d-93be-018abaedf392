import { Issue } from '../../../../globalTypes/ndrBenchmarkTypes';
import { ISSUE_TYPES } from '../../constants';
import { IssueType } from '../../types';

/**
 * Returns an array of all the issue types for easier assertion
 */
export const getIssueTypes = (): IssueType[] => {
  return Object.values(ISSUE_TYPES);
};

/**
 * Asserts that the given issues match the expected types
 */
export const assertIssueTypes = (
  issues: Issue[],
  expectedTypes: IssueType[]
): void => {
  const issueTypes = issues.map((issue) => issue.category);

  // Check if all expected types are present
  for (const expectedType of expectedTypes) {
    expect(issueTypes).toContain(expectedType);
  }

  // Check if there are no unexpected types
  for (const actualType of issueTypes) {
    expect(expectedTypes).toContain(actualType);
  }
};

/**
 * Asserts that the given issues match the expected entity IDs
 */
export const assertEntityIds = (
  issues: Issue[],
  expectedEntityIds: string[]
): void => {
  // Extract all entity IDs from the issues
  const entityIds = new Set<string>();

  issues.forEach((issue) => {
    // For security-group-issue type, use entitiesIds
    if (
      'type' in issue &&
      issue.type === 'security-group-issue' &&
      'entitiesIds' in issue
    ) {
      const entitiesIds = issue.entitiesIds || [];
      entitiesIds.forEach((id) => entityIds.add(id));
    }
    // For regular issues, use entityId
    else if ('entityId' in issue) {
      entityIds.add(issue.entityId);
    }
  });

  const uniqueEntityIds = Array.from(entityIds);

  // Check if all expected entity IDs are present
  for (const expectedEntityId of expectedEntityIds) {
    expect(uniqueEntityIds).toContain(expectedEntityId);
  }

  // Check if there are no unexpected entity IDs
  for (const actualEntityId of uniqueEntityIds) {
    expect(expectedEntityIds).toContain(actualEntityId);
  }
};

/**
 * Extracts a unique list of rule IDs from the issues
 */
export const extractRuleIds = (issues: Issue[]): string[] => {
  return [
    ...new Set(issues.map((issue) => issue.ruleId).filter(Boolean) as string[])
  ];
};

/**
 * Counts issues by category
 */
export const countIssuesByCategory = (
  issues: Issue[]
): Record<string, number> => {
  return issues.reduce(
    (acc, issue) => {
      const category = issue.category;
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );
};

/**
 * Filters issues by category
 */
export const filterIssuesByCategory = (
  issues: Issue[],
  category: IssueType
): Issue[] => {
  return issues.filter((issue) => issue.category === category);
};

/**
 * Asserts that a rule ID is contained in the issues
 */
export const assertRuleIdInIssues = (issues: Issue[], ruleId: string): void => {
  const hasRule = issues.some((issue) => issue.ruleId === ruleId);
  expect(hasRule).toBe(true);
};
