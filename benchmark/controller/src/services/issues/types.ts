import {
  BaseEntity,
  Issue,
  IssueStatus,
  Recommendation,
  Remediation,
  Severity,
  SQLAwsSecurityGroupRule,
  SQLTrafficEvent,
  UniqueTraffic
} from '../../globalTypes/ndrBenchmarkTypes';
import { ISSUE_TYPES } from './constants';

export type IssueType = (typeof ISSUE_TYPES)[keyof typeof ISSUE_TYPES];

export interface AggregatedTrafficPattern {
  traffic_type: 'ingress' | 'egress';
  entity_id: string;
  tps: SQLTrafficEvent[];
}

export interface IssueTypeConfig {
  title: string;
  severity: Severity;
  getSubTitle: (securityGroupRule: SQLAwsSecurityGroupRuleWithName) => string;
  getExplanation: (
    securityGroupRule: SQLAwsSecurityGroupRuleWithName,
    trafficPatterns?: UniqueTraffic[]
  ) => string;
}

export type IssuesDictionary = Record<IssueType, Issue[]>;
export type EntityDirectionedTraffic = {
  entityIngressTraffic: SQLTrafficEvent[];
  entityEgressTraffic: SQLTrafficEvent[];
};

export type IssuesCluster = {
  category: string;
  ruleId: string;
  title: string;
  subTitle: string;
  explaination: string;
  entitiesIds: Set<string>;
  relatedEntitiesIds: string[];
  severity: Severity;
  trafficPatterns: Record<string, UniqueTraffic[]>;
};

export interface SQLAwsSecurityGroupRuleWithName
  extends SQLAwsSecurityGroupRule {
  security_group_name: string;
}

export type SecurityGroupIssue = {
  entitiesIds: string[];
  category: string;
  title: string;
  subTitle: string;
  explanation: string;
  severity: Severity;
  relatedEntities?: BaseEntity[]; // deprecated
  relatedEntitiesIds: string[];
  ruleId: string;
  trafficPatterns: Record<string, UniqueTraffic[]>;
  recommendation?: Recommendation;
  remediation?: Remediation;
  status: IssueStatus;
  rule: SQLAwsSecurityGroupRuleWithName;
  type: 'security-group-issue';
  createdTime?: Date;
  updatedTime?: Date;
};
