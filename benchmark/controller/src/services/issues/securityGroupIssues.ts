import {
  SQLAwsSecurityGroupRule,
  CloudEntity,
  Issue,
  UniqueTraffic,
  AWSEndpointInfo,
  SQLTrafficEvent,
  BaseEntity
} from '@globalTypes/ndrBenchmarkTypes';
import {
  AggregatedTrafficPattern,
  EntityDirectionedTraffic,
  IssuesCluster,
  IssuesDictionary,
  IssueType,
  SQLAwsSecurityGroupRuleWithName,
  SecurityGroupIssue
} from './types';
import { BigQuerySQLControl } from '../../benchmark/controls';
import { Benchmark } from '../../benchmark/types/benchmarkTypes';
import { ISSUE_TYPES } from './constants';
import log from '../../logger';

import * as securityGroupUtils from './utils';
import { createIssue } from './issueCreator';
import * as ipUtils from '../../utils/ip';

export class SecurityGroupIssueAnalyzer {
  private benchmark: Benchmark;
  private securityGroupsRules: SQLAwsSecurityGroupRuleWithName[];
  private entitiesMap: Map<string, CloudEntity>;
  private trafficPatternsControl: BigQuerySQLControl;

  constructor(
    benchmark: Benchmark,
    securityGroupsRules: SQLAwsSecurityGroupRuleWithName[],
    entitiesMap: Map<string, CloudEntity>
  ) {
    this.benchmark = benchmark;
    this.securityGroupsRules = securityGroupsRules;
    this.entitiesMap = entitiesMap;

    this.trafficPatternsControl = new BigQuerySQLControl(
      'traffic-patterns/traffic_patterns.sql'
    );
  }

  private extractRelatedEntities(
    trafficPatterns: UniqueTraffic[],
    entityId: string,
    entitiesMap: Map<string, CloudEntity>
  ): CloudEntity[] {
    if (trafficPatterns.length === 0) {
      const entity = entitiesMap.get(entityId);

      if (entity) {
        return [entity];
      }

      return [];
    }
    const entitiesIds = Array.from(
      new Set(
        trafficPatterns.flatMap((trafficPattern) => [
          trafficPattern.src.id,
          trafficPattern.dst.id
        ])
      )
    );
    return [
      ...new Set(
        entitiesIds
          .filter((id) => id !== undefined && id !== null)
          .map((id) => entitiesMap.get(id))
          .filter((entity): entity is CloudEntity => entity !== undefined)
      )
    ];
  }

  private createIssue(
    entityId: string,
    issueType: IssueType,
    sqlTrafficPatterns: SQLTrafficEvent[],
    entitiesMap: Map<string, CloudEntity>,
    securityGroupRule: SQLAwsSecurityGroupRuleWithName
  ): Issue {
    const trafficPatterns = sqlTrafficPatterns.map(
      securityGroupUtils.transformSQLTrafficPatterns
    );
    const relatedEntities = this.extractRelatedEntities(
      trafficPatterns,
      entityId,
      entitiesMap
    );

    return createIssue(
      issueType,
      entityId,
      trafficPatterns,
      securityGroupRule,
      relatedEntities
    );
  }

  private getEntityToSecurityGroupRuleMap(): Map<
    string,
    SQLAwsSecurityGroupRuleWithName[]
  > {
    const entityToSecurityGroupRuleMap = new Map<
      string,
      SQLAwsSecurityGroupRuleWithName[]
    >();

    for (const [entityId, entity] of this.entitiesMap) {
      const entitySecurityGroups =
        (entity?.info as AWSEndpointInfo)?.securityGroups?.map((sg) => sg.id) ||
        [];

      const securityGroupRules = this.securityGroupsRules.filter(
        (rule) =>
          rule.remote_type !== 'security_group' &&
          entitySecurityGroups.includes(rule.security_group_id)
      );

      entityToSecurityGroupRuleMap.set(entityId, securityGroupRules);
    }

    return entityToSecurityGroupRuleMap;
  }

  private createSecurityGroupFilter(
    rule: SQLAwsSecurityGroupRule,
    isIngress: boolean,
    fromPort: number,
    toPort: number
  ) {
    return (traffic: SQLTrafficEvent): boolean => {
      if (
        !securityGroupUtils.isPortInRange(
          Number(traffic.port),
          fromPort,
          toPort
        )
      ) {
        return false;
      }

      const entityIdToCheck = isIngress ? traffic.src_id : traffic.dst_id;
      const remoteEntity = this.entitiesMap.get(String(entityIdToCheck));

      if (!remoteEntity) {
        return false;
      }

      const securityGroups = (remoteEntity.info as AWSEndpointInfo)
        ?.securityGroups;
      if (!securityGroups?.length) {
        return false;
      }

      return securityGroups.some((sg) => sg.id === rule.remote);
    };
  }

  private createCidrFilter(
    rule: SQLAwsSecurityGroupRule,
    isIngress: boolean,
    fromPort: number,
    toPort: number
  ) {
    return (traffic: SQLTrafficEvent): boolean => {
      if (
        !securityGroupUtils.isPortInRange(
          Number(traffic.port),
          fromPort,
          toPort
        )
      ) {
        return false;
      }

      const addressToCheck = isIngress
        ? String(traffic.src_addr)
        : String(traffic.dst_addr);

      if (
        !securityGroupUtils.isAddressTypeSameAsRemoteType(
          addressToCheck,
          rule.remote_type
        )
      ) {
        return false;
      }

      return ipUtils.isIpInCidr(addressToCheck, rule.remote);
    };
  }

  private filterTrafficPatternsByRule(
    rule: SQLAwsSecurityGroupRule,
    entityDirectionsTraffic: EntityDirectionedTraffic
  ): SQLTrafficEvent[] {
    const { entityIngressTraffic, entityEgressTraffic } =
      entityDirectionsTraffic;
    const trafficPatterns =
      rule.direction === 'ingress' ? entityIngressTraffic : entityEgressTraffic;

    if (!trafficPatterns?.length) {
      return [];
    }

    const fromPort = Number(rule.from_port);
    const toPort = Number(rule.to_port);
    const isIngress = rule.direction === 'ingress';
    const isSecurityGroupRule = rule.remote_type === 'security_group';

    const filterFn = isSecurityGroupRule
      ? this.createSecurityGroupFilter(rule, isIngress, fromPort, toPort)
      : this.createCidrFilter(rule, isIngress, fromPort, toPort);

    return trafficPatterns.filter(filterFn);
  }

  private buildTrafficIndex(
    trafficPatterns: AggregatedTrafficPattern[]
  ): Map<string, Map<string, SQLTrafficEvent[]>> {
    const trafficIndex = new Map<string, Map<string, SQLTrafficEvent[]>>();

    for (const traffic of trafficPatterns) {
      if (!trafficIndex.has(traffic.entity_id)) {
        trafficIndex.set(traffic.entity_id, new Map());
      }

      const entityMap = trafficIndex.get(traffic.entity_id)!;
      entityMap.set(traffic.traffic_type, traffic.tps || []);
    }

    return trafficIndex;
  }

  private getEntityTrafficByDirection(
    entityId: string,
    trafficIndex: Map<string, Map<string, SQLTrafficEvent[]>>
  ): EntityDirectionedTraffic {
    const entityTraffic = trafficIndex.get(entityId);

    return {
      entityIngressTraffic: entityTraffic?.get('ingress') || [],
      entityEgressTraffic: entityTraffic?.get('egress') || []
    };
  }

  private checkRuleForMatchingTraffic(
    entity: CloudEntity,
    rule: SQLAwsSecurityGroupRuleWithName,
    entityTraffic: EntityDirectionedTraffic,
    allIssues: IssuesDictionary
  ): boolean {
    const ruleTrafficPatterns = this.filterTrafficPatternsByRule(
      rule,
      entityTraffic
    );

    if (ruleTrafficPatterns.length === 0) {
      const issue = this.createIssue(
        entity.id,
        ISSUE_TYPES.RULES_WITHOUT_MATCHING_ACCEPTED_TRAFFIC,
        ruleTrafficPatterns,
        this.entitiesMap,
        rule
      );

      allIssues[ISSUE_TYPES.RULES_WITHOUT_MATCHING_ACCEPTED_TRAFFIC].push({
        ...issue,
        ruleId: rule.id
      });

      return false;
    }

    return true;
  }

  private checkRuleForUnusedPorts(
    entity: CloudEntity,
    rule: SQLAwsSecurityGroupRuleWithName,
    entityTraffic: EntityDirectionedTraffic,
    allIssues: IssuesDictionary
  ): void {
    const ruleTrafficPatterns = this.filterTrafficPatternsByRule(
      rule,
      entityTraffic
    );
    if (ruleTrafficPatterns.length === 0) {
      return;
    }

    const entityTrafficPatterns = [
      ...entityTraffic.entityIngressTraffic,
      ...entityTraffic.entityEgressTraffic
    ];

    const distinctPorts = securityGroupUtils.getDistinctPorts(
      entityTrafficPatterns
    );

    if (
      distinctPorts.length < securityGroupUtils.getRuleOpenPorts(rule)?.length
    ) {
      const issue = this.createIssue(
        entity.id,
        ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS,
        ruleTrafficPatterns,
        this.entitiesMap,
        rule
      );
      allIssues[ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS].push({
        ...issue,
        ruleId: rule.id
      });
    }
  }

  private checkRuleForExcessiveCIDRs(
    entity: CloudEntity,
    rule: SQLAwsSecurityGroupRuleWithName,
    entityTraffic: EntityDirectionedTraffic,
    allIssues: IssuesDictionary
  ): void {
    const ruleTrafficPatterns = this.filterTrafficPatternsByRule(
      rule,
      entityTraffic
    );
    if (ruleTrafficPatterns.length === 0) {
      return;
    }

    const ruleNumberOfAdresses = securityGroupUtils.countIpsInCidr(rule.remote);
    const distinctAddresses = securityGroupUtils.getDistinctAddresses(
      rule.direction,
      ruleTrafficPatterns
    );

    if (
      distinctAddresses.length < 10 &&
      distinctAddresses.length < ruleNumberOfAdresses
    ) {
      const issue = this.createIssue(
        entity.id,
        ISSUE_TYPES.INGRESS_SG_RULES_WITH_EXCESSIVE_CIDR_ALLOWANCE,
        ruleTrafficPatterns,
        this.entitiesMap,
        rule
      );

      allIssues[
        ISSUE_TYPES.INGRESS_SG_RULES_WITH_EXCESSIVE_CIDR_ALLOWANCE
      ].push({
        ...issue,
        ruleId: rule.id
      });
    }
  }

  private findEntitiesInSecurityGroup(securityGroupId: string): CloudEntity[] {
    const entities: CloudEntity[] = [];

    for (const entity of this.entitiesMap.values()) {
      const awsInfo = entity.info as AWSEndpointInfo;
      const securityGroups = awsInfo?.securityGroups;

      if (securityGroups) {
        for (const sg of securityGroups) {
          if (sg?.id === securityGroupId) {
            entities.push(entity);
            break;
          }
        }
      }
    }

    return entities;
  }

  private checkRemoteSGRule(
    entity: CloudEntity,
    rule: SQLAwsSecurityGroupRuleWithName,
    entityTraffic: EntityDirectionedTraffic,
    allIssues: IssuesDictionary
  ): void {
    if (rule.remote_type !== 'security_group') {
      return;
    }

    const entitiesInRemoteSecurityGroup = this.findEntitiesInSecurityGroup(
      rule.remote
    );

    if (
      entityTraffic.entityIngressTraffic.length === 0 &&
      entityTraffic.entityEgressTraffic.length === 0
    ) {
      return;
    }

    const entityTrafficByRule = this.filterTrafficPatternsByRule(
      rule,
      entityTraffic
    );
    const distinctAddresses = securityGroupUtils.getDistinctIds(
      rule.direction,
      entityTrafficByRule
    );
    const securityGroupTrafficPatterns: SQLTrafficEvent[] = entityTrafficByRule;

    if (
      entityTrafficByRule.length !== 0 &&
      distinctAddresses.length < entitiesInRemoteSecurityGroup.length
    ) {
      const issue = this.createIssue(
        entity.id,
        ISSUE_TYPES.INGRESS_SG_RULE_WITH_EXCESSIVE_SG_ALLOWANCE,
        securityGroupTrafficPatterns,
        this.entitiesMap,
        rule
      );

      allIssues[ISSUE_TYPES.INGRESS_SG_RULE_WITH_EXCESSIVE_SG_ALLOWANCE].push({
        ...issue,
        ruleId: rule.id
      });
    }
  }

  public async raiseIssues(): Promise<any> {
    try {
      log.info('Starting issue raising process for security groups');

      const allIssues: IssuesDictionary = {
        [ISSUE_TYPES.RULES_WITHOUT_MATCHING_ACCEPTED_TRAFFIC]: [],
        [ISSUE_TYPES.INGRESS_SG_RULES_WITH_EXCESSIVE_CIDR_ALLOWANCE]: [],
        [ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS]: [],
        [ISSUE_TYPES.INGRESS_SG_RULE_WITH_EXCESSIVE_SG_ALLOWANCE]: []
      };

      const entitiesTrafficPatterns =
        (await this.trafficPatternsControl.execute(
          this.benchmark
        )) as AggregatedTrafficPattern[];

      const entityIdToSecurityGroupRuleMap =
        this.getEntityToSecurityGroupRuleMap();

      const trafficIndex = this.buildTrafficIndex(entitiesTrafficPatterns);

      for (const [entityId, entity] of this.entitiesMap) {
        const entityTraffic = this.getEntityTrafficByDirection(
          entityId,
          trafficIndex
        );

        if (
          entityTraffic.entityIngressTraffic.length === 0 &&
          entityTraffic.entityEgressTraffic.length === 0
        ) {
          console.log(`entity ${entityId} has no traffic`);

          continue;
        }

        const entityRules = entityIdToSecurityGroupRuleMap.get(entityId) || [];

        if (entityRules.length === 0) {
          console.log(`entity ${entityId} has no rules`);

          continue;
        }

        for (const rule of entityRules) {
          if (rule.remote_type === 'security_group') {
            continue;
          }

          const hasMatchingTraffic = this.checkRuleForMatchingTraffic(
            entity,
            rule,
            entityTraffic,
            allIssues
          );

          if (!hasMatchingTraffic) {
            continue;
          }

          this.checkRuleForUnusedPorts(entity, rule, entityTraffic, allIssues);

          this.checkRuleForExcessiveCIDRs(
            entity,
            rule,
            entityTraffic,
            allIssues
          );
        }
      }

      const remoteSecurityGroupRules = this.securityGroupsRules.filter(
        (rule) => rule.remote_type === 'security_group'
      );

      for (const rule of remoteSecurityGroupRules) {
        const securityGroupEntitiesIds = this.findEntitiesInSecurityGroup(
          rule.security_group_id
        );

        for (const entity of securityGroupEntitiesIds) {
          const entityTraffic = this.getEntityTrafficByDirection(
            entity.id,
            trafficIndex
          );
          this.checkRemoteSGRule(entity, rule, entityTraffic, allIssues);
        }
      }

      log.info('Successfully generated issues for security groups');

      Object.entries(allIssues).forEach(([category, issues]) => {
        log.info(`${category}: ${issues.length}`);
      });

      const clusteredIssues = this.clusterSimilarIssues(allIssues);

      return clusteredIssues;
    } catch (error) {
      log.error(
        `Failed to generate issues for security groups, error: ${error}`,
        error
      );
    }
  }

  private clusterSimilarIssues(
    allIssues: IssuesDictionary
  ): SecurityGroupIssue[] {
    const clusteredIssuesMap = new Map<string, IssuesCluster>();

    // cluster by category and ruleId
    Object.entries(allIssues).forEach(([category, issues]) => {
      issues.forEach((issue) => {
        const entityId = issue.entityId;

        const key = `${category}-${String(issue.ruleId)}`;
        const cluster = clusteredIssuesMap.get(key) || {
          category,
          severity: issue.severity,
          ruleId: String(issue.ruleId),
          entitiesIds: new Set<string>(),
          title: issue.title,
          subTitle: issue.subTitle,
          explaination: issue.explanation,
          trafficPatterns: {},
          relatedEntitiesIds: [...(issue.relatedEntitiesIds as string[])]
        };

        cluster.entitiesIds.add(entityId);
        cluster.trafficPatterns[entityId] = issue.trafficPatterns;
        issue.relatedEntitiesIds?.forEach((entity) => {
          cluster.relatedEntitiesIds.push(entity);
        });

        clusteredIssuesMap.set(key, cluster);
      });
    });

    const clusteredIssues = Array.from(clusteredIssuesMap.values()).map(
      (cluster) => ({
        category: cluster.category,
        severity: cluster.severity,
        explaination: cluster.explaination,
        ruleId: cluster.ruleId,
        entitiesIds: Array.from(cluster.entitiesIds),
        title: cluster.title,
        subTitle: cluster.subTitle,
        trafficPatterns: cluster.trafficPatterns,
        relatedEntitiesIds: Array.from(cluster.relatedEntitiesIds)
      })
    );

    const issues = clusteredIssues.map((cluster) => {
      const isExcessivePortIssues =
        cluster.category ===
        ISSUE_TYPES.INGRESS_SG_RULES_WITH_UNUSED_ALLOWED_PORTS;

      const clusterTrafficPatterns = Object.values(
        cluster.trafficPatterns
      ).flat();
      const clusterSecurityRule = this.securityGroupsRules.find(
        (rule) => rule.id === cluster.ruleId
      ) as SQLAwsSecurityGroupRuleWithName;

      const explanation = isExcessivePortIssues
        ? securityGroupUtils.getPermissivePortExplanationMessage(
            clusterTrafficPatterns,
            clusterSecurityRule
          )
        : cluster.explaination;

      const relatedEntitiesIds = new Set<string>();

      cluster.relatedEntitiesIds.forEach((entity) => {
        relatedEntitiesIds.add(entity);
      });

      return {
        entitiesIds: cluster.entitiesIds,
        category: cluster.category,
        severity: cluster.severity,
        trafficPatterns: cluster.trafficPatterns,
        explanation,
        ruleId: cluster.ruleId,
        status: 'open',
        type: 'security-group-issue',
        title: cluster.title,
        subTitle: cluster.subTitle,
        rule: clusterSecurityRule,
        relatedEntitiesIds: Array.from(relatedEntitiesIds)
      } as SecurityGroupIssue;
    });

    return issues;
  }
}
