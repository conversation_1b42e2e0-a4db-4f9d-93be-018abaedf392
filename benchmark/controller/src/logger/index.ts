import Logger from 'bunyan';
import { LoggerConfig } from 'coralogix-logger';
import CoralogixBunyan from 'coralogix-logger-bunyan';
import winston from 'winston';
import { colorizeJson, severityMap } from './colorize';
import {
  CoralogixLogData,
  ErrorLogEntry,
  GcpLabels,
  LogEntry,
  LoggerOptions,
  LogLevel
} from './types';
import { serializeObject } from './utils';

type LogMethod = keyof winston.Logger;

const labels: GcpLabels = {
  service: process.env.K_SERVICE || '',
  revision: process.env.K_REVISION || ''
};

let logger: winston.Logger | null = null;
let loggerContext: LoggerOptions | null = null;
let coralogixLogger: Logger | null = null;
let hasInitializedGcpContext = false;
let coralogixDisabled = false;

// Helper functions to reduce duplication
const createErrorEntry = (
  level: LogLevel,
  timestamp: string
): ErrorLogEntry => ({
  severity: severityMap[level] || 'DEFAULT',
  timestamp,
  message: 'Log serialization failed',
  error: 'JSON.stringify failed'
});

const formatJsonOutput = (
  entry: LogEntry,
  level: LogLevel,
  isLocal: boolean
): string => {
  if (isLocal) {
    const { loggerContext, ...localEntry } = entry;
    const jsonString = JSON.stringify(localEntry, null, 2);
    return colorizeJson(jsonString, level);
  }
  return JSON.stringify(entry);
};

const createGcpFormat = (): winston.Logform.Format => {
  return winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(
      ({ timestamp, level, message, userMeta, ...metadata }) => {
        const meta = serializeObject(userMeta || {});
        const entry: LogEntry = {
          severity: severityMap[level as LogLevel] || 'DEFAULT',
          timestamp: timestamp as string,
          message: message as string,
          loggerContext,
          meta
        };

        try {
          return formatJsonOutput(
            entry,
            level as LogLevel,
            !!process.env.LOCAL
          );
        } catch (err) {
          const errorEntry = createErrorEntry(
            level as LogLevel,
            timestamp as string
          );
          return formatJsonOutput(
            errorEntry,
            level as LogLevel,
            !!process.env.LOCAL
          );
        }
      }
    )
  );
};

const createConsoleFormat = (): winston.Logform.Format => {
  return winston.format.combine(
    winston.format.colorize(),
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${timestamp} [${level}]: ${message}`;
    })
  );
};

const getFormat = (): winston.Logform.Format => {
  // Always use GCP format, but it will be colorized when LOCAL is set
  return createGcpFormat();
};

const initGcpContext = (): void => {
  if (hasInitializedGcpContext) return;

  if (loggerContext) {
    loggerContext = {
      ...loggerContext,
      labels
    };
  }
  hasInitializedGcpContext = true;
};

const createCoralogixLogger = (
  environment: string,
  serviceName: string
): Logger | null => {
  if (!process.env.CORALOGIX_PRIVATE_KEY || !process.env.CORALOGIX_URL) {
    return null;
  }

  const config = new LoggerConfig({
    applicationName: environment,
    privateKey: process.env.CORALOGIX_PRIVATE_KEY,
    subsystemName: serviceName
  });

  CoralogixBunyan.CoralogixStream.configure(config);

  return Logger.createLogger({
    name: serviceName,
    level: 'debug',
    serializers: {
      err: Logger.stdSerializers.err
    },
    streams: [
      {
        level: 'debug',
        stream: new CoralogixBunyan.CoralogixStream({
          category: serviceName || 'DefaultCategory'
        }),
        type: 'raw'
      }
    ]
  });
};

const logToCoralogix = (
  level: LogLevel,
  message: string,
  meta: Record<string, any>
): void => {
  if (level === 'debug' || !coralogixLogger || coralogixDisabled) return;

  const severity = severityMap[level] || 'INFO';
  const logData: CoralogixLogData = {
    loggerContext,
    meta: serializeObject(meta),
    message,
    severity,
    level
  };

  try {
    coralogixLogger[level as keyof Logger]?.({
      message,
      severity,
      fields: JSON.stringify(logData, null, 2)
    });
  } catch (error) {
    console.error('Coralogix logging failed:', error);
  }
};

const createLoggerProxy = (
  targetLogger: winston.Logger,
  parentMeta?: Record<string, any>
): winston.Logger => {
  return new Proxy(targetLogger, {
    get(target, prop: LogMethod) {
      if (prop === 'child') {
        return (meta: Record<string, any>) => {
          const childLogger = target.child(meta);
          const combinedMeta = { ...parentMeta, ...meta };
          return createLoggerProxy(childLogger, combinedMeta);
        };
      }

      return async (...args: any[]) => {
        if (target && typeof target[prop] === 'function') {
          initGcpContext();

          const [message, meta] = args;
          const combinedMeta = {
            ...parentMeta,
            ...meta
          };

          logToCoralogix(prop as LogLevel, message, combinedMeta);

          return target[prop](message, combinedMeta);
        }
      };
    }
  });
};

export function initLogger(options: LoggerOptions): void {
  const {
    serviceName,
    environment = '',
    level = process.env.LOG_LEVEL || 'info',
    customTransports = [],
    organizationId,
    integrationId,
    IntegrationType,
    disableCoralogix = false
  } = options;

  loggerContext = options;
  coralogixDisabled = disableCoralogix;

  const transports: winston.transport[] = [
    new winston.transports.Console({
      format: getFormat()
    }),
    ...customTransports
  ];

  // Only create Coralogix logger if not disabled
  if (!disableCoralogix) {
    coralogixLogger = createCoralogixLogger(environment, serviceName);
  }

  logger = winston.createLogger({
    level,
    transports
  });
}

const handler: ProxyHandler<winston.Logger> = {
  get(target, prop: keyof winston.Logger) {
    if (!logger) {
      throw new Error(
        'Logger has not been initialized. Call initLogger() first.'
      );
    }

    // Handle child method specially
    if (prop === 'child') {
      return (meta: Record<string, any>): winston.Logger => {
        const childLogger = logger!.child(meta);
        return createLoggerProxy(childLogger, meta);
      };
    }

    // For all other methods, check if they exist on the underlying logger
    if (logger && typeof logger[prop] === 'function') {
      return async (...args: any[]): Promise<any> => {
        initGcpContext();

        const [message, meta] = args;

        // Only log to Coralogix for standard logging methods
        if (typeof prop === 'string' && LogLevel.includes(prop as LogLevel)) {
          logToCoralogix(prop as LogLevel, message, meta || {});
        }

        // Pass meta as a custom field to avoid Winston's message merging
        return logger![prop](message, { userMeta: meta });
      };
    }

    // Handle unknown methods gracefully - log an error instead of crashing
    if (typeof prop === 'string') {
      return async (...args: any[]): Promise<any> => {
        const [message, meta] = args;

        // Log the error using the underlying logger's error method
        if (logger && typeof logger.error === 'function') {
          logger.error(
            `Unknown log level '${prop}' called with message: ${message}`,
            {
              userMeta: meta,
              unknownLevel: prop,
              originalMessage: message
            }
          );
        } else {
          // Fallback to console if logger.error is not available
          console.error(
            `Logger error: Unknown log level '${prop}' called with message: ${message}`,
            meta
          );
        }

        return Promise.resolve();
      };
    }

    // For non-function properties, return them directly from the underlying logger
    return logger?.[prop];
  }
};

const log: winston.Logger = new Proxy({} as winston.Logger, handler);

export default log;
