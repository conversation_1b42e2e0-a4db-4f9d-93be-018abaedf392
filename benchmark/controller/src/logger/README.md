# Logger

A Winston-based logger with support for:

- GCP Cloud Logging format
- Coralogix integration
- Colorized JSON output for local development

## Features

### Colorized JSON Logging

When the `LOCAL` environment variable is set, the logger will output colorized JSON with syntax highlighting:

- **Keys**: Cyan
- **String values**: Green
- **Boolean values**: Magenta
- **Numbers**: Yellow
- **Null values**: Gray
- **Severity**: **Bold Green** (highlighted for importance)
- **Message**: **Bold Yellow** (highlighted for importance)
- **Overall structure**: Color-coded by log level (error=red, warn=yellow, info=blue, debug=gray)
- **Formatting**: Pretty-printed with indentation when LOCAL is set, compact JSON otherwise
- **Local display**: Excludes `loggerContext` and `service` fields for cleaner output

### Usage

```typescript
import { initLogger } from './logger';

// Initialize logger
initLogger({
  serviceName: 'my-service',
  environment: 'development'
});

// Set LOCAL environment variable for colorized output
process.env.LOCAL = 'true';

// Log messages will be colorized JSON when LOCAL is set
log.info('User logged in', { userId: 123, timestamp: new Date() });
```

## Installation

```bash
npm install
```

## Local Development Setup

To enable colorized JSON logging for local development:

1. **Install chalk as a dev dependency**:

   ```bash
   npm install chalk --save-dev
   ```

2. **Set the LOCAL environment variable**:

   ```bash
   export LOCAL=true
   ```

   Or add it to your shell profile (`.zshrc`, `.bashrc`):

   ```bash
   echo 'export LOCAL=true' >> ~/.zshrc
   source ~/.zshrc
   ```

3. **Usage in your code**:

   ```typescript
   import { initLogger } from './logger';

   // Initialize logger
   initLogger({
     serviceName: 'my-service',
     environment: 'development'
   });

   // Set LOCAL environment variable for colorized output
   process.env.LOCAL = 'true';

   // Log messages will be colorized JSON when LOCAL is set
   log.info('User logged in', { userId: 123, timestamp: new Date() });
   ```

## Files

- `index.ts` - Main logger implementation
- `colorize.ts` - Colorization and formatting utilities
- `types.ts` - TypeScript type definitions
- `utils.ts` - Utility functions
