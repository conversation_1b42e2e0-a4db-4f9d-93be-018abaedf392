import { transport } from 'winston';

export const LogLevel = [
  'error',
  'warn',
  'info',
  'debug',
  'verbose',
  'silly'
] as const;

export type LogLevel = (typeof LogLevel)[number];

export interface GcpLabels {
  service?: string;
  revision?: string;
}

export interface LoggerOptions {
  serviceName: string;
  environment?: string;
  sentryDsn?: string;
  level?: string;
  defaultMeta?: Record<string, any>;
  customTransports?: transport[];
  organizationId?: string;
  integrationId?: string;
  IntegrationType?: string;
  isBackfill?: boolean;
  labels?: GcpLabels;
  disableCoralogix?: boolean;
}

export interface LogEntry {
  severity: string;
  timestamp: string;
  message: string;
  loggerContext?: LoggerOptions | null;
  meta?: Record<string, any>;
}

export interface ErrorLogEntry {
  severity: string;
  timestamp: string;
  message: string;
  error: string;
}

export interface CoralogixLogData {
  loggerContext: LoggerOptions | null;
  meta: any;
  message: string;
  severity: string;
  level: string;
}
