import { LogLevel } from './types';

// Type definitions
interface ChalkInstance {
  red: (text: string) => string;
  yellow: (text: string) => string;
  white: (text: string) => string;
  gray: (text: string) => string;
}

// Lazy chalk import - only when LOCAL is set
let chalk: ChalkInstance | null = null;
let chalkLoaded = false;

const loadChalk = (): ChalkInstance | null => {
  if (chalkLoaded) {
    return chalk;
  }

  try {
    chalk = require('chalk');
    console.log('Chalk loaded successfully');
  } catch {
    console.log('Chalk not available, will return original strings');
  }

  chalkLoaded = true;
  return chalk;
};

const severityMap: Record<LogLevel, string> = {
  error: 'ERROR',
  warn: 'WARNING',
  info: 'INFO',
  debug: 'DEBUG',
  verbose: 'DEBUG',
  silly: 'DEBUG'
} as const;

const colorizeJson = (jsonString: string, level: LogLevel): string => {
  // Only load chalk if LOCAL is set
  const chalkInstance = loadChalk();

  // If chalk is not available, return original string
  if (!chalkInstance) {
    return jsonString;
  }

  // Color scheme based on log level
  const levelColors: Record<LogLevel, (text: string) => string> = {
    error: chalkInstance.red,
    warn: chalkInstance.yellow,
    info: chalkInstance.white,
    debug: chalkInstance.gray,
    verbose: chalkInstance.gray,
    silly: chalkInstance.gray
  };

  const color = levelColors[level];
  return colorizeJsonSyntax(jsonString, color, chalkInstance);
};

const colorizeJsonSyntax = (
  jsonString: string,
  baseColor: (text: string) => string,
  chalk: ChalkInstance
): string => {
  return jsonString
    .split('\n')
    .map((line) => {
      // Apply base color to the entire line
      const coloredLine = baseColor(line);

      // Highlight the message value in yellow
      return coloredLine.replace(
        /"message":\s*"([^"]*)"/g,
        '"message": ' + chalk.yellow(`"$1"`)
      );
    })
    .join('\n');
};

export { colorizeJson, severityMap };
