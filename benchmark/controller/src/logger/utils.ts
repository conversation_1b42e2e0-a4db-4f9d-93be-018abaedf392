/**
 * Custom serializer to properly handle Error objects in JSON.stringify
 * @param obj The object to serialize
 * @returns A serializable version of the object
 */
export function serializeObject(obj: any): any {
  try {
    // Handle null/undefined
    if (obj === null || obj === undefined) {
      return obj;
    }

    // Handle primitives
    if (typeof obj !== 'object' && typeof obj !== 'function') {
      return obj;
    }

    // Handle functions
    if (typeof obj === 'function') {
      return '[Function]';
    }

    // Handle symbols
    if (typeof obj === 'symbol') {
      return '[Symbol]';
    }

    // Handle BigInt
    if (typeof obj === 'bigint') {
      return obj.toString();
    }

    // Try to use toJSON if available
    if (obj && typeof obj.toJSON === 'function') {
      try {
        return obj.toJSON();
      } catch (toJsonError) {
        return '[toJSON Error]';
      }
    }

    // Handle Error objects (including Axios errors)
    if (isErrorObject(obj)) {
      return serializeError(obj);
    }

    // Handle arrays
    if (Array.isArray(obj)) {
      return obj.map(serializeObject);
    }

    // Handle objects
    if (typeof obj === 'object') {
      const result: Record<string, any> = {};
      for (const [key, value] of Object.entries(obj)) {
        result[key] = serializeObject(value);
      }
      return result;
    }

    return obj;
  } catch (error) {
    console.error('Serialization error', error);
    return '[Serialization Error]';
  }
}

/**
 * Check if an object is an error-like object
 */
function isErrorObject(obj: any): boolean {
  // Standard Error instances
  if (obj instanceof Error) {
    return true;
  }

  // Check for common error-like objects (Axios, etc.)
  if (obj && typeof obj === 'object') {
    // Must have at least 2 of these properties to be considered an error
    const errorProps = ['message', 'stack', 'name', 'code', 'status'];
    const hasErrorProps = errorProps.filter((prop) => obj[prop] !== undefined);

    // Require at least 2 error properties to avoid false positives
    return hasErrorProps.length >= 2;
  }

  return false;
}

/**
 * Safely serialize error objects
 */
function serializeError(error: any): any {
  try {
    const errorObj: Record<string, any> = {
      name: error.name || 'UnknownError',
      message: error.message || 'No message',
      stack: error.stack
    };

    // Add common error properties
    if (error.code) errorObj.code = error.code;
    if (error.status) errorObj.status = error.status;
    if (error.statusText) errorObj.statusText = error.statusText;
    if (error.cause) errorObj.cause = serializeObject(error.cause);

    return errorObj;
  } catch (err) {
    return { name: 'Error', message: 'Failed to serialize error' };
  }
}
