import { Benchmark } from './types/benchmarkTypes';
import log from '../logger';
import { BaseControl } from './controls';
import { ControlResultData } from './controls/types';
import {
  ControlWithDependencies,
  sortControlsByDependencies,
  ControlDataType
} from './controlDependencies';
import { BenchmarkFirestoreWriter } from '../firestore/benchmark';

/**
 * Legacy function for backward compatibility - executes all controls and returns full benchmark
 */
export async function runBenchmarks(
  benchmarkControls: BaseControl[],
  organizationId: string
): Promise<Benchmark> {
  log.info(`Running benchmarks for organization: ${organizationId}`);

  const benchmark: Benchmark = {
    entities: [],
    enrichedEntities: [],
    issues: [],
    trafficPatterns: [],
    notifications: [],
    stats: [],
    labels: [],
    labelAssignments: [],
    detections: [],
    aiDetections: [],
    securityGroups: []
  };

  for (const control of benchmarkControls) {
    try {
      const startTime = process.hrtime();

      const controlResults = await control.execute(benchmark);

      const endTime = process.hrtime(startTime);
      const timeTaken = (endTime[0] * 1e9 + endTime[1]) / 1e9; // Convert to seconds

      log.info(
        `Successfully executed control: ${control.context} in ${timeTaken.toFixed(3)} seconds`
      );

      if (!controlResults) continue;

      controlResults.forEach((controlResult: ControlResultData) => {
        const {
          entity,
          enrichedEntity,
          issue,
          traffic_pattern: trafficPattern,
          notification,
          stat,
          label,
          labelAssignment,
          allLabels,
          allLabelAssignments,
          detection,
          aiDetection,
          securityGroup
        } = controlResult;

        if (entity) benchmark.entities.push(entity);
        if (enrichedEntity) benchmark.enrichedEntities.push(enrichedEntity);
        if (issue) benchmark.issues.push(issue);
        if (trafficPattern) benchmark.trafficPatterns.push(trafficPattern);
        if (notification) benchmark.notifications.push(notification);
        if (stat) benchmark.stats.push(stat);
        if (label) benchmark.labels.push(label);
        if (labelAssignment) benchmark.labelAssignments.push(labelAssignment);
        if (allLabels) benchmark.allLabels = allLabels;
        if (allLabelAssignments)
          benchmark.allLabelAssignments = allLabelAssignments;
        if (detection) benchmark.detections.push(detection);
        if (aiDetection) benchmark.aiDetections.push(aiDetection);
        if (securityGroup) benchmark.securityGroups.push(securityGroup);
      });
    } catch (err) {
      throw new Error(`Error executing benchmark: ${(err as Error).message}`);
    }
  }

  const benchmarkSummary = Object.fromEntries(
    Object.entries(benchmark).map(([key, value]) => [
      key,
      Array.isArray(value) ? value.length : 0
    ])
  );

  log.info(
    `Benchmark executed successfully for organization: ${organizationId}
      entities: ${benchmarkSummary.entities}
      issues: ${benchmarkSummary.issues}
      traffic patterns: ${benchmarkSummary.trafficPatterns}
      notifications: ${benchmarkSummary.notifications}
      stats: ${benchmarkSummary.stats}`,
    {
      context: 'runBenchmarks',
      ...benchmarkSummary
    }
  );

  return benchmark;
}

/**
 * New dependency-aware execution function that executes controls one-by-one,
 * syncs to firestore immediately, and removes from memory
 */
export async function runBenchmarksWithDependencies(
  benchmarkControls: BaseControl[],
  organizationId: string
): Promise<void> {
  log.info(`Running dependency-aware benchmarks for organization: ${organizationId}`);

  // Cast controls to ControlWithDependencies (they all extend BaseControl which now has dependency methods)
  const controlsWithDependencies = benchmarkControls as ControlWithDependencies[];

  // Sort controls by dependency order
  const sortedControls = sortControlsByDependencies(controlsWithDependencies);

  log.info(`Sorted ${sortedControls.length} controls by dependency order`, {
    controlOrder: sortedControls.map(c => c.context)
  });

  // Track available data types and maintain minimal benchmark state for dependent controls
  const availableData = new Set<ControlDataType>();
  const benchmark: Benchmark = {
    entities: [],
    enrichedEntities: [],
    issues: [],
    trafficPatterns: [],
    notifications: [],
    stats: [],
    labels: [],
    labelAssignments: [],
    detections: [],
    aiDetections: [],
    securityGroups: []
  };

  // Execute controls one by one
  for (const control of sortedControls) {
    await executeControlAndSync(control, benchmark, availableData, organizationId);
  }

  log.info(`All controls executed successfully for organization: ${organizationId}`, {
    totalControls: sortedControls.length,
    availableDataTypes: Array.from(availableData)
  });
}

/**
 * Execute a single control and sync results to firestore
 */
async function executeControlAndSync(
  control: ControlWithDependencies,
  benchmark: Benchmark,
  availableData: Set<ControlDataType>,
  organizationId: string
): Promise<void> {
  const startTime = performance.now();

  log.info(`Executing control: ${control.context}`);

  try {
    // Execute the control
    const controlResults = await control.execute(benchmark);
    const endTime = performance.now();
    const timeTaken = (endTime - startTime) / 1000;

    log.info(`Executed control: ${control.context} in ${timeTaken.toFixed(3)} seconds`);

    if (!controlResults || controlResults.length === 0) {
      log.info(`Control ${control.context} produced no results`);
      return;
    }

    // Process results and update benchmark state for dependent controls
    const processedResults = processControlResults(controlResults, benchmark);

    // Sync results to firestore immediately
    await syncControlResultsToFirestore(control, processedResults, organizationId);

    // Mark data types as available for dependent controls
    control.getProducedDataTypes().forEach(dataType => {
      availableData.add(dataType);
    });

    // Clear processed results from memory (keep only what's needed for dependencies)
    clearControlResultsFromMemory(control, benchmark);

    log.info(`Completed control: ${control.context} in ${timeTaken.toFixed(3)} seconds`);
  } catch (err) {
    log.error(`Error executing control ${control.context}: ${(err as Error).message}`);
    throw err;
  }
}

/**
 * Process control results and update benchmark state
 */
function processControlResults(
  controlResults: ControlResultData[],
  benchmark: Benchmark
): {
  entities: any[];
  enrichedEntities: any[];
  issues: any[];
  trafficPatterns: any[];
  notifications: any[];
  stats: any[];
  labels: any[];
  labelAssignments: any[];
  allLabels?: any[];
  allLabelAssignments?: any[];
  detections: any[];
  aiDetections: any[];
  securityGroups: any[];
} {
  const results = {
    entities: [] as any[],
    enrichedEntities: [] as any[],
    issues: [] as any[],
    trafficPatterns: [] as any[],
    notifications: [] as any[],
    stats: [] as any[],
    labels: [] as any[],
    labelAssignments: [] as any[],
    allLabels: undefined as any[] | undefined,
    allLabelAssignments: undefined as any[] | undefined,
    detections: [] as any[],
    aiDetections: [] as any[],
    securityGroups: [] as any[]
  };

  controlResults.forEach((controlResult: ControlResultData) => {
    const {
      entity,
      enrichedEntity,
      issue,
      traffic_pattern: trafficPattern,
      notification,
      stat,
      label,
      labelAssignment,
      allLabels,
      allLabelAssignments,
      detection,
      aiDetection,
      securityGroup
    } = controlResult;

    if (entity) {
      benchmark.entities.push(entity);
      results.entities.push(entity);
    }
    if (enrichedEntity) {
      benchmark.enrichedEntities.push(enrichedEntity);
      results.enrichedEntities.push(enrichedEntity);
    }
    if (issue) {
      benchmark.issues.push(issue);
      results.issues.push(issue);
    }
    if (trafficPattern) {
      benchmark.trafficPatterns.push(trafficPattern);
      results.trafficPatterns.push(trafficPattern);
    }
    if (notification) {
      benchmark.notifications.push(notification);
      results.notifications.push(notification);
    }
    if (stat) {
      benchmark.stats.push(stat);
      results.stats.push(stat);
    }
    if (label) {
      benchmark.labels.push(label);
      results.labels.push(label);
    }
    if (labelAssignment) {
      benchmark.labelAssignments.push(labelAssignment);
      results.labelAssignments.push(labelAssignment);
    }
    if (allLabels) {
      benchmark.allLabels = allLabels;
      results.allLabels = allLabels;
    }
    if (allLabelAssignments) {
      benchmark.allLabelAssignments = allLabelAssignments;
      results.allLabelAssignments = allLabelAssignments;
    }
    if (detection) {
      benchmark.detections.push(detection);
      results.detections.push(detection);
    }
    if (aiDetection) {
      benchmark.aiDetections.push(aiDetection);
      results.aiDetections.push(aiDetection);
    }
    if (securityGroup) {
      benchmark.securityGroups.push(securityGroup);
      results.securityGroups.push(securityGroup);
    }
  });

  return results;
}

/**
 * Sync control results to firestore based on produced data types
 */
async function syncControlResultsToFirestore(
  control: ControlWithDependencies,
  results: any,
  organizationId: string
): Promise<void> {
  const writer = BenchmarkFirestoreWriter.createForControlSync(organizationId);
  const producedDataTypes = control.getProducedDataTypes();

  try {
    for (const dataType of producedDataTypes) {
      switch (dataType) {
        case ControlDataType.ENTITIES:
          if (results.entities.length > 0) {
            await writer.syncEntitiesFromControlResults(results.entities);
          }
          break;

        case ControlDataType.NOTIFICATIONS:
          if (results.notifications.length > 0) {
            await writer.syncNotificationsFromControlResults(results.notifications);
          }
          break;

        case ControlDataType.STATS:
          if (results.stats.length > 0) {
            await writer.syncStatsFromControlResults(results.stats);
          }
          break;

        case ControlDataType.SECURITY_GROUPS:
          if (results.securityGroups.length > 0) {
            await writer.syncSecurityGroupsFromControlResults(results.securityGroups);
          }
          break;

        case ControlDataType.ISSUES:
          if (results.issues.length > 0) {
            await writer.syncIssuesFromControlResults(results.issues);
          }
          break;

        case ControlDataType.LABELS:
          if (results.labels.length > 0 || results.labelAssignments.length > 0) {
            await writer.syncLabelsFromControlResults(
              results.labels,
              results.labelAssignments,
              results.allLabels,
              results.allLabelAssignments
            );
          }
          break;

        case ControlDataType.DETECTIONS:
          if (results.detections.length > 0 || results.aiDetections.length > 0) {
            await writer.syncDetectionsFromControlResults(results.detections, results.aiDetections);
          }
          break;

        case ControlDataType.ENRICHED_ENTITIES:
          if (results.enrichedEntities.length > 0) {
            await writer.syncEnrichedEntitiesFromControlResults(results.enrichedEntities);
          }
          break;

        default:
          log.warn(`Unknown data type for firestore sync: ${dataType}`);
      }
    }

    // Special case: Detection confidence updates existing detections, no separate sync needed
    if (control.context.includes('DetectionConfidenceControl')) {
      log.info(`Detection confidence scores calculated for ${results.detections.length} detections`);
    }
  } catch (error) {
    log.error(`Error syncing control ${control.context} to firestore: ${(error as Error).message}`);
    throw error;
  }
}

/**
 * Clear processed results from memory to free up space
 * Keep only data needed for dependent controls
 */
function clearControlResultsFromMemory(control: ControlWithDependencies, benchmark: Benchmark): void {
  const producedDataTypes = control.getProducedDataTypes();

  // For most controls, we can clear the data immediately after syncing
  // Only keep data that other controls depend on
  for (const dataType of producedDataTypes) {
    switch (dataType) {
      case ControlDataType.STATS:
      case ControlDataType.SECURITY_GROUPS:
      case ControlDataType.ISSUES:
        // These are typically not dependencies for other controls, can be cleared
        break;

      case ControlDataType.NOTIFICATIONS:
        // Keep notifications if NotificationStatisticsControl hasn't run yet
        // For simplicity, we'll keep them (they're not too large)
        break;

      case ControlDataType.ENTITIES:
        // Keep entities as they're needed by labels, detections, and enriched_entities
        // Will be cleared after all dependent controls are processed
        break;

      case ControlDataType.LABELS:
        // Keep labels and labelAssignments for detection_confidence and enriched_entities
        // Will be cleared after dependent controls are processed
        break;

      case ControlDataType.DETECTIONS:
        // Keep detections for detection_confidence dependency
        // Will be cleared after detection_confidence is processed
        break;

      case ControlDataType.ENRICHED_ENTITIES:
        // Enriched entities are typically the final step, can clear dependencies
        benchmark.entities = [];
        benchmark.labels = [];
        benchmark.labelAssignments = [];
        if (benchmark.allLabels) benchmark.allLabels = [];
        if (benchmark.allLabelAssignments) benchmark.allLabelAssignments = [];
        break;

      default:
        log.debug(`No specific memory cleanup for data type: ${dataType}`);
    }
  }

  // Special case: Detection confidence updates existing detections
  if (control.context.includes('DetectionConfidenceControl')) {
    // Clear detections as they're no longer needed after confidence scoring
    benchmark.detections = [];
    benchmark.aiDetections = [];
  }

  log.debug(`Memory cleanup completed for control: ${control.context}`);
}

export default {
  runBenchmarks,
  runBenchmarksWithDependencies
};
