import { Benchmark } from './types/benchmarkTypes';
import log from '../logger';
import { BaseControl } from './controls';
import { ControlResultData } from './controls/types';
import {
  categorizeControlsIntoGroups,
  getNextExecutableGroups,
  ControlGroup
} from './controlDependencies';
import { BenchmarkFirestoreWriter } from '../firestore/benchmark';

/**
 * Legacy function for backward compatibility - executes all controls and returns full benchmark
 */
export async function runBenchmarks(
  benchmarkControls: BaseControl[],
  organizationId: string
): Promise<Benchmark> {
  log.info(`Running benchmarks for organization: ${organizationId}`);

  const benchmark: Benchmark = {
    entities: [],
    enrichedEntities: [],
    issues: [],
    trafficPatterns: [],
    notifications: [],
    stats: [],
    labels: [],
    labelAssignments: [],
    detections: [],
    aiDetections: [],
    securityGroups: []
  };

  for (const control of benchmarkControls) {
    try {
      const startTime = process.hrtime();

      const controlResults = await control.execute(benchmark);

      const endTime = process.hrtime(startTime);
      const timeTaken = (endTime[0] * 1e9 + endTime[1]) / 1e9; // Convert to seconds

      log.info(
        `Successfully executed control: ${control.context} in ${timeTaken.toFixed(3)} seconds`
      );

      if (!controlResults) continue;

      controlResults.forEach((controlResult: ControlResultData) => {
        const {
          entity,
          enrichedEntity,
          issue,
          traffic_pattern: trafficPattern,
          notification,
          stat,
          label,
          labelAssignment,
          allLabels,
          allLabelAssignments,
          detection,
          aiDetection,
          securityGroup
        } = controlResult;

        if (entity) benchmark.entities.push(entity);
        if (enrichedEntity) benchmark.enrichedEntities.push(enrichedEntity);
        if (issue) benchmark.issues.push(issue);
        if (trafficPattern) benchmark.trafficPatterns.push(trafficPattern);
        if (notification) benchmark.notifications.push(notification);
        if (stat) benchmark.stats.push(stat);
        if (label) benchmark.labels.push(label);
        if (labelAssignment) benchmark.labelAssignments.push(labelAssignment);
        if (allLabels) benchmark.allLabels = allLabels;
        if (allLabelAssignments)
          benchmark.allLabelAssignments = allLabelAssignments;
        if (detection) benchmark.detections.push(detection);
        if (aiDetection) benchmark.aiDetections.push(aiDetection);
        if (securityGroup) benchmark.securityGroups.push(securityGroup);
      });
    } catch (err) {
      throw new Error(`Error executing benchmark: ${(err as Error).message}`);
    }
  }

  const benchmarkSummary = Object.fromEntries(
    Object.entries(benchmark).map(([key, value]) => [
      key,
      Array.isArray(value) ? value.length : 0
    ])
  );

  log.info(
    `Benchmark executed successfully for organization: ${organizationId}
      entities: ${benchmarkSummary.entities}
      issues: ${benchmarkSummary.issues}
      traffic patterns: ${benchmarkSummary.trafficPatterns}
      notifications: ${benchmarkSummary.notifications}
      stats: ${benchmarkSummary.stats}`,
    {
      context: 'runBenchmarks',
      ...benchmarkSummary
    }
  );

  return benchmark;
}

/**
 * New dependency-aware execution function that executes controls in groups,
 * syncs to firestore immediately, and removes from memory
 */
export async function runBenchmarksWithDependencies(
  benchmarkControls: BaseControl[],
  organizationId: string
): Promise<void> {
  log.info(`Running dependency-aware benchmarks for organization: ${organizationId}`);

  // Categorize controls into execution groups
  const controlGroups = categorizeControlsIntoGroups(benchmarkControls);

  log.info(`Organized controls into ${controlGroups.length} execution groups`, {
    groups: controlGroups.map(g => ({ name: g.name, controlCount: g.controls.length }))
  });

  // Track completed groups and maintain benchmark state for dependent controls
  const completedGroups = new Set<string>();
  const benchmark: Benchmark = {
    entities: [],
    enrichedEntities: [],
    issues: [],
    trafficPatterns: [],
    notifications: [],
    stats: [],
    labels: [],
    labelAssignments: [],
    detections: [],
    aiDetections: [],
    securityGroups: []
  };

  let remainingGroups = [...controlGroups];

  while (remainingGroups.length > 0) {
    // Get groups that can be executed (dependencies satisfied)
    const executableGroups = getNextExecutableGroups(remainingGroups, completedGroups);

    if (executableGroups.length === 0) {
      const remainingGroupNames = remainingGroups.map(g => g.name);
      throw new Error(`Circular dependency detected or missing dependencies. Remaining groups: ${remainingGroupNames.join(', ')}`);
    }

    // Execute all executable groups in parallel
    await Promise.all(
      executableGroups.map(group => executeControlGroup(group, benchmark, organizationId))
    );

    // Mark groups as completed and remove from remaining
    executableGroups.forEach(group => {
      completedGroups.add(group.name);
      log.info(`Completed execution group: ${group.name}`);
    });

    remainingGroups = remainingGroups.filter(
      group => !executableGroups.includes(group)
    );
  }

  log.info(`All control groups executed successfully for organization: ${organizationId}`, {
    completedGroups: Array.from(completedGroups)
  });
}

/**
 * Execute a single control group and sync results to firestore
 */
async function executeControlGroup(
  group: ControlGroup,
  benchmark: Benchmark,
  organizationId: string
): Promise<void> {
  const startTime = performance.now();

  log.info(`Executing control group: ${group.name} with ${group.controls.length} controls`);

  // Execute all controls in the group
  const groupResults: ControlResultData[] = [];

  for (const control of group.controls) {
    try {
      const controlStartTime = performance.now();
      const controlResults = await control.execute(benchmark);
      const controlEndTime = performance.now();
      const controlTimeTaken = (controlEndTime - controlStartTime) / 1000;

      log.info(`Executed control: ${control.context} in ${controlTimeTaken.toFixed(3)} seconds`);

      if (controlResults) {
        groupResults.push(...controlResults);
      }
    } catch (err) {
      log.error(`Error executing control ${control.context}: ${(err as Error).message}`);
      throw err;
    }
  }

  // Process results and update benchmark state for dependent controls
  const processedResults = processControlResults(groupResults, benchmark);

  // Sync results to firestore immediately
  await syncGroupResultsToFirestore(group.name, processedResults, organizationId);

  // Clear processed results from memory (keep only what's needed for dependencies)
  clearProcessedResultsFromMemory(group.name, benchmark);

  const endTime = performance.now();
  const timeTaken = (endTime - startTime) / 1000;

  log.info(`Completed control group: ${group.name} in ${timeTaken.toFixed(3)} seconds`);
}

/**
 * Process control results and update benchmark state
 */
function processControlResults(
  controlResults: ControlResultData[],
  benchmark: Benchmark
): {
  entities: any[];
  enrichedEntities: any[];
  issues: any[];
  trafficPatterns: any[];
  notifications: any[];
  stats: any[];
  labels: any[];
  labelAssignments: any[];
  allLabels?: any[];
  allLabelAssignments?: any[];
  detections: any[];
  aiDetections: any[];
  securityGroups: any[];
} {
  const results = {
    entities: [] as any[],
    enrichedEntities: [] as any[],
    issues: [] as any[],
    trafficPatterns: [] as any[],
    notifications: [] as any[],
    stats: [] as any[],
    labels: [] as any[],
    labelAssignments: [] as any[],
    allLabels: undefined as any[] | undefined,
    allLabelAssignments: undefined as any[] | undefined,
    detections: [] as any[],
    aiDetections: [] as any[],
    securityGroups: [] as any[]
  };

  controlResults.forEach((controlResult: ControlResultData) => {
    const {
      entity,
      enrichedEntity,
      issue,
      traffic_pattern: trafficPattern,
      notification,
      stat,
      label,
      labelAssignment,
      allLabels,
      allLabelAssignments,
      detection,
      aiDetection,
      securityGroup
    } = controlResult;

    if (entity) {
      benchmark.entities.push(entity);
      results.entities.push(entity);
    }
    if (enrichedEntity) {
      benchmark.enrichedEntities.push(enrichedEntity);
      results.enrichedEntities.push(enrichedEntity);
    }
    if (issue) {
      benchmark.issues.push(issue);
      results.issues.push(issue);
    }
    if (trafficPattern) {
      benchmark.trafficPatterns.push(trafficPattern);
      results.trafficPatterns.push(trafficPattern);
    }
    if (notification) {
      benchmark.notifications.push(notification);
      results.notifications.push(notification);
    }
    if (stat) {
      benchmark.stats.push(stat);
      results.stats.push(stat);
    }
    if (label) {
      benchmark.labels.push(label);
      results.labels.push(label);
    }
    if (labelAssignment) {
      benchmark.labelAssignments.push(labelAssignment);
      results.labelAssignments.push(labelAssignment);
    }
    if (allLabels) {
      benchmark.allLabels = allLabels;
      results.allLabels = allLabels;
    }
    if (allLabelAssignments) {
      benchmark.allLabelAssignments = allLabelAssignments;
      results.allLabelAssignments = allLabelAssignments;
    }
    if (detection) {
      benchmark.detections.push(detection);
      results.detections.push(detection);
    }
    if (aiDetection) {
      benchmark.aiDetections.push(aiDetection);
      results.aiDetections.push(aiDetection);
    }
    if (securityGroup) {
      benchmark.securityGroups.push(securityGroup);
      results.securityGroups.push(securityGroup);
    }
  });

  return results;
}

/**
 * Sync group results to firestore based on group type
 */
async function syncGroupResultsToFirestore(
  groupName: string,
  results: any,
  organizationId: string
): Promise<void> {
  const writer = BenchmarkFirestoreWriter.createForControlSync(organizationId);

  try {
    switch (groupName) {
      case 'entities':
        if (results.entities.length > 0) {
          await writer.syncEntitiesFromControlResults(results.entities);
        }
        break;

      case 'notifications':
        if (results.notifications.length > 0) {
          await writer.syncNotificationsFromControlResults(results.notifications);
        }
        break;

      case 'stats':
        if (results.stats.length > 0) {
          await writer.syncStatsFromControlResults(results.stats);
        }
        break;

      case 'security_groups':
        if (results.securityGroups.length > 0) {
          await writer.syncSecurityGroupsFromControlResults(results.securityGroups);
        }
        break;

      case 'issues':
        if (results.issues.length > 0) {
          await writer.syncIssuesFromControlResults(results.issues);
        }
        break;

      case 'notification_stats':
        if (results.stats.length > 0) {
          await writer.syncStatsFromControlResults(results.stats);
        }
        break;

      case 'labels':
        if (results.labels.length > 0 || results.labelAssignments.length > 0) {
          await writer.syncLabelsFromControlResults(
            results.labels,
            results.labelAssignments,
            results.allLabels,
            results.allLabelAssignments
          );
        }
        break;

      case 'detections':
        if (results.detections.length > 0 || results.aiDetections.length > 0) {
          await writer.syncDetectionsFromControlResults(results.detections, results.aiDetections);
        }
        break;

      case 'detection_confidence':
        // Detection confidence updates existing detections, no separate sync needed
        log.info(`Detection confidence scores calculated for ${results.detections.length} detections`);
        break;

      case 'enriched_entities':
        if (results.enrichedEntities.length > 0) {
          await writer.syncEnrichedEntitiesFromControlResults(results.enrichedEntities);
        }
        break;

      default:
        log.warn(`Unknown group type for firestore sync: ${groupName}`);
    }
  } catch (error) {
    log.error(`Error syncing group ${groupName} to firestore: ${(error as Error).message}`);
    throw error;
  }
}

/**
 * Clear processed results from memory to free up space
 * Keep only data needed for dependent controls
 */
function clearProcessedResultsFromMemory(groupName: string, benchmark: Benchmark): void {
  switch (groupName) {
    case 'stats':
    case 'security_groups':
    case 'issues':
    case 'notification_stats':
      // These are independent and can be cleared completely
      // No other controls depend on them
      break;

    case 'notifications':
      // Keep notifications for notification_stats dependency
      // Will be cleared after notification_stats is processed
      break;

    case 'entities':
      // Keep entities as they're needed by labels, detections, and enriched_entities
      // Will be cleared after all dependent controls are processed
      break;

    case 'labels':
      // Keep labels and labelAssignments for detection_confidence and enriched_entities
      // Will be cleared after dependent controls are processed
      break;

    case 'detections':
      // Keep detections for detection_confidence dependency
      // Will be cleared after detection_confidence is processed
      break;

    case 'detection_confidence':
      // Clear detections as they're no longer needed
      benchmark.detections = [];
      benchmark.aiDetections = [];
      break;

    case 'enriched_entities':
      // Clear entities and labels as they're no longer needed
      benchmark.entities = [];
      benchmark.labels = [];
      benchmark.labelAssignments = [];
      benchmark.allLabels = [];
      benchmark.allLabelAssignments = [];
      break;

    default:
      log.warn(`Unknown group type for memory cleanup: ${groupName}`);
  }

  log.debug(`Memory cleanup completed for group: ${groupName}`);
}

export default {
  runBenchmarks,
  runBenchmarksWithDependencies
};
