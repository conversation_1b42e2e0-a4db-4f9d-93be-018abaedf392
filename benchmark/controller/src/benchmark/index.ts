import { Benchmark } from './types/benchmarkTypes';
import log from '../logger';
import { BaseControl } from './controls';
import { ControlResultData } from './controls/types';

export async function runBenchmarks(
  benchmarkControls: BaseControl[],
  organizationId: string
): Promise<Benchmark> {
  log.info(`Running benchmarks for organization: ${organizationId}`);

  const benchmark: Benchmark = {
    entities: [],
    enrichedEntities: [],
    issues: [],
    trafficPatterns: [],
    notifications: [],
    stats: [],
    labels: [],
    labelAssignments: [],
    detections: [],
    aiDetections: [],
    securityGroups: []
  };

  for (const control of benchmarkControls) {
    try {
      const startTime = process.hrtime();

      const controlResults = await control.execute(benchmark);

      const endTime = process.hrtime(startTime);
      const timeTaken = (endTime[0] * 1e9 + endTime[1]) / 1e9; // Convert to seconds

      log.info(
        `Successfully executed control: ${control.context} in ${timeTaken.toFixed(3)} seconds`
      );

      if (!controlResults) continue;

      controlResults.forEach((controlResult: ControlResultData) => {
        const {
          entity,
          enrichedEntity,
          issue,
          traffic_pattern: trafficPattern,
          notification,
          stat,
          label,
          labelAssignment,
          allLabels,
          allLabelAssignments,
          detection,
          aiDetection,
          securityGroup
        } = controlResult;

        if (entity) benchmark.entities.push(entity);
        if (enrichedEntity) benchmark.enrichedEntities.push(enrichedEntity);
        if (issue) benchmark.issues.push(issue);
        if (trafficPattern) benchmark.trafficPatterns.push(trafficPattern);
        if (notification) benchmark.notifications.push(notification);
        if (stat) benchmark.stats.push(stat);
        if (label) benchmark.labels.push(label);
        if (labelAssignment) benchmark.labelAssignments.push(labelAssignment);
        if (allLabels) benchmark.allLabels = allLabels;
        if (allLabelAssignments)
          benchmark.allLabelAssignments = allLabelAssignments;
        if (detection) benchmark.detections.push(detection);
        if (aiDetection) benchmark.aiDetections.push(aiDetection);
        if (securityGroup) benchmark.securityGroups.push(securityGroup);
      });
    } catch (err) {
      throw new Error(`Error executing benchmark: ${(err as Error).message}`);
    }
  }

  const benchmarkSummary = Object.fromEntries(
    Object.entries(benchmark).map(([key, value]) => [
      key,
      Array.isArray(value) ? value.length : 0
    ])
  );

  log.info(
    `Benchmark executed successfully for organization: ${organizationId}
      entities: ${benchmarkSummary.entities}
      issues: ${benchmarkSummary.issues}
      traffic patterns: ${benchmarkSummary.trafficPatterns}
      notifications: ${benchmarkSummary.notifications}
      stats: ${benchmarkSummary.stats}`,
    {
      context: 'runBenchmarks',
      ...benchmarkSummary
    }
  );

  return benchmark;
}

export default {
  runBenchmarks
};
