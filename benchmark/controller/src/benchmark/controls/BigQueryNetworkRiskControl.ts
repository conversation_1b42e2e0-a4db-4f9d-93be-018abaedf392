import { ControlResultData } from './types';
import { Benchmark } from '../types/benchmarkTypes';
import BigQuerySQLControl from './BigQuerySQLControl';

import { NetworkRiskScoreData } from '@globalTypes/ndrBenchmarkTypes/statTypes';

const CHART_NAME = 'Network Risk Score';
const GRAPH_TYPE = 'bar';
const STAT_TYPE = 'network_risk_score';
const SUBCOLLECTION_NAME = 'risk_history';

class BigQueryNetworkRiskControl extends BigQuerySQLControl {
  constructor(filePath: string) {
    if (!process.env.NETWORK_RISK_SCORE_CONFIG) {
      throw new Error(
        'NETWORK_RISK_SCORE_CONFIG environment variable is required'
      );
    }
    const config = JSON.parse(process.env.NETWORK_RISK_SCORE_CONFIG);
    super(filePath, false, config);
  }
  private enrichWithTimestamp(items: any[]) {
    const THREE_MONTHS_IN_MS = 3 * 30 * 24 * 60 * 60 * 1000;
    return items.map((item) => {
      const { timestamp, ...rest } = item;
      return {
        ...rest,
        createdAt: new Date(timestamp),
        expireAt: new Date(new Date(timestamp).getTime() + THREE_MONTHS_IN_MS)
      };
    });
  }

  private buildStatObject(data: NetworkRiskScoreData[]): ControlResultData[] {
    if (data.length === 0) {
      return [];
    }
    return [
      {
        stat: {
          type: STAT_TYPE,
          title: CHART_NAME,
          chartType: GRAPH_TYPE,
          data: data,
          subcollectionName: SUBCOLLECTION_NAME
        }
      }
    ];
  }
  public async execute(benchmark: Benchmark): Promise<ControlResultData[]> {
    const networkRiskScore = (await super.execute(
      benchmark
    )) as NetworkRiskScoreData[];
    const formattedNetworkRiskScore =
      this.enrichWithTimestamp(networkRiskScore);
    const networkRiskScoreWithRiskScore = formattedNetworkRiskScore.filter(
      (item) => item.score !== null
    );
    const result = this.buildStatObject(networkRiskScoreWithRiskScore);
    return result;
  }
}

export default BigQueryNetworkRiskControl;
