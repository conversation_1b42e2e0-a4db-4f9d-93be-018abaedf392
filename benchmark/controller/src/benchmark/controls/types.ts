import {
  CloudEntity,
  Issue,
  StatsType,
  TrafficPattern,
  Notification,
  Label,
  LabelAssignment,
  DetectionControl,
  DetectionWithControlResults,
  SQLAwsSecurityGroup,
  DetectionConfidence,
  EnrichedEntity
} from '@globalTypes/ndrBenchmarkTypes';

type ObjectType<T, K extends string> = {
  [P in K]: T[];
};

export type IssueObject = ObjectType<Issue, 'issue'>;
export type NotificationObject = ObjectType<Notification, 'notification'>;
export type EntityObject = ObjectType<CloudEntity, 'entity'>;
export type TrafficPatternObject = ObjectType<
  TrafficPattern,
  'traffic_pattern'
>;

export interface ControlResultData {
  entity?: any;
  enrichedEntity?: EnrichedEntity;
  issue?: any;
  traffic_pattern?: any;
  notification?: any;
  stat?: any;
  label?: Label;
  labelAssignment?: LabelAssignment;
  allLabels?: Label[];
  allLabelAssignments?: LabelAssignment[];
  detectionControl?: DetectionControl;
  detection?: DetectionWithControlResults;
  aiDetection?: DetectionWithControlResults;
  detectionConfidence?: DetectionConfidence;
  securityGroup?: SQLAwsSecurityGroup;
}

export type SQLQueryControlResponse = {
  rows: ControlResultData[];
};

export const NOTIFICATION_STATISTICS_TYPES = {
  BAR_CHART: 'notifications_bar_chart',
  COUNT_LAST_WEEK: 'notifications_count_last_week',
  COUNT_LAST_24H: 'notifications_count_last_24h',
  LINE_GRAPH: 'notifications_line_graph',
  PIE_CHART: 'notifications_pie_chart',
  TOTAL_COUNT: 'notifications_total_count'
} as const;

export type NotificationStatisticsType =
  (typeof NOTIFICATION_STATISTICS_TYPES)[keyof typeof NOTIFICATION_STATISTICS_TYPES];

export interface StatObject {
  stat: {
    type: StatsType | NotificationStatisticsType;
    data: number | Array<{ key: string | Date; data: number }>;
  };
}

export interface CombineNotificationsParams {
  benchmarkNotifications: Notification[];
  fireStoreNotifications: Notification[];
}

export type CombineNotificationsFunction = (
  params: CombineNotificationsParams
) => Notification[];

export interface BarChartData {
  key: string;
  data: number;
}

export interface LineGraphData {
  key: Date;
  data: number;
}

export interface PieChartData {
  key: string;
  data: number;
}

export type StatisticsFunctionsResult =
  | LineGraphData[]
  | PieChartData[]
  | BarChartData[]
  | number;

export type StatisticsObject = Record<
  NotificationStatisticsType,
  StatisticsFunctionsResult
>;

export type MatchPattern = {
  direction: string;
  port: string[];
};

export interface IProcessDetectionResultsParams {
  detectionControls: DetectionControl[];
  detectionsExecutionsResults: PromiseSettledResult<any[]>[];
}

export type HandleControlResults = {
  detections: DetectionWithControlResults[];
};
