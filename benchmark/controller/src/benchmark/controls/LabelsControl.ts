import { ControlResultData } from './types';
import { Benchmark } from '../types/benchmarkTypes';
import BaseControl from './BaseControl';
import * as firestoreOperations from '../../firestore/labels';
import log from '@logger';
import { ControlDataType } from '../controlDependencies';

import { LabelService } from '../../services/labels/LabelService';

class LabelsControl extends BaseControl {
  private organizationId: string;

  constructor(organizationId: string) {
    super('LabelsControl');
    this.organizationId = organizationId;
  }

  public async execute(benchmark: Benchmark): Promise<ControlResultData[]> {
    const fireStoreLabels = await firestoreOperations.getLabels(
      this.organizationId
    );

    const fireStoreLabelAssignments =
      await firestoreOperations.getLabelAssignments(this.organizationId);

    const labelService = new LabelService();
    const { createdLabels, labelAssignments } = await labelService.handle({
      existingLabels: fireStoreLabels,
      entities: benchmark.entities,
      existingLabelAssignments: fireStoreLabelAssignments
    });

    benchmark.labelAssignments = labelAssignments;

    const result: ControlResultData[] = [];

    createdLabels.forEach((label) => {
      result.push({ label });
    });

    const allLabels = [...createdLabels, ...fireStoreLabels];
    if (allLabels.length > 0) {
      result.push({ allLabels });
    }

    const allLabelAssignments = [
      ...labelAssignments,
      ...fireStoreLabelAssignments
    ];
    if (allLabelAssignments.length > 0) {
      result.push({ allLabelAssignments });
    }

    log.info(`New labels were generated: ${result.length}`);
    log.info(`LabelAssignments going to be synced: ${labelAssignments.length}`);

    return result;
  }

  public getDependencies(): ControlDataType[] {
    return [ControlDataType.ENTITIES]; // Labels depend on entities
  }

  public getProducedDataTypes(): ControlDataType[] {
    return [ControlDataType.LABELS];
  }
}

export default LabelsControl;
