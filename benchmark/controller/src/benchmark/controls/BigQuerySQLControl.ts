import path from 'path';
import { BigQuery, BigQueryTimestamp } from '@google-cloud/bigquery';
import BaseControl from './BaseControl';
import consts from '../../consts';
import { ControlResultData } from './types';
import { readFileSync } from 'fs';
import { Benchmark } from '../types/benchmarkTypes';
import Handlebars from 'handlebars';
import * as config from '../../config';
import _ from 'lodash';
import { ControlDataType } from '../controlDependencies';

import { getBigQueryClient, BigQueryClient } from '@big-query-client/bigquery';

function convertBigQueryTimestampsToDates(data: any, visited = new Set()): any {
  if (_.isString(data)) {
    try {
      const parsed = JSON.parse(data);
      if (_.isObject(parsed) || _.isArray(parsed)) {
        return parsed;
      } else {
        return data;
      }
    } catch (e) {
      return data;
    }
  }

  if (_.isNull(data) || !_.isObject(data)) {
    return data;
  }

  if (visited.has(data)) {
    return data; // Prevent infinite recursion for circular references
  }
  visited.add(data);

  // Detects BOTH BigQueryTimestamp objects:
  if (
    data instanceof BigQueryTimestamp ||
    (data && data.constructor?.name === 'BigQueryTimestamp' && 'value' in data)
  ) {
    return new Date((data as any).value);
  }

  if (_.isArray(data)) {
    return _.map(data, (item) =>
      convertBigQueryTimestampsToDates(item, visited)
    );
  }

  return _.mapValues(data, (value) =>
    convertBigQueryTimestampsToDates(value, visited)
  );
}

class BigQuerySQLControl extends BaseControl {
  private filePath: string;
  private client: BigQueryClient | null = null;
  private dataset: string;
  private allowFailure: boolean;
  private queryParams: Record<string, any>;

  constructor(
    filePath: string,
    allowFailure: boolean = false,
    queryParams: Record<string, any> = {}
  ) {
    super(`BigQuerySQLControl: "${filePath}"`);
    this.filePath = path.join(consts.getBigQueryLocation(__dirname), filePath);
    this.allowFailure = allowFailure;
    this.dataset = config.Global.organizationId;
    this.queryParams = queryParams;
  }

  private async getClient(): Promise<BigQueryClient> {
    if (!this.client) {
      this.client = await getBigQueryClient({
        datasetId: config.Global.organizationId
      });
    }
    return this.client;
  }

  private getCompileQuery(): string {
    const query = readFileSync(this.filePath, 'utf8');
    const getTemplateQuery = Handlebars.compile(query);
    const context = {
      dataset: this.dataset
    };

    return getTemplateQuery(context);
  }

  public async executeQueryInBatches(
    query: string,
    queryParams?: Record<string, any>,
    batchSize: number = 1000
  ): Promise<any> {
    let results: any[] = [];
    let batch: any[] = [];

    const client = await this.getClient();
    const stream = client.bigquery.createQueryStream({
      query: query,
      params: queryParams
    });

    return new Promise((resolve, reject) => {
      stream.on('data', (row: any) => {
        batch.push(row);

        if (batch.length >= batchSize) {
          try {
            const convertedBatch = convertBigQueryTimestampsToDates(batch);
            const normalizedResults = JSON.parse(
              JSON.stringify(convertedBatch)
            );
            results.push(...normalizedResults);

            batch = [];
          } catch (error) {
            reject(new Error(`Error processing batch: ${error}`));
            return;
          }
        }
      });

      stream.on('end', () => {
        try {
          if (batch.length > 0) {
            const convertedBatch = convertBigQueryTimestampsToDates(batch);
            const normalizedResults = JSON.parse(
              JSON.stringify(convertedBatch)
            );
            results.push(...normalizedResults);
          }

          console.log(
            `Query completed. Total rows processed: ${results.length}`
          );
          resolve(results);
        } catch (error) {
          reject(new Error(`Error processing final batch: ${error}`));
        }
      });

      stream.on('error', (error: Error) => {
        reject(new Error(`BigQuery stream error: ${error.message}`));
      });
    });
  }

  public async execute(benchmark?: Benchmark): Promise<ControlResultData[]> {
    try {
      const compiledQuery = this.getCompileQuery();
      return await this.executeQueryInBatches(compiledQuery, this.queryParams);
    } catch (error) {
      if (this.allowFailure) {
        console.log(
          'Failed to execute query, allow failure set to true',
          this.filePath
        );
        return [];
      }
      throw error;
    }
  }

  public getDependencies(): ControlDataType[] {
    // Determine dependencies based on file path
    if (this.filePath.includes('notifications/')) {
      return []; // Notifications are independent
    }
    if (this.filePath.includes('stats/')) {
      return []; // Most stats are independent
    }
    if (this.filePath.includes('security-groups/')) {
      return []; // Security groups are independent
    }
    return []; // Default to no dependencies
  }

  public getProducedDataTypes(): ControlDataType[] {
    // Determine produced data types based on file path
    if (this.filePath.includes('notifications/')) {
      return [ControlDataType.NOTIFICATIONS];
    }
    if (this.filePath.includes('stats/')) {
      return [ControlDataType.STATS];
    }
    if (this.filePath.includes('security-groups/')) {
      return [ControlDataType.SECURITY_GROUPS];
    }
    return []; // Default to no produced data types
  }
}

export default BigQuerySQLControl;
