import _ from 'lodash';
import { Logger } from 'winston';

import { Benchmark, IssueHint } from '../types/benchmarkTypes';
import BigQuerySQLControl from './BigQuerySQLControl';
import { CloudEntity, Issue } from '../../globalTypes/ndrBenchmarkTypes';

import log from '../../logger';
import { VertexService } from '../../services/vertex-ai';
import { FirewallRemediationService } from '../../services/remediation/firewall.service';
import { ControlResultData } from './types';
import BaseControl from './BaseControl';
import { SecurityGroupIssueAnalyzer } from '../../services/issues/securityGroupIssues';
import { SQLAwsSecurityGroupRuleWithName } from '../../services/issues/types';
import { transformSQLTrafficPatterns } from '../../services/issues/utils';

const MIN_ISSUE_CREATION_BUFFER =
  (24 + (Math.random() * 10 - 5)) * 60 * 60 * 1000; // 16-26 hours in milliseconds

const MAX_ISSUES_PER_EXECUTION = 4;
const MIN_ISSUES_PER_EXECUTION = 2;

const NUMBER_OF_ISSUES_TO_CREATE = _.random(
  MIN_ISSUES_PER_EXECUTION,
  MAX_ISSUES_PER_EXECUTION
);
const MAX_TRAFFIC_PATTERNS_PER_ISSUE = 10;

class VertexIssueControl extends BaseControl {
  private issueHintControl: BigQuerySQLControl;
  private securityGroupsControl: BigQuerySQLControl;

  private vertexService: VertexService;
  private firewallRemediationService: FirewallRemediationService;
  private existingIssues: Issue[];
  private maxIssues: number;
  private log: Logger;
  constructor(
    issueHintFilePath: string,
    existingIssues: Issue[],
    maxIssues: number = NUMBER_OF_ISSUES_TO_CREATE
  ) {
    super(`VertexIssueControl: "${issueHintFilePath}"`);
    this.issueHintControl = new BigQuerySQLControl(issueHintFilePath);
    this.securityGroupsControl = new BigQuerySQLControl(
      'security-groups/security_groups_rules.sql'
    );
    this.vertexService = new VertexService();
    this.firewallRemediationService = new FirewallRemediationService();
    this.existingIssues = existingIssues;
    this.maxIssues = maxIssues;
    this.log = log.child({
      context: 'VertexIssueControl'
    });
  }

  private getLastCreatedIssuesDate(): Date | undefined {
    if (!this.existingIssues.length) {
      return undefined;
    }

    const issueTimes = this.existingIssues.map((issue) =>
      issue.createdTime ? issue.createdTime.getTime() : 0
    );

    return new Date(Math.max(...issueTimes));
  }

  public async execute(benchmark: Benchmark): Promise<ControlResultData[]> {
    try {
      // Check if the last issue was created less than MIN_ISSUE_CREATION_BUFFER
      const entities = [...benchmark.entities] as CloudEntity[];

      const lastCreatedIssueDate = this.getLastCreatedIssuesDate();
      if (
        lastCreatedIssueDate &&
        Date.now() - lastCreatedIssueDate.getTime() < MIN_ISSUE_CREATION_BUFFER
      ) {
        this.log.info(
          `Skipping vertex issue creation: last issue was created ${Math.floor((Date.now() - lastCreatedIssueDate.getTime()) / (1000 * 60 * 60))} hours ago, ` +
            `which is less than the minimum buffer`
        );
        return [];
      }

      let securityGroupsRules: SQLAwsSecurityGroupRuleWithName[] = [];

      try {
        securityGroupsRules = (await this.securityGroupsControl.execute(
          benchmark
        )) as SQLAwsSecurityGroupRuleWithName[];
      } catch (error) {
        if ((error as any)?.message?.includes('Not found')) {
          this.log.info('No security groups found');
          securityGroupsRules = [];
        }
      }

      const entitiesMap = this.createEntitiesMap(entities);

      if (!securityGroupsRules?.length || securityGroupsRules.length === 0) {
        const issueHints = await this.getIssueHints(benchmark);

        if (!issueHints.length) {
          this.log.info('No issue hints found');
          return [];
        }

        const issues = await this.processIssueHints(issueHints, entitiesMap);

        return issues.map((issue) => ({ issue }));
      }

      const issuesAnalyzer = new SecurityGroupIssueAnalyzer(
        benchmark,
        securityGroupsRules,
        entitiesMap
      );

      const issues = (await issuesAnalyzer.raiseIssues()) as Issue[];

      return issues.map((issue) => ({ issue }));
    } catch (error) {
      this.log.error(`Issues Control Failed: ${error}`);

      return [];
    }
  }

  private async getIssueHints(benchmark: Benchmark): Promise<IssueHint[]> {
    const sqlControlResults = await this.issueHintControl.execute(benchmark);
    if (!sqlControlResults) return [];

    return sqlControlResults.map(
      (controlResult: any) => controlResult['issue_hint']
    );
  }

  private createEntitiesMap(entities: CloudEntity[]): Map<string, CloudEntity> {
    return new Map(entities.map((entity) => [entity.id, entity]));
  }

  private async processIssueHints(
    issueHints: IssueHint[],
    entitiesMap: Map<string, CloudEntity>
  ): Promise<Issue[]> {
    this.log.info(`Processing ${issueHints.length} issue hints`);

    // Filter invalid hints
    const validHints = issueHints.filter((hint) => this.isValidHint(hint));
    this.log.debug(
      `Filtered out ${issueHints.length - validHints.length} invalid hints`
    );

    // Filter duplicate hints
    const uniqueHints = validHints.filter(
      (hint) => !this.isDuplicateIssue(hint)
    );

    this.log.debug(
      `Filtered out ${validHints.length - uniqueHints.length} duplicate hints`
    );

    // Process up to maxIssues
    const hintsToProcess = uniqueHints.slice(0, this.maxIssues);
    if (uniqueHints.length > this.maxIssues) {
      this.log.debug(
        `Limited to ${this.maxIssues} hints due to generated issues limit`
      );
    }

    const issues: Issue[] = [];
    for (const hint of hintsToProcess) {
      const issue = await this.createIssueFromHint(hint, entitiesMap);

      if (!issue) {
        this.log.info(
          `No valid issue to create from hint: ${JSON.stringify(hint)}`
        );
        continue;
      }

      if (issue.trafficPatterns.length > MAX_TRAFFIC_PATTERNS_PER_ISSUE) {
        this.log.info(
          `Skipping issue "${issue.title}" because it has more than ${MAX_TRAFFIC_PATTERNS_PER_ISSUE} traffic patterns`
        );
        continue;
      }

      this.log.info(
        `Successfully created issue with ${issue.severity} severity: "${issue.title}"`
      );
      issues.push(issue);
    }

    return issues;
  }

  private isDuplicateIssue(hint: IssueHint): boolean {
    // convert hint patterns from SQLTrafficEvent to UniqueTraffic
    const hintPatterns = hint.trafficPatterns.map((pattern) =>
      transformSQLTrafficPatterns(pattern)
    );

    return this.existingIssues.some((existingIssue) => {
      // Skip resolved or dismissed issues
      if (existingIssue.status !== 'open') return false;

      // Compare traffic pattern IDs
      const existingPatterns = existingIssue.trafficPatterns;

      // If the existing issue has no traffic patterns, it's not a duplicate
      if (existingPatterns.length === 0) return false;

      // Check if all traffic patterns in issue exists in hint
      return existingPatterns.every((pattern) =>
        hintPatterns.some(
          (hintPattern) =>
            hintPattern.src.addr === pattern.src.addr &&
            hintPattern.dst.addr === pattern.dst.addr &&
            hintPattern.src.id === pattern.src.id &&
            hintPattern.dst.id === pattern.dst.id &&
            hintPattern.port === pattern.port
        )
      );
    });
  }

  private async createIssueFromHint(
    hint: IssueHint,
    entitiesMap: Map<string, CloudEntity>
  ): Promise<Issue | null> {
    try {
      const hintTrafficPatterns = hint.trafficPatterns.map((pattern) =>
        transformSQLTrafficPatterns(pattern)
      );

      const issue = await this.vertexService.generateIssue(hintTrafficPatterns);

      // No issue found
      if (!issue) return null;

      // Keeping both old and new traffic patterns for now
      // To get ready for the new traffic pattern format
      const trafficPatternsFromIssue = issue.trafficPatternsIds
        .map((id) => hintTrafficPatterns.find((pattern) => pattern.id === id))
        .filter((pattern) => pattern !== undefined);

      const relatedEntities = Array.from(
        new Set(
          trafficPatternsFromIssue.flatMap((pattern) =>
            [
              entitiesMap.get(pattern.src.id),
              entitiesMap.get(pattern.dst.id)
            ].filter((entity) => entity !== undefined)
          )
        )
      );

      const remediation = this.firewallRemediationService.createRemediation(
        trafficPatternsFromIssue
      );

      return {
        entityId: hintTrafficPatterns[0].dst.id,
        category: issue.category,
        status: 'open',
        title: issue.title,
        subTitle: issue.subTitle,
        explanation: issue.explanation,
        severity: issue.severity,
        relatedEntitiesIds: relatedEntities.map((entity) => entity.id),
        trafficPatterns: trafficPatternsFromIssue,
        remediation
      };
    } catch (error) {
      throw new Error(`Error creating issue from hint: ${error}`);
    }
  }

  private isValidHint(hint: IssueHint): boolean {
    if (!hint.trafficPatterns?.length) {
      this.log.debug('Skipping hint: No traffic patterns found');
      return false;
    }
    return true;
  }
}

export default VertexIssueControl;
