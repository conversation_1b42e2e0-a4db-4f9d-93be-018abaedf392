import log from '../../logger';
import {
  DetectionWithControlResults,
  EndpointEntity,
  Label,
  LabelAssignment
} from '@globalTypes/ndrBenchmarkTypes';
import { DetectionConfidenceService } from '../../services/vertex-ai';
import { Benchmark } from '../types/benchmarkTypes';
import BaseControl from './BaseControl';
import { ControlResultData } from './types';
import * as firestoreOperations from '../../firestore';

export type DetectionWithConfidence = DetectionWithControlResults & {
  confidence?: {
    confidenceScore: number;
    explanation: string;
    riskFactors: string[];
    entityAnalysis: {
      criticalEntities: string[];
      entityTypes: string[];
    };
  };
};

class DetectionConfidenceControl extends BaseControl {
  private organizationId: string;
  private detectionConfidenceService: DetectionConfidenceService;

  constructor(organizationId: string) {
    super('DetectionConfidenceControl');
    this.organizationId = organizationId;
    this.detectionConfidenceService = new DetectionConfidenceService(
      organizationId
    );
  }

  public async execute(benchmark: Benchmark): Promise<ControlResultData[]> {
    const startTime = performance.now();

    try {
      const detections = [...benchmark.detections, ...benchmark.aiDetections];
      log.info(
        `Starting detection confidence scoring for ${detections.length} detections`
      );

      if (detections.length === 0) {
        log.info('No detections to process for confidence scoring');
        return [];
      }

      const labels = benchmark.allLabels || [];
      const labelAssignments = benchmark.allLabelAssignments || [];

      log.info(
        `Retrieved ${labels.length} labels and ${labelAssignments.length} label assignments`
      );

      // Generate confidence scores for all detections
      const confidenceResults =
        await this.detectionConfidenceService.generateConfidenceScores(
          detections,
          benchmark.entities,
          labels,
          labelAssignments
        );

      // Enrich detections with confidence scores
      const enrichedDetections: DetectionWithConfidence[] = detections.map(
        (detection) => {
          const confidence = confidenceResults.get(detection.id);

          if (confidence) {
            return {
              ...detection,
              confidence: {
                confidenceScore: confidence.confidenceScore,
                explanation: confidence.explanation,
                riskFactors: confidence.riskFactors,
                entityAnalysis: confidence.entityAnalysis
              }
            };
          }

          return detection;
        }
      );

      // Update the benchmark with enriched detections
      benchmark.detections = enrichedDetections;

      const endTime = performance.now();
      const executionTime = Math.round(endTime - startTime);

      log.info(`Completed detection confidence scoring in ${executionTime}ms`, {
        totalDetections: benchmark.detections.length,
        scoredDetections: confidenceResults.size,
        averageConfidence: this.calculateAverageConfidence(confidenceResults),
        executionTimeMs: executionTime
      });

      // Return results for potential Firestore syncing
      const results: ControlResultData[] = Array.from(
        confidenceResults.entries()
      ).map(([detectionId, confidence]) => ({
        detectionConfidence: {
          detectionId,
          ...confidence
        }
      }));

      return results;
    } catch (error) {
      log.error('Error in detection confidence scoring', {
        error: (error as any)?.message,
        organizationId: this.organizationId
      });
      throw error;
    }
  }

  /**
   * Calculate average confidence score for logging purposes
   */
  private calculateAverageConfidence(
    confidenceResults: Map<string, { confidenceScore: number }>
  ): number {
    if (confidenceResults.size === 0) return 0;

    const totalScore = Array.from(confidenceResults.values()).reduce(
      (sum, result) => sum + result.confidenceScore,
      0
    );

    return Math.round(totalScore / confidenceResults.size);
  }

  /**
   * Get detections with high confidence scores (80+)
   */
  public getHighConfidenceDetections(
    detections: DetectionWithConfidence[]
  ): DetectionWithConfidence[] {
    return detections.filter(
      (detection) =>
        detection.confidence && detection.confidence.confidenceScore >= 80
    );
  }

  /**
   * Get detections with low confidence scores (below 40)
   */
  public getLowConfidenceDetections(
    detections: DetectionWithConfidence[]
  ): DetectionWithConfidence[] {
    return detections.filter(
      (detection) =>
        detection.confidence && detection.confidence.confidenceScore < 40
    );
  }

  /**
   * Sort detections by confidence score (highest first)
   */
  public sortDetectionsByConfidence(
    detections: DetectionWithConfidence[]
  ): DetectionWithConfidence[] {
    return [...detections].sort((a, b) => {
      const scoreA = a.confidence?.confidenceScore ?? 0;
      const scoreB = b.confidence?.confidenceScore ?? 0;
      return scoreB - scoreA;
    });
  }
}

export default DetectionConfidenceControl;
