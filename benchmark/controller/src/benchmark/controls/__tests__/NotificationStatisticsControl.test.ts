import { combineNotifications } from '../../../firestore/getNotifications';
import { Notification } from '../../../globalTypes/ndrBenchmarkTypes/notificationTypes';

describe('combineNotifications', () => {
  test('should combine two sets of completely different notifications', () => {
    const benchmarkNotifications = [
      { title: 'Notification 1', severity: 'low', timestamp: new Date() },
      { title: 'Notification 2', severity: 'medium', timestamp: new Date() }
    ] as unknown as Notification[];

    const fireStoreNotifications = [
      { title: 'Notification 3', severity: 'high', timestamp: new Date() },
      { title: 'Notification 4', severity: 'critical', timestamp: new Date() }
    ] as unknown as Notification[];

    const combinedNotifications = combineNotifications({
      benchmarkNotifications,
      fireStoreNotifications
    });

    expect(combinedNotifications.length).toBe(4);
    expect(combinedNotifications).toEqual(
      expect.arrayContaining([
        ...benchmarkNotifications,
        ...fireStoreNotifications
      ])
    );
  });

  test('should remove duplicates when combining notifications', () => {
    const benchmarkNotifications = [
      { title: 'Notification 1', severity: 'low', timestamp: new Date() },
      { title: 'Notification 2', severity: 'medium', timestamp: new Date() }
    ] as unknown as Notification[];

    const fireStoreNotifications = [
      { title: 'Notification 2', severity: 'medium', timestamp: new Date() },
      { title: 'Notification 3', severity: 'high', timestamp: new Date() }
    ] as unknown as Notification[];

    const combinedNotifications = combineNotifications({
      benchmarkNotifications,
      fireStoreNotifications
    });

    expect(combinedNotifications.length).toBe(3);
    expect(combinedNotifications).toContainEqual(benchmarkNotifications[0]);
    expect(combinedNotifications).toContainEqual(benchmarkNotifications[1]);
    expect(combinedNotifications).toContainEqual(fireStoreNotifications[1]);
  });

  test('should return unique set when notifications are exactly the same', () => {
    const benchmarkNotifications = [
      { title: 'Notification 1', severity: 'low', timestamp: new Date() },
      { title: 'Notification 2', severity: 'medium', timestamp: new Date() }
    ] as unknown as Notification[];

    const fireStoreNotifications = [
      { title: 'Notification 1', severity: 'low', timestamp: new Date() },
      { title: 'Notification 2', severity: 'medium', timestamp: new Date() }
    ] as unknown as Notification[];

    const combinedNotifications = combineNotifications({
      benchmarkNotifications,
      fireStoreNotifications
    });

    expect(combinedNotifications.length).toBe(2);
    expect(combinedNotifications).toEqual(benchmarkNotifications);
  });

  test('should handle empty arrays correctly', () => {
    const emptyResult = combineNotifications({
      benchmarkNotifications: [],
      fireStoreNotifications: []
    });
    expect(emptyResult).toEqual([]);

    const benchmarkOnly = combineNotifications({
      benchmarkNotifications: [
        { title: 'Test', severity: 'low', timestamp: new Date() }
      ] as unknown as Notification[],
      fireStoreNotifications: []
    });
    expect(benchmarkOnly.length).toBe(1);

    const firestoreOnly = combineNotifications({
      benchmarkNotifications: [],
      fireStoreNotifications: [
        { title: 'Test', severity: 'low', timestamp: new Date() }
      ] as unknown as Notification[]
    });
    expect(firestoreOnly.length).toBe(1);
  });

  test('should handle notifications with different properties but same key fields', () => {
    const benchmarkNotifications = [
      {
        title: 'Same',
        severity: 'low',
        timestamp: new Date(),
        extraField1: 'test'
      }
    ] as unknown as Notification[];

    const fireStoreNotifications = [
      {
        title: 'Same',
        severity: 'low',
        timestamp: new Date(),
        extraField2: 'different'
      }
    ] as unknown as Notification[];

    const result = combineNotifications({
      benchmarkNotifications,
      fireStoreNotifications
    });
    expect(result.length).toBe(2);
  });

  test('should preserve order with large datasets', () => {
    const largeBenchmarkSet = Array.from({ length: 1000 }, (_, i) => ({
      title: `Notification ${i}`,
      severity: 'low',
      timestamp: new Date()
    })) as unknown as Notification[];

    const largeFirestoreSet = Array.from({ length: 1000 }, (_, i) => ({
      title: `Firestore ${i}`,
      severity: 'high',
      timestamp: new Date()
    })) as unknown as Notification[];

    const result = combineNotifications({
      benchmarkNotifications: largeBenchmarkSet,
      fireStoreNotifications: largeFirestoreSet
    });

    expect(result.length).toBe(2000);
    expect(result[0].title).toContain('Notification');
    expect(result[result.length - 1].title).toContain('Firestore');
  });

  test('should handle notifications with special characters in title', () => {
    const benchmarkNotifications = [
      {
        title: 'Test!@#$%^&*()',
        severity: 'low',
        timestamp: new Date()
      }
    ] as unknown as Notification[];

    const fireStoreNotifications = [
      {
        title: 'Test!@#$%^&*()',
        severity: 'low',
        timestamp: new Date()
      }
    ] as unknown as Notification[];

    const result = combineNotifications({
      benchmarkNotifications,
      fireStoreNotifications
    });
    expect(result.length).toBe(1);
  });

  test('should handle notifications with undefined or null fields', () => {
    const benchmarkNotifications = [
      {
        title: undefined,
        severity: null,
        timestamp: new Date()
      }
    ] as unknown as Notification[];

    const fireStoreNotifications = [
      {
        title: null,
        severity: undefined,
        timestamp: new Date()
      }
    ] as unknown as Notification[];

    const result = combineNotifications({
      benchmarkNotifications,
      fireStoreNotifications
    });
    expect(result.length).toBe(2);
  });

  test('should not have any duplicate notifications', () => {
    const benchmarkNotifications = [
      {
        title: 'Test Notification 1',
        severity: 'high',
        timestamp: new Date()
      },
      {
        title: 'Test Notification 2',
        severity: 'medium',
        timestamp: new Date()
      }
    ] as unknown as Notification[];

    const fireStoreNotifications = [
      {
        title: 'Test Notification 1',
        severity: 'high',
        timestamp: new Date()
      },
      {
        title: 'Test Notification 3',
        severity: 'low',
        timestamp: new Date()
      }
    ] as unknown as Notification[];

    const result = combineNotifications({
      benchmarkNotifications,
      fireStoreNotifications
    });

    // Should only have 3 unique notifications
    expect(result.length).toBe(3);

    // Verify no duplicates by checking unique titles
    const uniqueTitles = new Set(result.map((n) => n.title));
    expect(uniqueTitles.size).toBe(result.length);
  });
});
