import { BigQuery, BigQueryTimestamp } from '@google-cloud/bigquery';
import BigQuerySQLControl from '../BigQuerySQLControl';
import * as fs from 'fs';
import path from 'path';
import * as config from '../../../config';

// Mock the modules
jest.mock('@google-cloud/bigquery');
jest.mock('fs');
jest.mock('path');
jest.mock('../../../config', () => ({
  Global: {
    organizationId: 'test-org'
  }
}));

// Mock the big-query-client module
jest.mock('@big-query-client/bigquery', () => ({
  getBigQueryClient: jest.fn()
}));

describe('BigQuerySQLControl', () => {
  const mockReadFileSync = fs.readFileSync as jest.Mock;
  const mockExecuteQuery = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockReadFileSync.mockReturnValue('SELECT * FROM {{dataset}}.table');
    mockExecuteQuery.mockResolvedValue([]);

    // Setup the mock implementation for getBigQueryClient
    const { getBigQueryClient } = require('@big-query-client/bigquery');
    (getBigQueryClient as jest.Mock).mockResolvedValue({
      executeQuery: mockExecuteQuery
    });
  });

  it('should handle both types of BigQueryTimestamp objects', async () => {
    const timestamp = new Date('2024-01-01T00:00:00.000Z').getTime();

    // Mock of custom client's timestamp
    const customClientTimestamp = {
      value: timestamp,
      constructor: { name: 'BigQueryTimestamp' }
    };

    // Mock of official client's timestamp using duck typing
    const officialClientTimestamp = {
      value: timestamp,
      constructor: { name: 'BigQueryTimestamp' },
      [Symbol.toStringTag]: 'BigQueryTimestamp' // Makes instanceof work
    };
    Object.setPrototypeOf(officialClientTimestamp, BigQueryTimestamp.prototype);

    const mockQueryResult = {
      customTimestamp: customClientTimestamp,
      officialTimestamp: officialClientTimestamp
    };

    mockExecuteQuery.mockResolvedValueOnce([mockQueryResult]);

    const control = new BigQuerySQLControl('test.sql');
    const result = await control.execute();

    // Both types should be converted to ISO strings at the top level
    expect(result).toEqual([
      {
        customTimestamp: '2024-01-01T00:00:00.000Z',
        officialTimestamp: '2024-01-01T00:00:00.000Z'
      }
    ]);
  });

  it('should handle BigQueryTimestamp objects', async () => {
    const timestamp = new Date('2024-01-01T00:00:00.000Z').getTime();
    const mockTimestamp = {
      value: timestamp,
      constructor: { name: 'BigQueryTimestamp' }
    };

    const mockQueryResult = {
      timestamp: mockTimestamp,
      nested: {
        timestamp: mockTimestamp
      },
      array: [mockTimestamp]
    };

    mockExecuteQuery.mockResolvedValueOnce([mockQueryResult]);

    const control = new BigQuerySQLControl('test.sql');
    const result = await control.execute();

    // Expect timestamps to be converted to ISO strings at the top level
    expect(result).toEqual([
      {
        timestamp: '2024-01-01T00:00:00.000Z',
        nested: {
          timestamp: mockTimestamp
        },
        array: [mockTimestamp]
      }
    ]);
  });

  it('should handle failed queries when allowFailure is true', async () => {
    mockExecuteQuery.mockRejectedValueOnce(new Error('Query failed'));

    const control = new BigQuerySQLControl('test.sql', true);
    const result = await control.execute();

    expect(result).toEqual([]);
  });

  it('should throw error for failed queries when allowFailure is false', async () => {
    mockExecuteQuery.mockRejectedValueOnce(new Error('Query failed'));

    const control = new BigQuerySQLControl('test.sql', false);
    await expect(control.execute()).rejects.toThrow('Query failed');
  });

  it('should handle string JSON data', async () => {
    const jsonString = JSON.stringify({ key: 'value' });
    mockExecuteQuery.mockResolvedValueOnce([{ data: jsonString }]);

    const control = new BigQuerySQLControl('test.sql');
    const result = await control.execute();

    // Expect JSON strings to be parsed into objects
    expect(result).toEqual([{ data: { key: 'value' } }]);
  });

  it('should preserve non-timestamp objects', async () => {
    const mockData = {
      number: 42,
      string: 'test',
      boolean: true,
      null: null,
      object: { nested: 'value' }
    };

    mockExecuteQuery.mockResolvedValueOnce([mockData]);

    const control = new BigQuerySQLControl('test.sql');
    const result = await control.execute();

    expect(result).toEqual([mockData]);
  });
});
