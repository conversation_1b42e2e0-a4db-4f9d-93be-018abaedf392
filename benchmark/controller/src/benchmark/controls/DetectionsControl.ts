import { v4 as uuidv4 } from 'uuid';

import log from '../../logger';
import {
  DetectionControl,
  DETECTION_STATUS,
  DetectionControlMetric,
  CloudEntity,
  UniqueTraffic,
  ORIGIN_TYPES,
  DetectionWithControlResults,
  SQLTrafficEvent
} from '@globalTypes/ndrBenchmarkTypes';
import { generateServiceAccountEmail } from '../../big-query-client/bigquery/utils';
import {
  BigQueryClient,
  getBigQueryClient
} from '../../big-query-client/bigquery';
import {
  grantImpersonationRoles,
  grantTableAccess
} from '../../big-query-client/bigquery/access';

import { transformSQLTrafficPatterns } from '../../services/issues/utils';
import {
  RECENT_TRAFFIC_TABLE_NAME,
  ENRICHED_TABLE_NAME,
  PORT0_AI_CONTROL_ID
} from '../consts';
import {
  updateLastRunAt,
  generateDetectionQuery,
  arrayToMap,
  enrichPort0Query
} from '../../utils/detectionUtils';
import { generateDetectionTags } from '../../utils/detectionTagUtils';
import {
  ControlResultData,
  HandleControlResults,
  IProcessDetectionResultsParams
} from './types';
import {
  getActiveDetectionControls,
  getDetectionMetrics
} from '../../firestore/getDetections';
import { Benchmark } from '../types/benchmarkTypes';
import BaseControl from './BaseControl';

class DetectionsControl extends BaseControl {
  private organizationId: string;
  private serviceAccountEmail: string;
  private entitiesMap: Map<string, CloudEntity>;
  private allowFailure: boolean;

  constructor(organizationId: string, allowFailure = false) {
    super('DetectionsControl');
    this.organizationId = organizationId;
    this.serviceAccountEmail = generateServiceAccountEmail(organizationId);
    this.allowFailure = allowFailure;
    this.entitiesMap = new Map();
  }

  public async execute(benchmark: Benchmark): Promise<ControlResultData[]> {
    log.debug('Beginning detection control execution...');

    try {
      this.entitiesMap = new Map(
        benchmark.entities.map((entity) => [entity.id, entity])
      );

      const detectionControls = await getActiveDetectionControls(
        this.organizationId
      );

      await grantTableAccess({
        datasetId: this.organizationId,
        tableId: RECENT_TRAFFIC_TABLE_NAME,
        serviceAccountEmail: this.serviceAccountEmail
      });

      await grantTableAccess({
        datasetId: this.organizationId,
        tableId: ENRICHED_TABLE_NAME,
        serviceAccountEmail: this.serviceAccountEmail
      });

      const detectionsExecutionsResults =
        await this.executeControlsAndUpdateExecutionTime(detectionControls);

      // Update lastRunAt for all controls that were executed
      await updateLastRunAt(this.organizationId, detectionControls);

      const results = await this.processDetectionResults({
        detectionControls,
        detectionsExecutionsResults
      });

      log.debug(`Detection controls execution completed successfully`, {
        organizationId: this.organizationId,
        detectionsCount: results.length
      });

      return results;
    } catch (err) {
      log.error(`Error computing detections: ${(err as Error).message}`, {
        organizationId: this.organizationId,
        error: err
      });

      if (this.allowFailure) {
        return [];
      }

      throw err;
    }
  }

  private async executeDetectionControl(
    bigQueryClient: BigQueryClient,
    control: DetectionControl
  ): Promise<any[]> {
    try {
      const query = generateDetectionQuery({
        organizationId: this.organizationId,
        query: control.query,
        interval: control.interval
      });

      if (control.origin === ORIGIN_TYPES.PORT0) {
        log.debug(`Original query`, {
          controlId: control.id,
          organizationId: this.organizationId,
          query
        });

        const generatedEnrichedQuery = await enrichPort0Query(query);

        log.debug('Generated enriched query', {
          detectionControlId: control.id,
          query: generatedEnrichedQuery
        });

        const [queryCost, enrichedQueryCost] = await Promise.all([
          bigQueryClient.calculateQueryCost({
            query: query
          }),
          bigQueryClient.calculateQueryCost({
            query: generatedEnrichedQuery
          })
        ]);

        log.info('Query cost breakdown for PORT0 detection control', {
          detectionControlId: control.id,
          interval: control.interval,
          organizationId: this.organizationId,
          costs: {
            initialQuery: {
              cost: queryCost.estimatedCost,
              bytesProcessed: queryCost.totalBytesProcessed,
              bytesBilled: queryCost.totalBytesBilled
            },
            enrichedQuery: {
              cost: enrichedQueryCost.estimatedCost,
              bytesProcessed: enrichedQueryCost.totalBytesProcessed,
              bytesBilled: enrichedQueryCost.totalBytesBilled
            }
          },
          totalCost: queryCost.estimatedCost + enrichedQueryCost.estimatedCost,
          totalBytesProcessed:
            queryCost.totalBytesProcessed +
            enrichedQueryCost.totalBytesProcessed
        });

        log.debug(`Executing detection control`, {
          controlId: control.id,
          organizationId: this.organizationId,
          generatedEnrichedQuery
        });

        const enrichedResult = await bigQueryClient.executeQuery(
          generatedEnrichedQuery
        );

        log.debug(`Detection control executed successfully: ${control.id}`, {
          organizationId: this.organizationId,
          generatedEnrichedQuery
        });
        return enrichedResult;
      }

      log.debug(`Executing detection control`, {
        controlId: control.id,
        organizationId: this.organizationId,
        query
      });

      const res = await bigQueryClient.executeQuery(query);

      log.debug(`Detection control executed successfully: ${control.id}`, {
        organizationId: this.organizationId,
        query
      });

      return res;
    } catch (err) {
      log.error(`Error executing detection control`, {
        detectionControlId: control.id,
        organizationId: this.organizationId,
        error: err
      });

      // check and grant impersonation permission if the error is due to missing permission
      await grantImpersonationRoles({
        errorMessage: (err as Error).message,
        organizationId: this.organizationId
      });

      return [];
    }
  }

  private generateDetection(
    control: DetectionControl,
    matches: UniqueTraffic[]
  ): DetectionWithControlResults {
    const relatedEntitiesIds = this.getRelatedEntitiesIds(matches);
    const tags = generateDetectionTags(matches);

    return {
      id: uuidv4(),
      controlId: control.id,
      category: control.category,
      title: control.name,
      subTitle: control.subTitle ?? '',
      explanation: control.explanation ?? '',
      severity: control.severity,
      controlResults: matches,
      relatedEntitiesIds,
      status: DETECTION_STATUS.OPEN,
      createdTime: new Date(),
      updatedTime: new Date(),
      tags
    };
  }

  private async getDetectionControlsMetricsMap(): Promise<
    Record<string, DetectionControlMetric>
  > {
    const detectionMetrics = await getDetectionMetrics(this.organizationId);
    const detectionControlsMetricsMap: Record<string, DetectionControlMetric> =
      arrayToMap(detectionMetrics);

    return detectionControlsMetricsMap;
  }

  private async processDetectionResults({
    detectionControls,
    detectionsExecutionsResults
  }: IProcessDetectionResultsParams): Promise<ControlResultData[]> {
    log.info('Processing detection results', {
      organizationId: this.organizationId
    });

    const results: ControlResultData[] = [];

    for (const [index, control] of detectionControls.entries()) {
      const matches =
        detectionsExecutionsResults[index].status === 'rejected'
          ? []
          : detectionsExecutionsResults[index].value;

      const { detections } = this.handleControl(matches, control);

      // push the updated metric and the created detection to the results array
      for (const detection of detections) {
        results.push({
          detection: detection
        });
      }
    }

    log.info('Processing detection results completed', {
      organizationId: this.organizationId,
      resultsCount: results.length
    });

    return results;
  }

  private async executeControlsAndUpdateExecutionTime(
    detectionControls: DetectionControl[]
  ): Promise<PromiseSettledResult<any[]>[]> {
    const bigQueryClient = await getBigQueryClient(
      { datasetId: this.organizationId },
      this.serviceAccountEmail
    );

    const detectionsExecutionsPromises: Promise<any[]>[] = [];

    for (const control of detectionControls) {
      if (control.id === PORT0_AI_CONTROL_ID) {
        continue;
      }

      detectionsExecutionsPromises.push(
        this.executeDetectionControl(bigQueryClient, control)
      );
    }

    const detectionsExecutionsResults = await Promise.allSettled(
      detectionsExecutionsPromises
    );

    return detectionsExecutionsResults;
  }

  private getRelatedEntitiesIds(matches: UniqueTraffic[]): string[] {
    return Array.from(
      new Set(
        matches.flatMap((match) =>
          [
            this.entitiesMap.get(match.src.id),
            this.entitiesMap.get(match.dst.id)
          ].filter((entity) => entity !== undefined)
        )
      )
    ).map((entity) => entity.id);
  }

  private handlePort0Control(
    matches: any[],
    control: DetectionControl
  ): HandleControlResults {
    const detections: DetectionWithControlResults[] = [];
    for (const match of matches) {
      const controlResults = match.results ?? [];
      const transformedControlResults = controlResults
        .slice(0, 100)
        .map((result: SQLTrafficEvent) => transformSQLTrafficPatterns(result));

      const detection = this.generateDetection(
        control,
        transformedControlResults
      );
      detections.push(detection);

      log.debug(`New detection was created`, {
        organizationId: this.organizationId,
        detectionControlId: control.id,
        detectionId: detection.id,
        origin: control.origin,
        matchesCount: matches.length
      });
    }
    return {
      detections
    };
  }

  private handleUserControl(
    matches: UniqueTraffic[],
    control: DetectionControl
  ): HandleControlResults {
    const detections: DetectionWithControlResults[] = [];
    for (const match of matches) {
      if (match.sessionCount >= control.minThreshold) {
        const detection = this.generateDetection(control, [match]);
        detections.push(detection);

        log.debug(`New detection was created`, {
          organizationId: this.organizationId,
          detectionControlId: control.id,
          detectionId: detection.id,
          origin: control.origin,
          matchesCount: matches.length
        });
      }
    }

    return {
      detections
    };
  }

  private handleControl(
    matches: any[],
    control: DetectionControl
  ): HandleControlResults {
    if (control.origin === ORIGIN_TYPES.PORT0) {
      return this.handlePort0Control(matches, control);
    } else {
      const transformedMatches = matches
        .slice(0, 100)
        .map((match: SQLTrafficEvent) => transformSQLTrafficPatterns(match));
      return this.handleUserControl(transformedMatches, control);
    }
  }
}

export default DetectionsControl;
