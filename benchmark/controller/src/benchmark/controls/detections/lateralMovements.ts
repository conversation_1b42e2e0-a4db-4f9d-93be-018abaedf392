export const lateralMovement = [
  {
    name: 'Repeated Internal SMB Access Across Hosts',
    category: 'Lateral Movement',
    subTitle:
      'This pattern may indicate \`lateral movement\`, \`network reconnaissance\`, or \`host scanning\` over \`SMB or RPC protocols\`.',
    explanation:
      "Finds \`internal machines\` that are connecting to \`20 or more unique internal hosts\` using \`SMB or related management ports (445, 139, 137, 138, 135, 593)\` within a \`10-minute window'.",
    query: `
      SELECT 
        src_addr, 
        src_name, 
        COUNT(DISTINCT dst_addr) AS unique_dst_hosts, 
        src_process, 
        port, 
        SUM(session_count) AS total_sessions 
      FROM {{table}} 
      WHERE port in (445, 139, 137, 138, 135, 593) 
        AND src_type != 'internet' 
        AND dst_type != 'internet' 
        AND src_process != '' 
        AND NOT (
          'Domain Controllers' IN UNNEST(src_common.activeDirectoryOu)
          AND src_process IN (
            'ntfrs.exe', 
            'dfsrs.exe', 
            'lsass.exe', 
            'rpcss', 
            'dcomlaunch', 
            'ntoskrnl.exe', 
            'Microsoft.Tri.Sensor.exe', 
            'svchost.exe', 
            'OktaAgentService.exe'
          )
        ) 
      GROUP BY 
        src_addr, 
        src_name, 
        src_process, 
        port 
      HAVING COUNT(DISTINCT dst_addr) >= 20
    `,
    severity: 'medium',
    interval: 10
  },
  {
    name: 'Internal RDP Sessions with Unrecognized Processes',
    category: 'Lateral Movement',
    subTitle:
      'Highlights \`suspicious or unauthorized RDP usage\`, which could suggest \`lateral movement\` or \`privilege escalation attempts\`.',
    explanation:
      'Detects \`RDP sessions within the internal network\` (\`dst_port\` 3389 or 3388) where the \`destination process\` is \`not part of the standard system processes list\` (like svchost.exe, lsass.exe, etc.).',
    query: `
      SELECT 
        src_addr, 
        dst_addr, 
        port, 
        src_process, 
        dst_process, 
        SUM(session_count) AS session_count 
      FROM {{table}} 
      WHERE src_type != 'internet' 
        AND dst_type != 'internet' 
        AND port IN (3389, 3388) 
        AND dst_process IS NOT NULL 
        AND dst_process != '' 
        AND dst_process NOT IN (
          'svchost.exe', 
          'lsass.exe', 
          'csrss.exe', 
          'winlogon.exe'
        ) 
      GROUP BY 
        src_addr, 
        dst_addr, 
        port, 
        src_process, 
        dst_process
    `,
    severity: 'high',
    interval: 10
  },
  {
    name: 'Internal SMB Sessions with Unrecognized Processes',
    category: 'Lateral Movement',
    subTitle:
      'Flags \`unexpected SMB sessions\`, which could indicate \`lateral movement\`, \`file staging\`, or \`unauthorized file-sharing activity\`.',
    explanation:
      'Detects \`SMB connections within the internal network\` (\`dst_port\` 445, 139, 137, 138) where the \`destination process\` is \`not part of the standard SMB system processes list\` (such as svchost.exe, smbd, etc.).',
    query: `
      SELECT 
        src_addr, 
        dst_addr, 
        port, 
        src_process, 
        dst_process, 
        SUM(session_count) AS session_count 
      FROM {{table}} 
      WHERE src_type != 'internet' 
        AND dst_type != 'internet' 
        AND port IN (445, 139, 137, 138) 
        AND dst_process IS NOT NULL 
        AND dst_process != '' 
        AND dst_process NOT IN (
          'svchost.exe', 
          'smbd', 
          'ntoskrnl.exe', 
          'system', 
          'CynetMS.exe'
        ) 
      GROUP BY 
        src_addr, 
        dst_addr, 
        port, 
        src_process, 
        dst_process
    `,
    severity: 'medium',
    interval: 10
  },
  {
    name: 'Internal RPC Sessions with Unrecognized Processes',
    category: 'Lateral Movement',
    subTitle:
      'Highlights \`suspicious RPC usage\`, which could point to \`domain controller enumeration\`, \`DCOM abuse\`, or \`lateral movement\` within the network.',
    explanation:
      'Detects \`RPC connections within the internal network\` (\`dst_port\` 135 or 593) where the \`destination process\` is \`not part of the standard RPC system processes list\` (such as svchost.exe, lsass.exe, etc.).',
    query: `
      SELECT 
        src_addr, 
        dst_addr, 
        port, 
        src_process, 
        dst_process, 
        SUM(session_count) AS session_count 
      FROM {{table}} 
      WHERE src_type != 'internet' 
        AND dst_type != 'internet' 
        AND port IN (135, 593) 
        AND dst_process IS NOT NULL 
        AND dst_process != '' 
        AND dst_process NOT IN (
          'svchost.exe', 
          'lsass.exe', 
          'rpcss', 
          'dcomlaunch', 
          'CynetEPS.exe'
        ) 
        AND dst_process NOT LIKE '$$DeleteMesvchost.exe%'
      GROUP BY 
        src_addr, 
        dst_addr, 
        port, 
        src_process, 
        dst_process
    `,
    severity: 'medium',
    interval: 10
  },
  {
    name: 'Unusual RDP/SMB/SSH connections',
    category: 'Lateral Movement',
    subTitle:
      'Detects \`new or unusual connections\` using sensitive protocols (RDP, SMB, SSH) that have never been seen before in historical traffic.',
    explanation:
      'Identifies connections on ports 22 (SSH), 139/445 (SMB), and 3389 (RDP) between source and destination pairs that have no historical precedent. This could indicate \`unauthorized remote access\`, \`lateral movement\`, or \`unauthorized file sharing\`.',
    query: `
      ,recent_sensitive_connections AS (
        SELECT
          src_name,
          dst_name,
          port,
          SUM(session_count) AS recent_sessions
        FROM
          {{table}}
        WHERE
          port IN (22, 139, 445, 3389)
          AND (action = 'accept' OR action = 'firewall_allow')
        GROUP BY
          src_name, dst_name, port
      )
      ,known_pairs AS (
        SELECT DISTINCT
          src_name,
          dst_name,
          port
        FROM 
          {{completed30DaysTrafficExceptCurrentIntervalTableName}}
        WHERE
          port IN (22, 139, 445, 3389)
          AND (action = 'accept' OR action = 'firewall_allow')
      )
      ,unusual_connections AS (
        SELECT
          r.src_name,
          r.dst_name,
          r.port,
          r.recent_sessions
        FROM
          recent_sensitive_connections r
        LEFT JOIN
          known_pairs k
        ON
          r.src_name = k.src_name
          AND r.dst_name = k.dst_name
          AND r.port = k.port
        WHERE
          k.dst_name IS NULL  -- new pair
      )
      SELECT 
        src_name,
        dst_name,
        port,
        recent_sessions
      FROM unusual_connections
      ORDER BY recent_sessions DESC
    `,
    severity: 'high',
    interval: 180
  },
  {
    name: 'CertUtil to rare destinations',
    category: 'Lateral Movement',
    subTitle:
      'Detects \`suspicious CertUtil usage\` where the process is connecting to new or previously unseen destinations, which could indicate \`malicious file downloads\`, \`data exfiltration\`, or \`command execution\`.',
    explanation:
      'Identifies instances where \`CertUtil.exe\` is connecting to destinations it has never connected to before. This is suspicious because CertUtil is often abused for malicious purposes such as \`downloading files\`, \`encoding/decoding data\`, or \`executing commands\`.',
    query: `
      ,recent_certutil_connections AS (
        SELECT
          src_addr,
          dst_addr,
          src_process,
          SUM(session_count) AS recent_sessions
        FROM
          {{table}}
        WHERE
          LOWER(src_process) = 'certutil.exe'
        GROUP BY
          src_addr, dst_addr, src_process
      )
      ,baseline_certutil_pairs AS (
        SELECT DISTINCT
          src_addr,
          dst_addr
        FROM
          {{completed30DaysTrafficExceptCurrentIntervalTableName}}
        WHERE
          LOWER(src_process) = 'certutil.exe'
      )
      SELECT
        r.src_addr,
        r.dst_addr,
        r.src_process,
        r.recent_sessions
      FROM
        baseline_certutil_pairs b
      LEFT JOIN
        recent_certutil_connections r
      ON
        r.src_addr = b.src_addr AND r.dst_addr = b.dst_addr
      WHERE
        b.dst_addr IS NULL  -- new/unseen destination
      ORDER BY
        r.recent_sessions DESC
    `,
    severity: 'high',
    interval: 180
  }
];
