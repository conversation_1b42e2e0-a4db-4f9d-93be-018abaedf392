export const privilegedToolAbuse = [
  {
    name: 'New Internal Connections Using Administrative Tools',
    category: 'Privileged Tool Abuse',
    subTitle:
      'Detects \`new internal connections\` initiated by \`administrative tools\` that have no historical precedent, which could indicate \`lateral movement\`, \`privilege escalation\`, or \`unauthorized administrative access\`.',
    explanation:
      'Identifies connections from \`privileged administrative tools\` (PowerShell, WMIC, PsExec, WinRM, Rundll32, SSH, MSTSC) to \`internal destinations\` that have \`never been seen before\` in the last 30 days. This pattern could indicate \`malicious lateral movement\`, \`unauthorized administrative access\`, or \`privilege escalation attempts\` using legitimate administrative tools.',
    query: `
  ,suspicious_today AS (
      SELECT src_id, dst_id, src_process, src_cmd, src_addr, dst_addr, port, time
      FROM {{table}}
      WHERE REGEXP_CONTAINS(LOWER(src_process), r'(powershell|wmic|psexec|winrm|rundll32|ssh|mstsc)')
        AND NOT REGEXP_CONTAINS(src_cmd, r'(?i)windows defender advanced threat protection')
        AND REGEXP_CONTAINS(dst_addr, r'^10\\..*|^192\\.168\\..*|^172\\.(1[6-9]|2[0-9]|3[0-1])\\.') -- Internal
        AND src_id IS NOT NULL AND dst_id IS NOT NULL
    )

    SELECT st.src_id, st.dst_id, st.src_process, st.src_cmd, st.src_addr, st.dst_addr, st.port
    FROM suspicious_today st
    LEFT JOIN {{completed30DaysTrafficExceptCurrentIntervalSrcDstIdsTableName}} hist
      ON st.src_id = hist.src_id AND st.dst_id = hist.dst_id
    WHERE hist.src_id IS NULL
`,
    severity: 'high',
    interval: 1440
  }
];
