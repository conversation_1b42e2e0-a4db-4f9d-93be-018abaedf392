export const reconnaissanceScanningActivities = [
  {
    name: 'Port Scanning Activity',
    category: 'Reconnaissance Scanning Activities',
    subTitle:
      'Flags \`port scanning behavior\`, often used as \`reconnaissance\` to discover \`open services\` on target machines.',
    explanation:
      "Identifies \`internal hosts\` scanning \`20 or more unique destination ports\` on \`one or more targets\` within a \`10-minute window'.",
    query: `
      SELECT 
        src_addr, 
        src_process, 
        COUNT(DISTINCT port) AS unique_ports, 
        SUM(session_count) AS session_count 
      FROM {{table}} 
      where src_process IS NULL 
          OR src_process != 'svchost.exe'
      GROUP BY 
        src_addr, 
        src_process 
      HAVING COUNT(DISTINCT port) >= 20 
      ORDER BY unique_ports DESC
    `,
    severity: 'medium',
    interval: 10
  },
  {
    name: 'Internal Host Discovery via IP Scanning',
    category: 'Reconnaissance Scanning Activities',
    subTitle:
      'Flags \`internal horizontal scanning behavior\`, typically used for \`internal host discovery\` during \`internal network reconnaissance\`.',
    explanation:
      "Identifies \`internal hosts\` probing \`200 or more unique internal destination IPs\` on the \`same port\` within a \`10-minute window'.",
    query: `
      SELECT 
        src_addr, 
        src_name, 
        COUNT(DISTINCT dst_addr) AS unique_dst_addr, 
        src_process, 
        SUM(session_count) AS session_count, 
        port 
      FROM {{table}} 
      WHERE port NOT IN (135, 7680) 
        AND src_type != 'internet' 
        AND dst_type != 'internet' 
        AND (
          src_process IS NULL 
          OR src_process NOT IN (
            'svchost.exe', 
            'Microsoft.Tri.Sensor.exe'
          )
        ) 
      GROUP BY 
        src_addr, 
        src_name, 
        src_process, 
        port 
      HAVING COUNT(DISTINCT dst_addr) >= 200
    `,
    severity: 'medium',
    interval: 10
  },
  {
    name: 'Possible Brute Force',
    category: 'Credential Access',
    subTitle:
      'Detects possible brute force by identifying sources with high numbers of failed connection attempts to login service ports.',
    explanation:
      'Flags sources with repeated failed or blocked connection attempts to login ports (SSH, RDP, LDAP). This indicates possible brute force or password spraying.',
    query: `
      SELECT
        src_addr,
        dst_addr,
        port,
        COUNT(*) AS failed_attempts,
        MIN(time) AS first_time,
        MAX(time) AS last_time
      FROM
        {{table}}
      WHERE
        action = 'firewall_block'
        AND port IN (22, 3389, 389, 636) -- common auth ports
      GROUP BY
        src_addr, dst_addr, port
      HAVING
        failed_attempts >= 20
      ORDER BY
        failed_attempts DESC
      `,
    severity: 'high'
  },
  {
    name: 'Anomalous SMB connections and scanning activity',
    category: 'Reconnaissance Scanning Activities',
    subTitle:
      'Detects \`suspicious SMB activity\` where sources are either connecting to many different SMB servers or generating a high volume of SMB sessions in a short time period.',
    explanation:
      'Identifies sources that either connect to \`10 or more different SMB servers\` (ports 139, 445) within a \`1-minute window\` or generate \`50 or more SMB sessions\` in that same window. This could indicate \`SMB scanning\`, \`network enumeration\`, or \`lateral movement attempts\`.',
    query: `
      ,smb_activity AS (
        SELECT
          src_addr,
          dst_addr,
          port,
          TIMESTAMP_TRUNC(created_at, MINUTE) AS minute_bucket,
          SUM(session_count) AS session_count
        FROM 
          {{table}}
        WHERE
          port IN (139, 445)
          AND (action = 'accept' OR action = 'firewall_allow')
        GROUP BY
          src_addr, dst_addr, port, minute_bucket
      )
      ,fanout AS (
        SELECT
          src_addr,
          minute_bucket,
          port,
          COUNT(DISTINCT dst_addr) AS unique_targets,
          SUM(session_count) AS total_sessions
        FROM
          smb_activity
        GROUP BY
          src_addr, minute_bucket, port
      )
      SELECT  
        src_addr,
        port,
        minute_bucket,
        unique_targets,
        total_sessions
      FROM fanout
      WHERE
        unique_targets >= 10  -- suspicious: hitting 10+ different SMB servers in 1 min
        OR total_sessions >= 50
      ORDER BY
        unique_targets DESC, total_sessions DESC
    `,
    severity: 'high',
    interval: 10
  },
  {
    name: 'Failed connections to rare endpoints',
    category: 'Reconnaissance Scanning Activities',
    subTitle:
      'Detects \`failed connection attempts\` to new or rarely seen destinations, which could indicate \`network scanning\`, \`service enumeration\`, or \`attempted unauthorized access\`.',
    explanation:
      'Identifies sources that are attempting to connect to destinations that are either completely new (never seen before) or very rare (seen less than 5 times in the last 30 days). This could indicate \`network reconnaissance\`, \`vulnerability scanning\`, or \`attempted access to unauthorized systems\`.',
    query: `
      ,failed_connections AS (
        SELECT
          src_addr,
          dst_addr,
          action,
          COUNT(*) AS failed_count
        FROM
          {{table}}
        WHERE
          action = 'firewall_block'
        GROUP BY
          src_addr,
          dst_addr,
          action
      )
      ,baseline_ips AS (
        SELECT
          dst_addr,
          COUNT(*) AS total_seen_count
        FROM
          {{completed30DaysTrafficExceptCurrentIntervalTableName}}
        GROUP BY
          dst_addr
      )
      SELECT
        f.src_addr,
        f.dst_addr,
        f.failed_count,
        action,
        COALESCE(b.total_seen_count, 0) AS baseline_seen,
        CASE
          WHEN b.total_seen_count IS NULL THEN 'NEW'
          WHEN b.total_seen_count < 5 THEN 'RARE'
          ELSE 'COMMON'
        END AS rarity_flag
      FROM
        failed_connections f
      LEFT JOIN
        baseline_ips b
      ON
        f.dst_addr = b.dst_addr
      WHERE
        b.total_seen_count IS NULL  -- totally new
        OR b.total_seen_count < 5   -- or very rare
      ORDER BY
        failed_count DESC
    `,
    severity: 'medium',
    interval: 180
  }
];
