export const exfiltrationC2 = [
  {
    name: 'High DNS Traffic Between internal Hosts',
    category: 'Exfiltration C2',
    subTitle:
      "Highlights \`unusual levels of DNS traffic\` tied to a specific process, which could indicate \`DNS tunneling\` or \`command-and-control (C2) communication'.",
    explanation:
      "Detects \`200 or more DNS sessions\` between a specific \`internal source\`, \`destination\`, and \`destination process\` within a \`10-minute window'.",
    query: `
      SELECT 
        src_addr, 
        src_name, 
        dst_addr, 
        dst_name, 
        dst_process, 
        port,
        SUM(session_count) AS session_count 
      FROM {{table}} 
      WHERE port = 53 
        AND NOT ('Domain Controllers' IN UNNEST(dst_common.activeDirectoryOu)) 
      GROUP BY 
        src_addr, 
        src_name, 
        dst_addr, 
        dst_name, 
        dst_process,
        port
      HAVING session_count > 200
    `,
    severity: 'high',
    interval: 10
  },
  {
    name: 'High DNS Query Volume from Internal Host',
    category: 'Exfiltration C2',
    subTitle:
      'Flags \`excessive DNS activity\` from a single host, which could suggest \`DNS tunneling\`, \`malware beaconing\`, or \`misconfigured systems\`.',
    explanation:
      "Detects \`internal hosts\` that generate \`100 or more DNS queries\` within a \`10-minute window'.",
    query: `
      SELECT 
        src_addr, 
        port,
        SUM(session_count) AS session_count 
      FROM {{table}} 
      WHERE port = 53 
        AND src_type != 'internet' 
      GROUP BY src_addr, port
      HAVING session_count >= 100
    `,
    severity: 'high',
    interval: 10
  },
  {
    name: 'Active SSH Tunneling',
    category: 'Exfiltration C2',
    subTitle:
      "Detects hosts where an \`SSH process\` was followed by outbound connections to non-SSH ports, potentially indicating \`SSH tunneling\` used for \`data exfiltration\` or \`command and control'.",
    explanation:
      "Identifies machines that initiated an \`SSH session\` (via ssh or sshd) and then made subsequent outbound connections to external destinations over non-SSH ports. This behavior may indicate that the SSH session is being used to \`tunnel traffic', potentially bypassing security controls or establishing covert \`C2 channels'.",
    query: `
      ,ssh_targets AS (
        SELECT
          dst_addr,
          src_process,
          action,
          MIN(time) AS first_ssh_time,
          SUM(session_count) AS ssh_sessions
        FROM
          {{table}}
        WHERE
          LOWER(src_process) IN ('ssh', 'sshd')
          AND (action = 'accept' OR action = 'firewall_allow')
        GROUP BY
          dst_addr,
          src_process,
          action
      )
      ,target_outbound AS (
        SELECT
          src_addr,
          dst_addr,
          port,
          action,
          MIN(time) AS first_outbound_time,
          SUM(session_count) AS outbound_sessions
        FROM
          {{table}}
        WHERE
          (action = 'accept' OR action = 'firewall_allow')
          AND port != 22  -- Exclude SSH port in outbound connections
        GROUP BY
          src_addr, dst_addr, port, action
      )
      SELECT
        o.src_addr AS src_addr,
        o.dst_addr AS dst_addr,
        o.port AS port,
        r.first_ssh_time,
        o.first_outbound_time,
        TIMESTAMP_DIFF(o.first_outbound_time, r.first_ssh_time, SECOND) AS seconds_after_ssh,
        r.ssh_sessions,
        o.outbound_sessions
      FROM
        ssh_targets r
      JOIN
        target_outbound o
      ON
        r.dst_addr = o.src_addr
      WHERE
        o.first_outbound_time > r.first_ssh_time
      ORDER BY
        first_outbound_time
    `,
    severity: 'high',
    interval: 10
  }
];
