# Summary
## 01 - external access critical services
## 02 - brute force authentication anomalies
## [03 - suspicious process executions](suspiciousProcessExecutions.ts)
- [Rare Process with Internet Connectivity](#rare-process-with-internet-connectivity)✔️ 
- [Rare Process with Internal Connectivity](#rare-process-with-internal-connectivity)✔️ 
## 04 - Persistence Mechanisms
## 05 - Privilege Escalation Attempts
## 06 - Defense Evasion
## [07 - lateral movements](lateralMovements.ts)
- [Repeated Internal SMB Access Across Hosts](#repeated-internal-smb-access-across-hosts)✔️ 
- [Internal SSH Connections from Unrecognized Processes](#internal-ssh-connections-from-unrecognized-processes)✔️ 
- [Internal RDP Sessions with Unrecognized Processes](#internal-rdp-sessions-with-unrecognized-processes)✔️ 
- [Internal SMB Sessions with Unrecognized Processes](#internal-smb-sessions-with-unrecognized-processes)✔️ 
- [Internal RPC Sessions with Unrecognized Processes](#internal-rpc-sessions-with-unrecognized-processes)✔️
## [08 - exfiltration c2](exfiltrationC2.ts)
- [High DNS Traffic Between internal Hosts](#high-dns-traffic-between-internal-hosts)✔️
- [High DNS Query Volume from Internal Host](#high-dns-query-volume-from-internal-host)✔️

## [09 - dos ddos detection](dosDdosDetection.ts)
- [High Connection Volume to Single Destination](#high-connection-volume-to-single-destination)✔️
- [Non-Administrative Traffic Spike to Destination](#non-administrative-traffic-spike-to-destination)✔️
## [10 - reconnaissance scanning activities](reconnaissanceScanningActivities.ts)
- [Port Scanning Activity](#port-scanning-activity)✔️
- [Host Discovery via IP Scanning (IP Recon)](#host-discovery-via-ip-scanning-ip-recon)✔️
## 11 - Malware Activity Detection
## 12 - Insider Threat Indicators
## 13 - Credential Access Abuse
## 14 - Application Layer Attacks
## [15 - network anomalies](networkAnomalies.ts)
- [SSH Traffic Spike](#ssh-traffic-spike)✔️
- [RDP Traffic Spike](#rdp-traffic-spike)✔️
- [SMB Traffic Spike](#smb-traffic-spike)✔️
- [RPC Traffic Spike](#rpc-traffic-spike)✔️
- [New Ports Contacted by Host](#new-ports-contacted-by-host)✔️
- [New Ports Used by Process](#new-ports-used-by-process)✔️
- [New Non-Administrative Connection Between Hosts](#new-non-administrative-connection-between-hosts)✔️
- [New SSH Connection Between Hosts](#new-ssh-connection-between-hosts)✔️
- [New RDP Connection Between Hosts](#new-rdp-connection-between-hosts)✔️
- [New SMB Connection Between Hosts](#new-smb-connection-between-hosts)✔️
- [New WinRM Connection Between Hosts](#new-winrm-connection-between-hosts)✔️
- [New RPC Connection Between Hosts](#new-rpc-connection-between-hosts)✔️
- [New VNC Connection Between Hosts](#new-vnc-connection-between-hosts)✔️
- [Off-Hours Traffic Spike](#off-hours-traffic-spike)✔️
## 16 - File Integrity Monitoring
## 17 - Suspicious User Behavior

# Detailed
### PARAMS
 - if , in start of query it means with
 - {{table}} = last 10 min:
    ```sql
    with network_traffic_logs as (
    SELECT *
    FROM {{project_id}}.{{org_id}}.{{table_name}}
    where created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
    )
    ```

## 01 - external access critical services

## 02 - brute force authentication anomaliesx

## 03 - suspicious process executions

### Rare Process with Internet Connectivity

- **Feasibility:** ✅ Available  
- **Detection Logic:** Finds /uncommon processes/ running on /internal machines/ that are making /outbound connections to the internet/. A process is considered rare if it makes up /less than 1% of total outbound connections/ for that machine based on historical patterns.  
- **Reason:** Detects /suspicious outbound activity/ from rare processes, which could signal /malware/, /command-and-control (C2)/ communication, or /data exfiltration/.  
- **Severity:** High  
- **MinThreshold:** Detected in the current period, historically seen in less than 1% of outbound traffic  
- **emailNotify:** TRUE  
- **SQL Query:**  
```sql
, baseline_process_counts AS (
  SELECT 
    src_addr,
    src_process,
    SUM(session_count) AS process_count
  FROM {{table}}
  WHERE created_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
    AND src_type != 'internet'
    AND dst_type = 'internet'
  GROUP BY src_addr, src_process
),
total_process_counts AS (
  SELECT 
    src_addr,
    SUM(session_count) AS total_count
  FROM {{table}}
  WHERE created_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
    AND src_type != 'internet'
    AND dst_type = 'internet'
  GROUP BY src_addr
),
process_ratios AS (
  SELECT 
    b.src_addr,
    b.src_process,
    b.process_count,
    t.total_count,
    SAFE_DIVIDE(b.process_count, t.total_count) AS process_ratio
  FROM baseline_process_counts b
  JOIN total_process_counts t
    ON b.src_addr = t.src_addr
),
rare_processes AS (
  SELECT 
    src_addr,
    src_process,
    process_count,
    total_count,
    process_ratio
  FROM process_ratios
  WHERE process_ratio < 0.01  -- Less or equal to 1% of executions
),
current_processes AS (
  SELECT 
    src_addr,
    src_process,
    SUM(session_count) AS recent_executions
  FROM {{table}}
  WHERE src_type != 'internet'
    AND dst_type = 'internet'
  GROUP BY src_addr, src_process
)
SELECT 
  c.src_addr,
  c.src_process,
  c.recent_executions,
  r.process_count,
  r.total_count,
  r.process_ratio
FROM current_processes c
JOIN rare_processes r
  ON c.src_addr = r.src_addr 
 AND c.src_process = r.src_process;
```

---
### Rare Process with Internal Connectivity

- **Feasibility:** ✅ Available  
- **Detection Logic:** Finds /uncommon processes/ running on /internal machines/ that are connecting to /other internal machines/. A process is flagged if it accounts for /less than 1% of internal connections/ on that machine based on historical data.  
- **Reason:** Highlights /unexpected lateral movement/ or /internal reconnaissance/ by rare processes, which could indicate /malicious activity/ moving across the network.  
- **Severity:** Medium  
- **MinThreshold:** Detected in the current period, historically seen in less than 1% of internal traffic  
- **emailNotify:** FALSE  
- **SQL Query:**  
```sql
, baseline_process_counts AS (
  SELECT 
    src_addr,
    src_process,
    SUM(session_count) AS process_count
  FROM {{table}}
  WHERE created_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
    AND src_type != 'internet'
    AND dst_type != 'internet'
  GROUP BY src_addr, src_process
),
total_process_counts AS (
  SELECT 
    src_addr,
    SUM(session_count) AS total_count
  FROM {{table}}
  WHERE created_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
    AND src_type != 'internet'
    AND dst_type != 'internet'
  GROUP BY src_addr
),
process_ratios AS (
  SELECT 
    b.src_addr,
    b.src_process,
    b.process_count,
    t.total_count,
    SAFE_DIVIDE(b.process_count, t.total_count) AS process_ratio
  FROM baseline_process_counts b
  JOIN total_process_counts t
    ON b.src_addr = t.src_addr
),
rare_processes AS (
  SELECT 
    src_addr,
    src_process,
    process_count,
    total_count,
    process_ratio
  FROM process_ratios
  WHERE process_ratio < 0.01  -- Less or equal to 1% of executions
),
current_processes AS (
  SELECT 
    src_addr,
    src_process,
    SUM(session_count) AS recent_executions
  FROM {{table}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
  GROUP BY src_addr, src_process
)
SELECT 
  c.src_addr,
  c.src_process,
  c.recent_executions,
  r.process_count,
  r.total_count,
  r.process_ratio
FROM current_processes c
JOIN rare_processes r
  ON c.src_addr = r.src_addr 
 AND c.src_process = r.src_process;
```

## 04 - persistence mechanisms

## 05 - privilege escalation attempts

## 06 - defense evasion

## 07 - lateral movement

### Repeated Internal SMB Access Across Hosts

- **Feasibility:** ✅ Available  
- **Detection Logic:** Finds /internal machines/ that are connecting to /five or more unique internal hosts/ using /SMB or related management ports (445, 139, 137, 138, 135, 593)/ within a /10-minute window/.  
- **Reason:** This pattern may indicate /lateral movement/, /network reconnaissance/, or /host scanning/ over /SMB or RPC protocols/.  
- **Severity:** Medium  
- **MinThreshold:** 5 or more unique destinations within 10 minutes  
- **emailNotify:** FALSE  
- **Exclusions:** Ignores /known-safe processes/ from /Domain Controllers/ (like /replication/ or /management tasks/) to reduce false alerts.  

- **SQL Query:**
```sql
SELECT src_addr,src_name, COUNT(DISTINCT dst_addr) AS unique_dst_hosts, src_process, port, SUM(session_count) AS total_sessions
FROM {{table}}
WHERE port in (445, 139, 137, 138, 135, 593)
    AND src_type != 'internet'
    AND dst_type != 'internet'
    AND src_process != ''
AND NOT (
  -- Traffic FROM Domain Controllers with known-safe processes
  'Domain Controllers' IN UNNEST(src_common.activeDirectoryOu)
  AND src_process IN (
    'ntfrs.exe', 'dfsrs.exe', 'lsass.exe', 'rpcss', 'dcomlaunch', 
    'ntoskrnl.exe', 'Microsoft.Tri.Sensor.exe', 'svchost.exe', 'OktaAgentService.exe'
  )
)
GROUP BY src_addr,src_name, src_process, port
HAVING COUNT(DISTINCT dst_addr) >= 5
```
### Internal SSH Connections from Unrecognized Processes

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /SSH sessions within the internal network/ where the /source process/ is /not part of the approved SSH tools list/ (like `ssh`, `putty.exe`, etc.).  
- **Reason:** Flags /unexpected SSH client usage/ on internal machines, which could point to /unauthorized lateral movement/ or the use of /custom tools for SSH access/.  
- **Severity:** Medium  
- **MinThreshold:** 1 event  
- **emailNotify:** FALSE  
- **SQL Query:**

```sql
SELECT src_addr, dst_addr, port, src_process, dst_process, SUM(session_count) AS session_count
FROM {{table}}
WHERE port = 22
  AND src_type != 'internet'
  AND dst_type != 'internet'
  AND src_process NOT IN ('ssh', 'sshd', 'putty.exe', 'plink.exe', 'securecrt.exe', 'mobaxterm.exe', 'remmina', 'gnome-terminal', 'konsole', 'powershell.exe', 'ssh.exe')
GROUP BY src_addr, dst_addr, port, src_process, dst_process
```
### Internal RDP Sessions with Unrecognized Processes

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /RDP sessions within the internal network/ (`dst_port` 3389 or 3388) where the /destination process/ is /not part of the standard system processes list/ (like `svchost.exe`, `lsass.exe`, etc.).  
- **Reason:** Highlights /suspicious or unauthorized RDP usage/, which could suggest /lateral movement/ or /privilege escalation attempts/.  
- **Severity:** High  
- **MinThreshold:** 1 event  
- **emailNotify:** TRUE  
 
- **SQL Query:**  
```sql
SELECT src_addr, dst_addr, port, src_process, dst_process, SUM(session_count) AS session_count
FROM {{table}}
WHERE src_type != 'internet'
  AND dst_type != 'internet'
  AND port IN (3389, 3388)
  AND dst_process IS NOT NULL
  AND dst_process != ''
  AND dst_process NOT IN ('svchost.exe', 'lsass.exe', 'csrss.exe', 'winlogon.exe')
GROUP BY src_addr, dst_addr, port, src_process, dst_process
```

---
### Internal SMB Sessions with Unrecognized Processes

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /SMB connections within the internal network/ (`dst_port` 445, 139, 137, 138) where the /destination process/ is /not part of the standard SMB system processes list/ (such as `svchost.exe`, `smbd`, etc.).  
- **Reason:** Flags /unexpected SMB sessions/, which could indicate /lateral movement/, /file staging/, or /unauthorized file-sharing activity/.  
- **Severity:** Medium  
- **MinThreshold:** 1 event  
- **emailNotify:** FALSE  

- **SQL Query:**  
```sql
SELECT src_addr, dst_addr, port, src_process, dst_process, SUM(session_count) AS session_count
FROM {{table}}
WHERE src_type != 'internet'
  AND dst_type != 'internet'
  AND port IN (445, 139, 137, 138)
  AND dst_process IS NOT NULL
  AND dst_process != ''
  AND dst_process NOT IN ('svchost.exe', 'smbd', 'ntoskrnl.exe', 'system', 'CynetMS.exe')
GROUP BY src_addr, dst_addr, port, src_process, dst_process
```
---
### Internal RPC Sessions with Unrecognized Processes

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /RPC connections within the internal network/ (`dst_port` 135 or 593) where the /destination process/ is /not part of the standard RPC system processes list/ (such as `svchost.exe`, `lsass.exe`, etc.).  
- **Reason:** Highlights /suspicious RPC usage/, which could point to /domain controller enumeration/, /DCOM abuse/, or /lateral movement/ within the network.  
- **Severity:** Medium  
- **MinThreshold:** 1 event  
- **emailNotify:** FALSE  

- **SQL Query:**  
```sql
SELECT src_addr, dst_addr, port, src_process, dst_process, SUM(session_count) AS session_count
FROM {{table}}
WHERE src_type != 'internet'
  AND dst_type != 'internet'
  AND port IN (135, 593)
  AND dst_process IS NOT NULL
  AND dst_process != ''
  AND dst_process NOT IN (  'svchost.exe', 'lsass.exe', 'rpcss', 'dcomlaunch', 'CynetEPS.exe')
GROUP BY src_addr, dst_addr, port, src_process, dst_process
```

## 08_exfiltration_c2

### High DNS Traffic Between internal Hosts

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /200 or more DNS sessions/ between a specific /internal source/, /destination/, and /destination process/ within a /10-minute window/.  
- **Reason:** Highlights /unusual levels of DNS traffic/ tied to a specific process, which could indicate /DNS tunneling/ or /command-and-control (C2) communication/.  
- **Severity:** High  
- **MinThreshold:** 200 or more sessions between `(src_addr, dst_addr, dst_process)` in 10 minutes  
- **emailNotify:** TRUE  
- **SQL Query:**
```sql
SELECT src_addr,src_name, dst_addr, dst_name, dst_process, SUM(session_count) AS session_count
FROM {{table}}
WHERE port = 53
  AND NOT (
    'Domain Controllers' IN UNNEST(dst_common.activeDirectoryOu)
  )
GROUP BY src_addr, src_name, dst_addr, dst_name, dst_process
HAVING session_count > 200;
```
### High DNS Query Volume from Internal Host

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /internal hosts/ that generate /100 or more DNS queries/ within a /10-minute window/.  
- **Reason:** Flags /excessive DNS activity/ from a single host, which could suggest /DNS tunneling/, /malware beaconing/, or /misconfigured systems/.  
- **Severity:** High  
- **MinThreshold:** 100 or more queries from a host in 10 minutes  
- **emailNotify:** TRUE  

```sql
SELECT src_addr, SUM(session_count) AS session_count
FROM {{table}}
WHERE port = 53
  AND src_type != 'internet'
GROUP BY src_addr
HAVING session_count >= 100;
```

## 09_dos_ddos_detection

### High Connection Volume to Single Destination

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /1000 or more connections/ made to a /single destination IP/ within a /1-minute window/.  
- **Reason:** Flags /potential denial-of-service (DoS) attacks/ or /service flooding/, where a target is overwhelmed with excessive traffic.  
- **Severity:** High  
- **MinThreshold:** 1000 or more connections in 1 minute  
- **emailNotify:** TRUE  
- **SQL Query:**

```sql
SELECT dst_addr, SUM(session_count) AS session_count
FROM {{table}} tl
GROUP BY dst_addr
HAVING session_count >= 1000;
```
### Non-Administrative Traffic Spike to Destination

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects a /300% or greater increase in traffic/ to a /destination host/ over /non-administrative ports/ (excluding SSH, RDP, SMB, etc.), compared to its /historical baseline over a 10-minute window/.  
- **Reason:** Flags /unusual traffic surges/ that could signal /DoS/DDoS attacks/, /service abuse/, or /unexpected spikes in service usage/.  
- **Severity:** High  
- **MinThreshold:** 3 times the baseline traffic within 10 minutes  
- **emailNotify:** TRUE  


```sql
, network_traffic_logs AS (
  SELECT *
  FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE port NOT IN (22, 23, 135, 137, 138, 139, 445, 593, 3306, 3388, 3389, 5432, 1433, 1434, 5985, 5986, 5900, 389, 636)
),
baseline_window AS (
  SELECT 
    src_addr, 
    port, 
    TIMESTAMP_TRUNC(created_at, MINUTE) AS minute_bucket, 
    SUM(session_count) AS total_sessions
  FROM network_traffic_logs tl
  WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
    AND created_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr, port, minute_bucket
),
baseline_traffic AS (
  SELECT 
    bw.src_addr, 
    bw.port, 
    AVG(bw.total_sessions) AS avg_sessions_per_minute
  FROM baseline_window bw
  GROUP BY bw.src_addr, bw.port
),
current_traffic AS (
  SELECT 
    src_addr, 
    port, 
    SUM(session_count) AS recent_sessions
  FROM {{table}}
  GROUP BY src_addr, port
)
SELECT 
  c.src_addr, 
  c.port,
  c.recent_sessions, 
  b.avg_sessions_per_minute * 10 AS baseline_sessions_per_10_min,
  SAFE_DIVIDE(c.recent_sessions, b.avg_sessions_per_minute * 10) AS spike_ratio,
FROM current_traffic c
JOIN baseline_traffic b 
  ON c.src_addr = b.src_addr 
 AND c.port = b.port
WHERE c.recent_sessions > b.avg_sessions_per_minute * 10 * 3
  AND b.avg_sessions_per_minute > 0.1  -- Optional: avoid low baseline noise
```

## 10_reconnaissance_scanning_activities

### Port Scanning Activity

- **Feasibility:** ✅ Available  
- **Detection Logic:** Identifies /internal hosts/ scanning /20 or more unique destination ports/ on /one or more targets/ within a /10-minute window/.  
- **Reason:** Flags /port scanning behavior/, often used as /reconnaissance/ to discover /open services/ on target machines.  
- **Severity:** Medium  
- **MinThreshold:** 20 or more unique ports scanned in 10 minutes  
- **emailNotify:** FALSE  
- **SQL Query:**

```sql
SELECT src_addr, dst_addr, src_process, COUNT(DISTINCT port) AS unique_ports, SUM(session_count) AS session_count
FROM {{table}}
GROUP BY src_addr, dst_addr, src_process
HAVING COUNT(DISTINCT port) >= 20
ORDER BY unique_ports DESC
```
### Host Discovery via IP Scanning (IP Recon)

- **Feasibility:** ✅ Available  
- **Detection Logic:** Identifies /internal hosts/ probing /20 or more unique destination IPs/ on the /same port/ within a /10-minute window/.  
- **Reason:** Flags /horizontal scanning behavior/, typically used for /host discovery/ during /network reconnaissance/.  
- **Severity:** Medium  
- **MinThreshold:** 20 or more unique destination IPs probed in 10 minutes  
- **emailNotify:** FALSE  
- **SQL Query:**  
```sql
SELECT src_addr, src_name, COUNT(DISTINCT dst_addr) AS unique_dst_addr, src_process, SUM(session_count) AS session_count, port
FROM {{table}}
WHERE port NOT IN (135, 7680)  -- Exclude common ports
  AND (src_process IS NULL OR src_process NOT IN ('svchost.exe', 'Microsoft.Tri.Sensor.exe'))  -- Exclude noisy processes
GROUP BY src_addr, src_name, src_process, port
HAVING COUNT(DISTINCT dst_addr) >= 20
```

## 11_malware_activity_detection

## 12_insider_threat_indicators

## 13_credential_access_abuse

## 14_application_layer_attacks

## 15_network_anomalies
### SSH Traffic Spike

- **Feasibility:** ✅ Available  
- **Detection Logic:** Flags /internal hosts/ that generate /three times or more SSH traffic/ (port 22) compared to their /historical average/ within a /10-minute window/.  
- **Reason:** Detects /spikes in SSH activity/, which could suggest /unauthorized access/, /lateral movement/, or /brute-force attempts/ across internal systems.  
- **Severity:** High  
- **MinThreshold:** 3 times the baseline SSH traffic in 10 minutes  
- **emailNotify:** TRUE  
- **SQL Query:**  
```sql
, network_traffic_logs AS (
  SELECT *
  FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE port = 22
),
baseline_window AS (
  SELECT 
    src_addr, 
    TIMESTAMP_TRUNC(created_at, MINUTE) AS minute_bucket, 
    SUM(session_count) AS total_sessions
  FROM network_traffic_logs
  WHERE created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr, minute_bucket
),
baseline_traffic AS (
  SELECT 
    src_addr, 
    AVG(total_sessions) AS avg_sessions_per_minute
  FROM baseline_window
  GROUP BY src_addr
),
current_traffic AS (
  SELECT 
    src_addr, 
    SUM(session_count) AS recent_sessions
  FROM network_traffic_logs
  WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr
)
SELECT 
  c.src_addr, 
  c.recent_sessions, 
  b.avg_sessions_per_minute * 10 AS baseline_sessions_per_10_min,
  SAFE_DIVIDE(c.recent_sessions, b.avg_sessions_per_minute * 10 ) AS spike_ratio
FROM current_traffic c
JOIN baseline_traffic b 
  ON c.src_addr = b.src_addr
WHERE c.recent_sessions > b.avg_sessions_per_minute * 10 * 3
  AND b.avg_sessions_per_minute > 0.1;
```
### RDP Traffic Spike

- **Feasibility:** ✅ Available  
- **Detection Logic:** Flags /internal hosts/ generating /three times or more RDP traffic/ (ports 3389, 3388) compared to their /historical average/ within a /10-minute window/.  
- **Reason:** Detects /spikes in RDP sessions/, which could indicate /unauthorized remote desktop activity/, /lateral movement/, or /privilege escalation attempts/.  
- **Severity:** High  
- **MinThreshold:** 3 times the baseline RDP traffic in 10 minutes  
- **emailNotify:** TRUE  
- **SQL Query:**  
```sql
, network_traffic_logs AS (
  SELECT *
  FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE port IN (3389, 3388)
),
baseline_window AS (
  SELECT 
    src_addr, 
    TIMESTAMP_TRUNC(created_at, MINUTE) AS minute_bucket, 
    SUM(session_count) AS total_sessions
  FROM network_traffic_logs
  WHERE created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr, minute_bucket
),
baseline_traffic AS (
  SELECT 
    src_addr, 
    AVG(total_sessions) AS avg_sessions_per_minute
  FROM baseline_window
  GROUP BY src_addr
),
current_traffic AS (
  SELECT 
    src_addr, 
    SUM(session_count) AS recent_sessions
  FROM network_traffic_logs
  WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr
)
SELECT 
  c.src_addr, 
  c.recent_sessions, 
  b.avg_sessions_per_minute * 10 AS baseline_sessions_per_10_min,
  SAFE_DIVIDE(c.recent_sessions, b.avg_sessions_per_minute * 10 ) AS spike_ratio
FROM current_traffic c
JOIN baseline_traffic b 
  ON c.src_addr = b.src_addr
WHERE c.recent_sessions > b.avg_sessions_per_minute * 10 * 3
  AND b.avg_sessions_per_minute > 0.1;
```
### SMB Traffic Spike

- **Feasibility:** ✅ Available  
- **Detection Logic:** Flags /internal hosts/ generating /three times or more SMB traffic/ (ports 445, 139, 137, 138) compared to their /historical average/ within a /10-minute window/.  
- **Reason:** Detects /spikes in SMB activity/, which could suggest /lateral movement/, /data staging/, or /host enumeration/ over SMB protocols.  
- **Severity:** High  
- **MinThreshold:** 3 times the baseline SMB traffic in 10 minutes  
- **emailNotify:** TRUE    
- **SQL Query:**  
```sql
, network_traffic_logs AS (
  SELECT *
  FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE port IN (445, 139, 137, 138)
),
baseline_window AS (
  SELECT 
    src_addr, 
    TIMESTAMP_TRUNC(created_at, MINUTE) AS minute_bucket, 
    SUM(session_count) AS total_sessions
  FROM network_traffic_logs
  WHERE created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr, minute_bucket
),
baseline_traffic AS (
  SELECT 
    src_addr, 
    AVG(total_sessions) AS avg_sessions_per_minute
  FROM baseline_window
  GROUP BY src_addr
),
current_traffic AS (
  SELECT 
    src_addr, 
    SUM(session_count) AS recent_sessions
  FROM network_traffic_logs
  WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr
)
SELECT 
  c.src_addr, 
  c.recent_sessions, 
  b.avg_sessions_per_minute * 10 AS baseline_sessions_per_10_min,
  SAFE_DIVIDE(c.recent_sessions, b.avg_sessions_per_minute * 10 ) AS spike_ratio
FROM current_traffic c
JOIN baseline_traffic b 
  ON c.src_addr = b.src_addr
WHERE c.recent_sessions > b.avg_sessions_per_minute * 10 * 3
  AND b.avg_sessions_per_minute > 0.1;
```
### RPC Traffic Spike

- **Feasibility:** ✅ Available  
- **Detection Logic:** Flags /internal hosts/ generating /three times or more RPC traffic/ (ports 135, 593) compared to their /historical average/ within a /10-minute window/.  
- **Reason:** Detects /spikes in RPC activity/, which could indicate /domain controller enumeration/, /DCOM abuse/, or /lateral movement/ within the network.  
- **Severity:** High  
- **MinThreshold:** 3 times the baseline RPC traffic in 10 minutes  
- **emailNotify:** TRUE  
- **SQL Query:**  
```sql
, network_traffic_logs AS (
  SELECT *
  FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE port IN (135, 593)
),
baseline_window AS (
  SELECT 
    src_addr, 
    TIMESTAMP_TRUNC(created_at, MINUTE) AS minute_bucket, 
    SUM(session_count) AS total_sessions
  FROM network_traffic_logs
  WHERE created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr, minute_bucket
),
baseline_traffic AS (
  SELECT 
    src_addr, 
    AVG(total_sessions) AS avg_sessions_per_minute
  FROM baseline_window
  GROUP BY src_addr
),
current_traffic AS (
  SELECT 
    src_addr, 
    SUM(session_count) AS recent_sessions
  FROM network_traffic_logs
  WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr
)
SELECT 
  c.src_addr, 
  c.recent_sessions, 
  b.avg_sessions_per_minute * 10 AS baseline_sessions_per_10_min,
  SAFE_DIVIDE(c.recent_sessions, b.avg_sessions_per_minute * 10 ) AS spike_ratio
FROM current_traffic c
JOIN baseline_traffic b 
  ON c.src_addr = b.src_addr
WHERE c.recent_sessions > b.avg_sessions_per_minute * 10 * 3
  AND b.avg_sessions_per_minute > 0.1;
```
### New Ports Contacted by Host

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /internal hosts/ initiating connections to /ports not previously seen/ in their /historical baseline/.  
- **Reason:** Flags /new or unexpected service access/ by a host, which could suggest /lateral movement/, /tunneling/, or /malicious behavior/.  
- **Severity:** Medium  
- **MinThreshold:** 1 new port contacted  
- **emailNotify:** FALSE  
- **SQL Query:**  
```sql
, baseline_ports AS (
  SELECT 
    src_addr, 
    ARRAY_AGG(DISTINCT port) AS known_ports
  FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
  GROUP BY src_addr
),
current_ports AS (
  SELECT 
    src_addr, 
    port
  FROM {{table}}
)
SELECT 
  cp.src_addr, 
  ARRAY_AGG(DISTINCT cp.port) AS new_ports
FROM current_ports cp
LEFT JOIN baseline_ports bp
  ON cp.src_addr = bp.src_addr
WHERE bp.known_ports IS NOT NULL
  AND NOT cp.port IN UNNEST(bp.known_ports)
GROUP BY cp.src_addr;

```

---
### New Ports Used by Process

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /processes/ initiating connections on /ports not observed in their historical baseline/.  
- **Reason:** Flags /unexpected port usage by a process/, which could indicate /pivoting/, /data exfiltration/, or /tunneling activity/.  
- **Severity:** Medium  
- **MinThreshold:** 1 new port used  
- **emailNotify:** FALSE  
- **SQL Query:**  
```sql
, baseline_ports AS (
  SELECT 
    src_process, 
    ARRAY_AGG(DISTINCT port) AS known_ports
  FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
  and src_process != "" and src_process IS NOT NULL
  GROUP BY src_process
),
current_ports AS (
  SELECT 
    src_process, 
    port
  FROM {{table}}
  WHERE src_process != "" and src_process IS NOT NULL

)
SELECT 
  cp.src_process, 
  cp.port
FROM current_ports cp
LEFT JOIN baseline_ports bp
  ON cp.src_process = bp.src_process
WHERE NOT cp.port IN UNNEST(bp.known_ports);
```  
### New Non-Administrative Connection Between Hosts

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /first-time communication/ between two /internal hosts/ over /non-administrative ports/ (excluding SSH, RDP, SMB, etc.).  
- **Reason:** Identifies /new relationships/ forming in the network, which could suggest /lateral movement/, /reconnaissance/, or /unauthorized activity/.  
- **Severity:** Medium  
- **MinThreshold:** 1 new connection  
- **emailNotify:** FALSE  
- **SQL Query:**  
```sql
SELECT src_addr, dst_addr, FORMAT_TIMESTAMP('%FT%TZ', MIN(created_at)) AS first_seen_time
FROM {{project_id}}.{{org_id}}.{{table_name}}
WHERE  src_type != 'internet' AND dst_type != 'internet' -- optional: limit to internal hosts
AND port NOT IN (22, 23, 135, 137, 138, 139, 445, 593, 3306, 3388, 3389, 5432, 1433, 1434, 5985, 5986, 5900, 389, 636)
GROUP BY src_addr, dst_addr
HAVING MIN(created_at) >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE);
```
### New SSH Connection Between Hosts

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /first-time SSH communication/ between two /internal hosts/ (`src_addr` → `dst_addr` on port 22).  
- **Reason:** Flags /new SSH relationships/, which could indicate /lateral movement/ or /unauthorized access/.  
- **Severity:** High  
- **MinThreshold:** 1 new connection  
- **emailNotify:** TRUE  
- **SQL Query:**  
```sql
, total_internal_hosts AS (
  SELECT 
    COUNT(DISTINCT dst_addr) AS total_hosts
FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port = 22
    AND created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
),
historical_connections AS (
  SELECT 
    src_addr,
    COUNT(DISTINCT dst_addr) AS connected_hosts
FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port = 22
    AND created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr
),
first_time_ssh AS (
  SELECT 
    src_addr,
    dst_addr,
    MIN(created_at) AS first_seen_time
  FROM {{table}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port = 22
  GROUP BY src_addr, dst_addr
)
SELECT 
  f.src_addr,
  f.dst_addr,
  FORMAT_TIMESTAMP('%FT%TZ', f.first_seen_time) AS first_seen_time
FROM first_time_ssh f
LEFT JOIN historical_connections h
  ON f.src_addr = h.src_addr
CROSS JOIN total_internal_hosts t
WHERE SAFE_DIVIDE(h.connected_hosts, t.total_hosts) < 0.4
```

---
### New RDP Connection Between Hosts

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /first-time RDP communication/ between two /internal hosts/ (`src_addr` → `dst_addr` on ports 3389, 3388).  
- **Reason:** Flags /new RDP sessions/, which could indicate /lateral movement/ or /unauthorized remote desktop access/.  
- **Severity:** High  
- **MinThreshold:** 1 new connection  
- **emailNotify:** TRUE  
- **SQL Query:**  
```sql
, total_internal_hosts AS (
  SELECT 
    COUNT(DISTINCT dst_addr) AS total_hosts
FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port IN (3389, 3388)
    AND created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
),
historical_connections AS (
  SELECT 
    src_addr,
    COUNT(DISTINCT dst_addr) AS connected_hosts
FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port IN (3389, 3388)
    AND created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr
),
first_time_ssh AS (
  SELECT 
    src_addr,
    dst_addr,
    MIN(created_at) AS first_seen_time
  FROM {{table}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port IN (3389, 3388)
  GROUP BY src_addr, dst_addr
)
SELECT 
  f.src_addr,
  f.dst_addr,
  FORMAT_TIMESTAMP('%FT%TZ', f.first_seen_time) AS first_seen_time
FROM first_time_ssh f
LEFT JOIN historical_connections h
  ON f.src_addr = h.src_addr
CROSS JOIN total_internal_hosts t
WHERE SAFE_DIVIDE(h.connected_hosts, t.total_hosts) < 0.5
```

---
### New SMB Connection Between Hosts

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /first-time SMB communication/ between two /internal hosts/ (`src_addr` → `dst_addr` on ports 445, 139, 137, 138).  
- **Reason:** Flags /new file-sharing or management sessions/, which could suggest /lateral movement/, /data staging/, or /unauthorized access/.  
- **Severity:** High  
- **MinThreshold:** 1 new connection  
- **emailNotify:** TRUE  
- **SQL Query:**  
```sql
, total_internal_hosts AS (
  SELECT 
    COUNT(DISTINCT dst_addr) AS total_hosts
FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port IN (445, 139, 137, 138)
    AND created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
),
historical_connections AS (
  SELECT 
    src_addr,
    COUNT(DISTINCT dst_addr) AS connected_hosts
FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port IN (445, 139, 137, 138)
    AND created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr
),
first_time_ssh AS (
  SELECT 
    src_addr,
    dst_addr,
    MIN(created_at) AS first_seen_time
  FROM {{table}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port IN (445, 139, 137, 138)
  GROUP BY src_addr, dst_addr
)
SELECT 
  f.src_addr,
  f.dst_addr,
  FORMAT_TIMESTAMP('%FT%TZ', f.first_seen_time) AS first_seen_time
FROM first_time_ssh f
LEFT JOIN historical_connections h
  ON f.src_addr = h.src_addr
CROSS JOIN total_internal_hosts t
WHERE SAFE_DIVIDE(h.connected_hosts, t.total_hosts) < 0.3
```

---
### New WinRM Connection Between Hosts

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /first-time WinRM communication/ between two /internal hosts/ (`src_addr` → `dst_addr` on ports 5985, 5986).  
- **Reason:** Flags /new remote management sessions/, which could indicate /lateral movement/, /privilege abuse/, or /unauthorized system control/.  
- **Severity:** High  
- **MinThreshold:** 1 new connection  
- **emailNotify:** TRUE  
- **SQL Query:**  
```sql
, total_internal_hosts AS (
  SELECT 
    COUNT(DISTINCT dst_addr) AS total_hosts
FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port IN (5985, 5986)
    AND created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
),
historical_connections AS (
  SELECT 
    src_addr,
    COUNT(DISTINCT dst_addr) AS connected_hosts
FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port IN (5985, 5986)
    AND created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr
),
first_time_ssh AS (
  SELECT 
    src_addr,
    dst_addr,
    MIN(created_at) AS first_seen_time
  FROM {{table}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port IN (5985, 5986)
  GROUP BY src_addr, dst_addr
)
SELECT 
  f.src_addr,
  f.dst_addr,
  FORMAT_TIMESTAMP('%FT%TZ', f.first_seen_time) AS first_seen_time
FROM first_time_ssh f
LEFT JOIN historical_connections h
  ON f.src_addr = h.src_addr
CROSS JOIN total_internal_hosts t
WHERE SAFE_DIVIDE(h.connected_hosts, t.total_hosts) < 0.3
```

---
### New RPC Connection Between Hosts

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /first-time RPC communication/ between two /internal hosts/ (`src_addr` → `dst_addr` on ports 135, 593).  
- **Reason:** Flags /new remote procedure call (RPC) sessions/, which could indicate /lateral movement/, /privilege escalation/, or /DCOM abuse/.  
- **Severity:** High  
- **MinThreshold:** 1 new connection  
- **emailNotify:** TRUE  
- **SQL Query:**  
```sql
, total_internal_hosts AS (
  SELECT 
    COUNT(DISTINCT dst_addr) AS total_hosts
FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port IN (135, 593)
    AND created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
),
historical_connections AS (
  SELECT 
    src_addr,
    COUNT(DISTINCT dst_addr) AS connected_hosts
FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port IN (135, 593)
    AND created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr
),
first_time_ssh AS (
  SELECT 
    src_addr,
    dst_addr,
    MIN(created_at) AS first_seen_time
  FROM {{table}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port IN (135, 593)
  GROUP BY src_addr, dst_addr
)
SELECT 
  f.src_addr,
  f.dst_addr,
  FORMAT_TIMESTAMP('%FT%TZ', f.first_seen_time) AS first_seen_time
FROM first_time_ssh f
LEFT JOIN historical_connections h
  ON f.src_addr = h.src_addr
CROSS JOIN total_internal_hosts t
WHERE SAFE_DIVIDE(h.connected_hosts, t.total_hosts) < 0.25
```

---
### New VNC Connection Between Hosts

- **Feasibility:** ✅ Available  
- **Detection Logic:** Detects /first-time VNC communication/ between two /internal hosts/ (`src_addr` → `dst_addr` on port 5900).  
- **Reason:** Flags /new remote desktop (VNC) sessions/, which could suggest /unauthorized access/ or /lateral movement/.  
- **Severity:** High  
- **MinThreshold:** 1 new connection  
- **emailNotify:** TRUE  
- **SQL Query:**  
```sql
, total_internal_hosts AS (
  SELECT 
    COUNT(DISTINCT dst_addr) AS total_hosts
FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port = 5900
    AND created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
),
historical_connections AS (
  SELECT 
    src_addr,
    COUNT(DISTINCT dst_addr) AS connected_hosts
FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port = 5900
    AND created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY src_addr
),
first_time_ssh AS (
  SELECT 
    src_addr,
    dst_addr,
    MIN(created_at) AS first_seen_time
  FROM {{table}}
  WHERE src_type != 'internet'
    AND dst_type != 'internet'
    AND port = 5900
  GROUP BY src_addr, dst_addr
)
SELECT 
  f.src_addr,
  f.dst_addr,
  FORMAT_TIMESTAMP('%FT%TZ', f.first_seen_time) AS first_seen_time
FROM first_time_ssh f
LEFT JOIN historical_connections h
  ON f.src_addr = h.src_addr
CROSS JOIN total_internal_hosts t
WHERE SAFE_DIVIDE(h.connected_hosts, t.total_hosts) < 0.15
```

---  
### Off-Hours Traffic Spike

- **Feasibility:** ✅ Available  
- **Detection Logic:** Compares /current off-hours traffic/ to the /historical off-hours baseline/ for each service, flagging /spikes of three times or more/ within a /10-minute window/.  
- **Reason:** Detects /unusual service activity during off-hours/, which could indicate /unauthorized access/, /data exfiltration/, or /malicious behavior/ when systems are expected to be quieter.  
- **Severity:** Medium  
- **MinThreshold:** 3 times the baseline off-hours traffic in 10 minutes  
- **emailNotify:** TRUE  
- **SQL Query:**  
```sql
, network_traffic_logs_filtered AS (
  SELECT *
  FROM {{project_id}}.{{org_id}}.{{table_name}}
  WHERE EXTRACT(HOUR FROM created_at) NOT BETWEEN 8 AND 19  -- Off-hours only
),
baseline_window AS (
  SELECT 
    dst_addr,
    port,
    TIMESTAMP_TRUNC(created_at, MINUTE) AS minute_bucket,
    SUM(session_count) AS session_count
  FROM network_traffic_logs_filtered
  WHERE created_at BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY dst_addr, port, minute_bucket
),
baseline_traffic AS (
  SELECT 
    dst_addr,
    port,
    AVG(session_count) AS avg_sessions_per_minute
  FROM baseline_window
  GROUP BY dst_addr, port
),
current_traffic AS (
  SELECT 
    dst_addr,
    port,
    SUM(session_count) AS recent_sessions
  FROM network_traffic_logs_filtered
  WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)
  GROUP BY dst_addr, port
)
SELECT 
  c.dst_addr,
  c.port,
  c.recent_sessions,
  b.avg_sessions_per_minute * 10 AS baseline_sessions_per_10_min,
  SAFE_DIVIDE(c.recent_sessions, b.avg_sessions_per_minute * 10) AS spike_ratio
FROM current_traffic c
JOIN baseline_traffic b
  ON c.dst_addr = b.dst_addr 
 AND c.port = b.port
WHERE c.recent_sessions > b.avg_sessions_per_minute * 10 * 3  -- Spike threshold (3x)
AND b.avg_sessions_per_minute > 0.1
```

## 16_file_integrity_monitoring

## 17_suspicious_user_behavior


