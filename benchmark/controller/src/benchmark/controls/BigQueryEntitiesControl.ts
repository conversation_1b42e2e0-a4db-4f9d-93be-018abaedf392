import { ControlResultData } from './types';
import { Benchmark } from '../types/benchmarkTypes';
import BigQuerySQLControl from './BigQuerySQLControl';
import {
  EndpointEntity,
  IntegrationTypes,
  BaseEntityTypes
} from '@globalTypes/ndrBenchmarkTypes';
import { ControlDataType } from '../controlDependencies';

class BigQueryEntitiesControl extends BigQuerySQLControl {
  constructor(filePath: string) {
    super(filePath);
  }

  public async execute(benchmark: Benchmark): Promise<ControlResultData[]> {
    const result = await super.execute(benchmark);
    // Function to extract the correct info based on integration type
    const getEntityWithCorrectInfo = (entity: EndpointEntity) => {
      if (entity.type === BaseEntityTypes.UNRECOGNIZED) {
        return entity;
      }
      const infoKeys = Object.values(IntegrationTypes);
      const nestedInfoKey = infoKeys.find((key) => entity.info[key]);
      const info = nestedInfoKey ? entity.info[nestedInfoKey] : entity.info;
      return { ...entity, info };
    };

    // Fix the info field to be a JSON object
    return result.map((row) => {
      const entity = getEntityWithCorrectInfo(row.entity);
      return { entity };
    });
  }

  public getDependencies(): ControlDataType[] {
    return []; // Entities control has no dependencies
  }

  public getProducedDataTypes(): ControlDataType[] {
    return [ControlDataType.ENTITIES];
  }
}

export default BigQueryEntitiesControl;
