import log from '../../logger';
import * as firestoreOperations from '../../firestore/getNotifications';
import * as statisticsUtils from '../../services/notifications';

import BaseControl from './BaseControl';
import { Benchmark } from '../types/benchmarkTypes';
import { ControlResultData } from './types';
import { ControlDataType } from '../controlDependencies';

class NotificationStatisticsControl extends BaseControl {
  private organizationId: string;

  constructor(organizationId: string) {
    super('NotificationStatisticsControl');
    this.organizationId = organizationId;
  }

  public async execute(benchmark: Benchmark): Promise<ControlResultData[]> {
    log.info('Starting notifications statistics computation...');

    const fireStoreNotifications = await firestoreOperations.getNotifications(
      this.organizationId
    );

    const combinedNotifications = firestoreOperations.combineNotifications({
      benchmarkNotifications: benchmark.notifications,
      fireStoreNotifications
    });

    log.info('Notification array lengths', {
      firestore: fireStoreNotifications.length,
      benchmark: benchmark.notifications.length,
      combined: combinedNotifications.length
    });

    try {
      const statisticsToStore = statisticsUtils.getStatistics(
        combinedNotifications
      );

      log.info(`Successfully computed notifications statistics.`);

      return statisticsToStore;
    } catch (err) {
      log.error(
        `Error computing notifications statistics: ${(err as Error).message}`
      );

      throw err;
    }
  }

  public getDependencies(): ControlDataType[] {
    return [ControlDataType.NOTIFICATIONS]; // Depends on notifications
  }

  public getProducedDataTypes(): ControlDataType[] {
    return [ControlDataType.STATS]; // Produces notification statistics
  }
}

export default NotificationStatisticsControl;
