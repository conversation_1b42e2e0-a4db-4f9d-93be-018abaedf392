import { ControlResultData } from './types';
import { Benchmark } from '../types/benchmarkTypes';
import { Label, LabelAssignment } from '@globalTypes/ndrBenchmarkTypes';
import BigQuerySQLControl from './BigQuerySQLControl';
import { getBigQueryClient, BigQueryClient } from '@big-query-client/bigquery';
import { BIG_QUERY_TABLE_SCHEMAS } from '@big-query-client/definitions/databaseSchemas';
import { BIG_QUERY_TABLES } from '@big-query-client/definitions/constants';
import {
  DatabaseSchemas,
  TableName,
  TableSchema
} from '@big-query-client/bigquery/types';
import * as config from '../../config';

export interface RawLabelRecord {
  id: string;
  type: string;
  key: string;
  description?: string;
  values: string[];
  action: string | null;
  icon?: string;
  color?: string;
}

export interface RawLabelAssignmentRecord {
  entityId: string;
  labelIds: string[];
  created_at: Date;
  action: string | null;
}

export type InsertDataType = 'labels' | 'labelAssignments';

export interface BigQueryLabelControlConfig {
  dataType: InsertDataType;
  data: Label[] | LabelAssignment[];
  action?: string;
}

class BigQueryLabelControl extends BigQuerySQLControl {
  private dataType: InsertDataType;
  private insertData: Label[] | LabelAssignment[];
  private action: string;

  constructor(config: BigQueryLabelControlConfig) {
    super('', false, {});
    this.dataType = config.dataType;
    this.insertData = config.data;
    this.action = config.action || 'insert';
  }

  private getTableName(): TableName {
    return this.dataType === 'labels'
      ? BIG_QUERY_TABLES.RAW_LABELS
      : BIG_QUERY_TABLES.RAW_LABEL_ASSIGNMENTS;
  }

  private transformLabelsToRawRecords(labels: Label[]): RawLabelRecord[] {
    return labels.map((label) => ({
      id: label.id,
      type: label.type,
      key: label.key,
      values: label.values || [],
      description: label.description || undefined,
      action: null,
      icon: label.icon,
      color: label.color
    }));
  }

  private transformLabelAssignmentsToRawRecords(
    assignments: LabelAssignment[]
  ): Omit<RawLabelAssignmentRecord, 'created_at'>[] {
    return assignments.map((assignment) => ({
      entityId: assignment.entityId,
      labelIds: assignment.labelIds || [],
      action: null
    }));
  }

  public async execute(benchmark?: Benchmark): Promise<ControlResultData[]> {
    try {
      if (!this.insertData.length) {
        console.log('No data to insert');
        return [];
      }

      const tableName = this.getTableName();
      const orgId = config.Global.organizationId;

      let documentsToInsert: any[];
      if (this.dataType === 'labels') {
        documentsToInsert = this.transformLabelsToRawRecords(
          this.insertData as Label[]
        );
      } else {
        documentsToInsert = this.transformLabelAssignmentsToRawRecords(
          this.insertData as LabelAssignment[]
        );
      }

      const tableSchema = BIG_QUERY_TABLE_SCHEMAS[tableName];
      if (!tableSchema) {
        throw new Error(`Table schema not found for table: ${tableName}`);
      }

      const tableSchemas: Record<string, TableSchema> = {
        [tableName]: tableSchema
      };

      const bigQueryClient = await getBigQueryClient({
        datasetId: orgId,
        tableSchemas: tableSchemas as DatabaseSchemas,
        initialize: false
      });

      await bigQueryClient.parallelWrite(tableName, documentsToInsert);

      const insertedCount = documentsToInsert.length;
      console.log(
        `Successfully inserted ${insertedCount} records into ${tableName}`
      );

      if (this.dataType === 'labels') {
        return this.insertData.map((label) => ({ label: label as Label }));
      } else {
        return this.insertData.map((assignment) => ({
          labelAssignment: assignment as LabelAssignment
        }));
      }
    } catch (error) {
      console.log(`Failed to insert data into ${this.getTableName()}:`, error);
      throw error;
    }
  }
}

export default BigQueryLabelControl;
