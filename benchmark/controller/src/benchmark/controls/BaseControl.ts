import { Benchmark } from '../types/benchmarkTypes';
import { ControlResultData } from './types';
import { ControlDataType } from '../controlDependencies';

abstract class BaseControl {
  public context: string;

  constructor(context: string) {
    this.context = context;
  }

  public abstract execute(benchmark?: Benchmark): Promise<ControlResultData[]>;

  /**
   * Override this method to specify what data types this control depends on
   */
  public getDependencies(): ControlDataType[] {
    return [];
  }

  /**
   * Override this method to specify what data types this control produces
   */
  public getProducedDataTypes(): ControlDataType[] {
    return [];
  }
}

export default BaseControl;
