import { Label } from '@globalTypes/ndrBenchmarkTypes';
import { ControlResultData } from './types';
import { Benchmark } from '../types/benchmarkTypes';
import BaseControl from './BaseControl';
import { getEntitiesMetrics } from '../../firestore/entityMetrics';


class EnrichedEntityControl extends BaseControl {
  private organizationId: string;

  constructor(organizationId: string) {
    super('EnrichedEntityControl');
    this.organizationId = organizationId;
  }

  public async execute(benchmark: Benchmark): Promise<ControlResultData[]> {
    const result: ControlResultData[] = [];
    const entitiesMetrics = await getEntitiesMetrics(this.organizationId);
    const { entities, allLabelAssignments = [], allLabels = [] } = benchmark;

    const labels = new Map(allLabels.map((label) => [label.id, label]));
    const assignmentsMap = new Map(
      allLabelAssignments.map((la) => [la.entityId, la])
    );

    entities.forEach((entity) => {
      const labelAssignment = assignmentsMap.get(entity.id);
      const entityLabels =
        labelAssignment?.labelIds
          .map((id) => labels.get(id))
          .filter((label): label is Label => !!label) || [];

      const metric = entitiesMetrics[entity.id];
      const { entityRef, impactAnalysis, ...restMetric } = metric || {};

      const enrichedEntity = {
        ...entity,
        ...(metric && { metrics: restMetric }),
        labels: entityLabels
      };

      result.push({ enrichedEntity });
    });

    return result;
  }
}

export default EnrichedEntityControl;
