import { v4 as uuidv4 } from 'uuid';

import {
  CloudEntity,
  DETECTION_STATUS,
  DetectionWithControlResults,
  SQLTrafficEvent,
  UniqueTraffic
} from '@globalTypes/ndrBenchmarkTypes';
import { grantTableAccess } from '../../big-query-client/bigquery/access';
import { generateServiceAccountEmail } from '../../big-query-client/bigquery/utils';
import { isDetectionControlActive } from '../../firestore/getDetections';
import log from '../../logger';
import { transformSQLTrafficPatterns } from '../../services/issues/utils';
import { VertexDetection } from '../../services/vertex-ai/models/types';
import { VertexService } from '../../services/vertex-ai/vertex.service';
import { generateDetectionTags } from '../../utils/detectionTagUtils';
import { PORT0_AI_CONTROL_ID, RECENT_TRAFFIC_TABLE_NAME } from '../consts';
import { Benchmark } from '../types/benchmarkTypes';
import BaseControl from './BaseControl';
import BigQuerySQLControl from './BigQuerySQLControl';
import { ControlResultData } from './types';

class VertexDetectionControl extends BaseControl {
  private organizationId: string;
  private serviceAccountEmail: string;
  private vertexService: VertexService;
  private allowFailure: boolean;
  private recentTrafficControl: BigQuerySQLControl;
  private readonly CONTROL_ID = PORT0_AI_CONTROL_ID;

  constructor(organizationId: string, allowFailure = false) {
    super('VertexDetectionControl');
    this.organizationId = organizationId;
    this.serviceAccountEmail = generateServiceAccountEmail(organizationId);
    this.allowFailure = allowFailure;
    this.vertexService = new VertexService();
    this.recentTrafficControl = new BigQuerySQLControl(
      'traffic-patterns/recent_traffic_for_vertex.sql',
      allowFailure
    );
  }

  public async execute(benchmark: Benchmark): Promise<ControlResultData[]> {
    log.debug('Beginning VertexAI detection control execution...');

    try {
      // Check if the port0-ai control is active
      if (!(await this.isAIControlEnabled())) {
        return [];
      }

      // Grant access to the recent traffic table
      await grantTableAccess({
        datasetId: this.organizationId,
        tableId: RECENT_TRAFFIC_TABLE_NAME,
        serviceAccountEmail: this.serviceAccountEmail
      });

      // Fetch recent traffic data using BigQuerySQLControl
      const recentTraffic = await this.fetchRecentTraffic(benchmark);

      if (recentTraffic.length === 0) {
        log.debug('No recent traffic data found for VertexAI analysis', {
          organizationId: this.organizationId
        });
        return [];
      }

      // Generate detections using VertexAI
      const vertexDetections =
        await this.generateVertexDetections(recentTraffic);

      // Convert VertexAI detections to DetectionWithControlResults format
      const detections = this.convertToDetections(
        vertexDetections,
        recentTraffic
      );

      log.debug(`VertexAI detection control execution completed successfully`, {
        organizationId: this.organizationId,
        detectionsCount: detections.length
      });

      return detections.map((detection) => ({ aiDetection: detection }));
    } catch (err) {
      log.error(
        `Error computing VertexAI detections: ${(err as Error).message}`,
        {
          organizationId: this.organizationId,
          error: err
        }
      );

      if (this.allowFailure) {
        return [];
      }

      throw err;
    }
  }

  private async isAIControlEnabled(): Promise<boolean> {
    const isControlActive = await isDetectionControlActive(
      this.organizationId,
      this.CONTROL_ID
    );

    if (!isControlActive) {
      log.debug('VertexAI detection control is disabled, skipping execution', {
        organizationId: this.organizationId,
        controlId: this.CONTROL_ID
      });
      return false;
    }

    return true;
  }

  private async fetchRecentTraffic(
    benchmark: Benchmark
  ): Promise<SQLTrafficEvent[]> {
    log.debug('Fetching recent traffic for VertexAI analysis', {
      organizationId: this.organizationId
    });

    try {
      const result = await this.recentTrafficControl.execute(benchmark);
      return result as SQLTrafficEvent[];
    } catch (err) {
      log.error('Error fetching recent traffic data', {
        organizationId: this.organizationId,
        error: err
      });
      return [];
    }
  }

  private async generateVertexDetections(
    trafficEvents: SQLTrafficEvent[]
  ): Promise<VertexDetection[]> {
    log.debug('Generating detections using VertexAI', {
      organizationId: this.organizationId,
      trafficEventsCount: trafficEvents.length
    });

    try {
      const detections =
        await this.vertexService.generateDetections(trafficEvents);

      log.debug('VertexAI detections generated successfully', {
        organizationId: this.organizationId,
        detectionsCount: detections.length
      });

      return detections;
    } catch (err) {
      log.error('Error generating VertexAI detections', {
        organizationId: this.organizationId,
        error: err
      });
      return [];
    }
  }

  private convertToDetections(
    vertexDetections: VertexDetection[],
    trafficEvents: SQLTrafficEvent[]
  ): DetectionWithControlResults[] {
    const detections: DetectionWithControlResults[] = [];

    for (const vertexDetection of vertexDetections) {
      // Find the related traffic events based on the IDs provided by VertexAI
      const relatedTrafficEvents = trafficEvents.filter((event) =>
        vertexDetection.relatedTrafficIds.includes(event.id)
      );

      if (relatedTrafficEvents.length === 0) {
        log.warn('No related traffic events found for VertexAI detection', {
          organizationId: this.organizationId,
          detectionTitle: vertexDetection.title,
          relatedTrafficIds: vertexDetection.relatedTrafficIds
        });
        continue;
      }

      // Transform SQL traffic events to UniqueTraffic format
      const uniqueTraffic: UniqueTraffic[] = relatedTrafficEvents
        .slice(0, 100) // Limit to 100 events per detection
        .map((event) => transformSQLTrafficPatterns(event));

      const relatedEntitiesIds = this.getRelatedEntitiesIds(uniqueTraffic);
      const tags = generateDetectionTags(uniqueTraffic);

      const detection: DetectionWithControlResults = {
        id: uuidv4(),
        controlId: PORT0_AI_CONTROL_ID, // All VertexAI detections use the same control ID
        category: 'AI', // For now, all VertexAI detections are in the AI category
        title: vertexDetection.title,
        subTitle: vertexDetection.subTitle,
        explanation: vertexDetection.explanation,
        severity: this.mapVertexSeverityToDetectionSeverity(
          vertexDetection.severity
        ),
        controlResults: uniqueTraffic,
        relatedEntitiesIds,
        status: DETECTION_STATUS.OPEN,
        createdTime: new Date(),
        updatedTime: new Date(),
        tags
      };

      detections.push(detection);
    }

    return detections;
  }

  private getRelatedEntitiesIds(trafficEvents: UniqueTraffic[]): string[] {
    return Array.from(
      new Set(trafficEvents.flatMap((event) => [event.src.id, event.dst.id]))
    );
  }

  private mapVertexSeverityToDetectionSeverity(
    vertexSeverity: string
  ): 'info' | 'low' | 'medium' | 'high' | 'critical' {
    // Map VertexAI severity to detection severity
    const severityMap: Record<
      string,
      'info' | 'low' | 'medium' | 'high' | 'critical'
    > = {
      info: 'info',
      low: 'low',
      medium: 'medium',
      high: 'high',
      critical: 'critical'
    };

    return severityMap[vertexSeverity] || 'low';
  }
}

export default VertexDetectionControl;
