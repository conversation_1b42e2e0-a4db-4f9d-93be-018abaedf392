import log from '@logger';

import { ControlResultData } from './types';
import { Benchmark } from '../types/benchmarkTypes';
import BaseControl from './BaseControl';
import { getEntitiesMetrics } from '../../firestore/entityMetrics';
import { EntityMetricsDocument } from '../../firestore/entityMetrics';

const CHART_NAME = 'Critical Entities by Risk Score';
const GRAPH_TYPE = 'bar';
const STAT_TYPE = 'critical_entities_to_segment';

interface ChartData {
  key: string;
  data: number;
}

class EntityRiskControl extends BaseControl {
  private organizationId: string;

  constructor(organizationId: string) {
    super('EntityRiskControl');
    this.organizationId = organizationId;
  }

  private convertToChartFormat(
    entityMetrics: Record<string, EntityMetricsDocument>
  ): ChartData[] {
    return Object.entries(entityMetrics).map(([entityId, metrics]) => {
      return {
        key: entityId,
        data: metrics.riskScore?.value || 0
      };
    });
  }

  private sortAndTakeTopResults(chartData: ChartData[]) {
    return chartData.sort((a, b) => b.data - a.data).slice(0, 10);
  }

  private buildStatObject(sortedData: ChartData[]): ControlResultData[] {
    return [
      {
        stat: {
          type: STAT_TYPE,
          title: CHART_NAME,
          chartType: GRAPH_TYPE,
          data: sortedData
        }
      }
    ];
  }

  public async execute(benchmark: Benchmark): Promise<ControlResultData[]> {
    const entitiesMetrics = await getEntitiesMetrics(this.organizationId);

    const chartData = this.convertToChartFormat(entitiesMetrics);

    const sortedData = this.sortAndTakeTopResults(chartData);

    const result = this.buildStatObject(sortedData);

    log.info(`Entities risk stat calculated successfully`, {
      organizationId: this.organizationId
    });

    return result;
  }
}

export default EntityRiskControl;
