import {
  VertexIssueControl,
  NotificationStatisticsControl,
  BigQuerySQLControl,
  BigQueryEntitiesControl,
  LabelsControl,
  DetectionsControl,
  VertexDetectionControl,
  DetectionConfidenceControl,
  EntityRiskControl
} from './controls';

import getIssues from '../firestore/getIssues';
import BaseControl from './controls/BaseControl';
import BigQueryNetworkRiskControl from './controls/BigQueryNetworkRiskControl';
import EnrichedEntityControl from './controls/EnrichedEntityControl';

export function getStatsControls(organizationId: string) {
  return [
    new BigQuerySQLControl('stats/instances.sql'),
    new EntityRiskControl(organizationId),
    new BigQuerySQLControl('stats/employee_diverse_connections.sql'),
    new BigQuerySQLControl('stats/most_connected_entities.sql'),
    new BigQuerySQLControl('stats/connections_type.sql'),
    new BigQueryNetworkRiskControl('stats/network_risk_score.sql')
  ];
}

export const ENTITIES_CONTROLS = [
  new BigQueryEntitiesControl('entities/entities.sql')
];

export const NOTIFICATIONS_CONTROLS = [
  new BigQuerySQLControl('notifications/known_ports.sql')
];

export async function getBenchmarkControls(organizationId: string) {
  const existingIssues = await getIssues(organizationId);

  const controls: BaseControl[] = [
    ...ENTITIES_CONTROLS,
    ...NOTIFICATIONS_CONTROLS,
    ...getStatsControls(organizationId),
    new NotificationStatisticsControl(organizationId),
    new BigQuerySQLControl('security-groups/security_groups.sql', true),
    new VertexIssueControl('issue-hints/microsegmentation.sql', existingIssues),
    new LabelsControl(organizationId),
    new DetectionsControl(organizationId, true),
    new VertexDetectionControl(organizationId, true),
    new DetectionConfidenceControl(organizationId),
    new EnrichedEntityControl(organizationId)
  ];

  return controls;
}

export const DETECTIONS_TABLES = {
  TRAFFIC: 'traffic',
  COMPLETED_TRAFFIC: 'complete_traffic',
  COMPLETED_30_DAYS_TRAFFIC: 'complete_30_days_traffic',
  COMPLETED_30_DAYS_TRAFFIC_EXCEPT_CURRENT_INTERVAL:
    'completed_30_days_traffic_except_current_interval',
  COMPLETED_30_DAYS_TRAFFIC_EXCEPT_CURRENT_INTERVAL_SRC_DST_IDS:
    'completed_30_days_traffic_except_current_interval_src_dst_ids',
  TRAFFIC_LOGS: 'traffic_logs'
} as const;

export const SELECT_FIELDS = [
  'id',
  'src_id',
  'src_name',
  'src_process',
  'src_cmd',
  'src_type',
  'src_addr',
  'dst_id',
  'dst_name',
  'dst_process',
  'dst_cmd',
  'dst_type',
  'dst_addr',
  'session_count',
  'port'
];

export const RECENT_TRAFFIC_TABLE_NAME = 'recent_traffic';
export const ENRICHED_TABLE_NAME = 'enriched';

export const PORT0_AI_CONTROL_ID = 'port0-ai';
