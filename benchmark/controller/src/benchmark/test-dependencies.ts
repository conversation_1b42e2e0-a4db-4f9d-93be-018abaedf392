/**
 * Simple test script to verify the dependency system works correctly
 */

import { 
  ControlDataType, 
  sortControlsByDependencies, 
  ControlWithDependencies 
} from './controlDependencies';
import { ControlResultData } from './controls/types';
import { Benchmark } from './types/benchmarkTypes';

// Mock control class for testing
class MockControl implements ControlWithDependencies {
  public context: string;
  private dependencies: ControlDataType[];
  private producedDataTypes: ControlDataType[];

  constructor(
    context: string, 
    dependencies: ControlDataType[] = [], 
    producedDataTypes: ControlDataType[] = []
  ) {
    this.context = context;
    this.dependencies = dependencies;
    this.producedDataTypes = producedDataTypes;
  }

  public async execute(benchmark?: Benchmark): Promise<ControlResultData[]> {
    console.log(`Executing ${this.context}`);
    return [];
  }

  public getDependencies(): ControlDataType[] {
    return this.dependencies;
  }

  public getProducedDataTypes(): ControlDataType[] {
    return this.producedDataTypes;
  }
}

// Test the dependency sorting
function testDependencySorting() {
  console.log('Testing dependency sorting...');

  // Create mock controls with dependencies
  const controls: ControlWithDependencies[] = [
    new MockControl('EnrichedEntityControl', [ControlDataType.ENTITIES, ControlDataType.LABELS], [ControlDataType.ENRICHED_ENTITIES]),
    new MockControl('DetectionConfidenceControl', [ControlDataType.DETECTIONS, ControlDataType.LABELS], []),
    new MockControl('LabelsControl', [ControlDataType.ENTITIES], [ControlDataType.LABELS]),
    new MockControl('DetectionsControl', [ControlDataType.ENTITIES], [ControlDataType.DETECTIONS]),
    new MockControl('EntitiesControl', [], [ControlDataType.ENTITIES]),
    new MockControl('StatsControl', [], [ControlDataType.STATS]),
    new MockControl('NotificationStatsControl', [ControlDataType.NOTIFICATIONS], [ControlDataType.STATS]),
    new MockControl('NotificationsControl', [], [ControlDataType.NOTIFICATIONS])
  ];

  try {
    const sortedControls = sortControlsByDependencies(controls);
    
    console.log('Sorted control execution order:');
    sortedControls.forEach((control, index) => {
      const deps = control.getDependencies();
      const produces = control.getProducedDataTypes();
      console.log(`${index + 1}. ${control.context}`);
      console.log(`   Dependencies: [${deps.join(', ')}]`);
      console.log(`   Produces: [${produces.join(', ')}]`);
    });

    // Verify the order is correct
    const controlNames = sortedControls.map(c => c.context);
    
    // Entities should come before Labels
    const entitiesIndex = controlNames.indexOf('EntitiesControl');
    const labelsIndex = controlNames.indexOf('LabelsControl');
    console.assert(entitiesIndex < labelsIndex, 'Entities should come before Labels');

    // Labels should come before DetectionConfidence
    const detectionConfidenceIndex = controlNames.indexOf('DetectionConfidenceControl');
    console.assert(labelsIndex < detectionConfidenceIndex, 'Labels should come before DetectionConfidence');

    // Detections should come before DetectionConfidence
    const detectionsIndex = controlNames.indexOf('DetectionsControl');
    console.assert(detectionsIndex < detectionConfidenceIndex, 'Detections should come before DetectionConfidence');

    // Notifications should come before NotificationStats
    const notificationsIndex = controlNames.indexOf('NotificationsControl');
    const notificationStatsIndex = controlNames.indexOf('NotificationStatsControl');
    console.assert(notificationsIndex < notificationStatsIndex, 'Notifications should come before NotificationStats');

    console.log('✅ All dependency order tests passed!');
    return true;
  } catch (error) {
    console.error('❌ Dependency sorting failed:', error);
    return false;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testDependencySorting();
}

export { testDependencySorting };
