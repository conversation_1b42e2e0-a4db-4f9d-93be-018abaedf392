import {
  Issue,
  SQLTrafficEvent,
  TrafficPattern,
  Notification,
  Stat,
  Label,
  EndpointEntity,
  LabelAssignment,
  DetectionWithControlResults,
  SQLAwsSecurityGroup,
  EnrichedEntity
} from '@globalTypes/ndrBenchmarkTypes';

export type Benchmark = {
  entities: EndpointEntity[];
  enrichedEntities: EnrichedEntity[];
  issues: Issue[];
  trafficPatterns: TrafficPattern[];
  notifications: Notification[];
  stats: Stat[];
  labels: Label[];
  labelAssignments: LabelAssignment[];
  allLabels?: Label[];
  allLabelAssignments?: LabelAssignment[];
  detections: DetectionWithControlResults[];
  aiDetections: DetectionWithControlResults[];
  securityGroups: SQLAwsSecurityGroup[];
};

export type IssueHint = {
  trafficPatterns: SQLTrafficEvent[];
  hint: string;
};
