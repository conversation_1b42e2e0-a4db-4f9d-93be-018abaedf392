import BaseControl from './controls/BaseControl';
import {
  BigQuerySQLControl,
  BigQueryEntitiesControl,
  LabelsControl,
  DetectionsControl,
  VertexDetectionControl,
  DetectionConfidenceControl,
  EntityRiskControl,
  NotificationStatisticsControl,
  VertexIssueControl
} from './controls';
import BigQueryNetworkRiskControl from './controls/BigQueryNetworkRiskControl';
import EnrichedEntityControl from './controls/EnrichedEntityControl';

export interface ControlGroup {
  name: string;
  controls: BaseControl[];
  dependencies: string[]; // Names of groups this group depends on
}

export interface ControlDependencyMap {
  [controlName: string]: {
    dependencies: string[];
    group: string;
  };
}

/**
 * Defines the dependency relationships between controls
 */
export const CONTROL_DEPENDENCIES: ControlDependencyMap = {
  // Independent controls - no dependencies
  'BigQueryEntitiesControl': { dependencies: [], group: 'entities' },
  'BigQuerySQLControl: "notifications/notifications.sql"': { dependencies: [], group: 'notifications' },
  'BigQuerySQLControl: "stats/instances.sql"': { dependencies: [], group: 'stats' },
  'EntityRiskControl': { dependencies: [], group: 'stats' },
  'BigQuerySQLControl: "stats/employee_diverse_connections.sql"': { dependencies: [], group: 'stats' },
  'BigQuerySQLControl: "stats/most_connected_entities.sql"': { dependencies: [], group: 'stats' },
  'BigQuerySQLControl: "stats/connections_type.sql"': { dependencies: [], group: 'stats' },
  'BigQueryNetworkRiskControl': { dependencies: [], group: 'stats' },
  'NotificationStatisticsControl': { dependencies: ['notifications'], group: 'notification_stats' },
  'BigQuerySQLControl: "security-groups/security_groups.sql"': { dependencies: [], group: 'security_groups' },
  
  // Dependent controls
  'VertexIssueControl': { dependencies: [], group: 'issues' }, // Uses existing issues, not dependent on current run
  'LabelsControl': { dependencies: ['entities'], group: 'labels' },
  'DetectionsControl': { dependencies: ['entities'], group: 'detections' },
  'VertexDetectionControl': { dependencies: ['entities'], group: 'detections' },
  'DetectionConfidenceControl': { dependencies: ['detections', 'labels'], group: 'detection_confidence' },
  'EnrichedEntityControl': { dependencies: ['entities', 'labels'], group: 'enriched_entities' }
};

/**
 * Defines execution groups in dependency order
 */
export const EXECUTION_GROUPS: ControlGroup[] = [
  {
    name: 'entities',
    controls: [], // Will be populated dynamically
    dependencies: []
  },
  {
    name: 'notifications',
    controls: [],
    dependencies: []
  },
  {
    name: 'stats',
    controls: [],
    dependencies: []
  },
  {
    name: 'security_groups',
    controls: [],
    dependencies: []
  },
  {
    name: 'issues',
    controls: [],
    dependencies: []
  },
  {
    name: 'notification_stats',
    controls: [],
    dependencies: ['notifications']
  },
  {
    name: 'labels',
    controls: [],
    dependencies: ['entities']
  },
  {
    name: 'detections',
    controls: [],
    dependencies: ['entities']
  },
  {
    name: 'detection_confidence',
    controls: [],
    dependencies: ['detections', 'labels']
  },
  {
    name: 'enriched_entities',
    controls: [],
    dependencies: ['entities', 'labels']
  }
];

/**
 * Categorizes controls into execution groups based on their dependencies
 */
export function categorizeControlsIntoGroups(controls: BaseControl[]): ControlGroup[] {
  // Create a copy of execution groups with proper typing
  const groups: ControlGroup[] = EXECUTION_GROUPS.map(group => ({
    ...group,
    controls: [] as BaseControl[]
  }));

  // Categorize each control into its appropriate group
  for (const control of controls) {
    const controlKey = control.context;
    const dependency = CONTROL_DEPENDENCIES[controlKey];

    if (dependency) {
      const group = groups.find(g => g.name === dependency.group);
      if (group) {
        group.controls.push(control);
      } else {
        console.warn(`No group found for control: ${controlKey}`);
      }
    } else {
      console.warn(`No dependency mapping found for control: ${controlKey}`);
    }
  }

  // Filter out empty groups
  return groups.filter(group => group.controls.length > 0);
}

/**
 * Validates that all dependencies for a group are satisfied
 */
export function validateGroupDependencies(
  group: ControlGroup,
  completedGroups: Set<string>
): boolean {
  return group.dependencies.every(dep => completedGroups.has(dep));
}

/**
 * Gets the next executable groups (groups whose dependencies are satisfied)
 */
export function getNextExecutableGroups(
  remainingGroups: ControlGroup[],
  completedGroups: Set<string>
): ControlGroup[] {
  return remainingGroups.filter(group => 
    validateGroupDependencies(group, completedGroups)
  );
}
