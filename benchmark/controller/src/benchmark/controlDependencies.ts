import BaseControl from './controls/BaseControl';

/**
 * Types of data that controls can produce/depend on
 */
export enum ControlDataType {
  ENTITIES = 'entities',
  NOTIFICATIONS = 'notifications',
  STATS = 'stats',
  SECURITY_GROUPS = 'security_groups',
  ISSUES = 'issues',
  LABELS = 'labels',
  DETECTIONS = 'detections',
  ENRICHED_ENTITIES = 'enriched_entities'
}

/**
 * Interface for controls with dependencies
 */
export interface ControlWithDependencies extends BaseControl {
  getDependencies(): ControlDataType[];
  getProducedDataTypes(): ControlDataType[];
}

/**
 * Check if a control can be executed based on available data
 */
export function canExecuteControl(
  control: ControlWithDependencies,
  availableData: Set<ControlDataType>
): boolean {
  const dependencies = control.getDependencies();
  return dependencies.every(dep => availableData.has(dep));
}

/**
 * Find the next executable control from a list of remaining controls
 */
export function getNextExecutableControl(
  remainingControls: ControlWithDependencies[],
  availableData: Set<ControlDataType>
): ControlWithDependencies | null {
  return remainingControls.find(control => canExecuteControl(control, availableData)) || null;
}

/**
 * Sort controls by dependency order (topological sort)
 */
export function sortControlsByDependencies(controls: ControlWithDependencies[]): ControlWithDependencies[] {
  const sorted: ControlWithDependencies[] = [];
  const remaining = [...controls];
  const availableData = new Set<ControlDataType>();

  while (remaining.length > 0) {
    const nextControl = getNextExecutableControl(remaining, availableData);

    if (!nextControl) {
      const remainingNames = remaining.map(c => c.context);
      throw new Error(`Circular dependency detected or missing dependencies. Remaining controls: ${remainingNames.join(', ')}`);
    }

    // Remove from remaining and add to sorted
    const index = remaining.indexOf(nextControl);
    remaining.splice(index, 1);
    sorted.push(nextControl);

    // Add produced data types to available data
    nextControl.getProducedDataTypes().forEach(dataType => {
      availableData.add(dataType);
    });
  }

  return sorted;
}
