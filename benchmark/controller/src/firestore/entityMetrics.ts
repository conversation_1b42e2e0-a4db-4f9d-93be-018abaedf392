import log from '@logger';
import getFirestore from './getFirestore';

export interface EntityMetricsDocument {
  riskScore: {
    value: number;
    riskExplanation: string[];
    updatedAt: Date;
  };
  firewallRulesCount?: number;
  potentialRiskScore?: {
    value: number;
    updatedAt: Date;
  };
  impactAnalysis?: any;
  entityRef? : any;
}

type EntityToRiskMapper = Record<string, EntityMetricsDocument>;

export async function getEntitiesMetrics(
  organizationId: string
): Promise<EntityToRiskMapper> {
  const firestore = getFirestore();

  const metricsRef = firestore
    .collection('organizations')
    .doc(organizationId)
    .collection('dashboards')
    .doc('NDR')
    .collection('entityMetrics');

  const metricsSnapshot = await metricsRef.get();

  if (metricsSnapshot.empty) {
    log.info('No entity metrics found for organization', {
      organizationId,
      context: 'getEntityMetrics'
    });

    return {};
  }

  const metrics = metricsSnapshot.docs.reduce((acc, doc) => {
    acc[doc.id] = doc.data() as EntityMetricsDocument;

    return acc;
  }, {} as EntityToRiskMapper);

  return metrics;
}
