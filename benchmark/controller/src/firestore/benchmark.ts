import {
  DetectionWithControlResults,
  EnrichedEntity,
  Entity,
  Issue,
  ISSUE_STATUS,
  Label,
  LabelAssignment,
  Notification,
  SQLAwsSecurityGroup,
  Stat,
  TrafficPattern
} from '@globalTypes/ndrBenchmarkTypes';
import log from '@logger';
import { BulkWriter, CollectionReference } from 'firebase-admin/firestore';
import { Benchmark } from '../benchmark/types/benchmarkTypes';
import * as config from '../config';
import { hash, hashIssue, isIssueValid } from '../utils/issueUtils';
import { syncDetectionsHitCountsForOrganization } from './detectionsHitCountSync';
import getFirestore from './getFirestore';
import getIssues, { getOpenVertexIssues } from './getIssues';
import { getEntityTrafficPatterns } from './getTrafficPatterns';

import { syncAllDetectionsToBigQuery } from '../big-query/detections';
import {
  syncAllLabelAssignmentsToBigQuery,
  syncAllLabelsToBigQuery
} from '../big-query/labels';
import { SecurityGroupIssue } from '../services/issues/types';
import {
  getIssuesCountByEntityId,
  isAwsSecurityGroupIssueHasChanged,
  MAX_TRAFFIC_PATTERNS_PER_ENTITY
} from '../services/issues/utils';

type BenchmarkCollections = {
  entities: CollectionReference;
  enrichedEntities: CollectionReference;
  trafficPatterns: CollectionReference;
  issues: CollectionReference;
  notifications: CollectionReference;
  stats: CollectionReference;
  labels: CollectionReference;
  labelAssignments: CollectionReference;
  detections: CollectionReference;
  detectionControlMetrics: CollectionReference;
  securityGroups: CollectionReference;
  detectionCategories: CollectionReference;
  issueCategories: CollectionReference;
};

export class BenchmarkFirestoreWriter {
  private collections: BenchmarkCollections;
  private benchmark: Benchmark;
  private bulkWriter: BulkWriter;

  constructor(benchmark: Benchmark, organizationId: string) {
    const firestore = getFirestore();
    this.collections =
      BenchmarkFirestoreWriter.getBenchmarkCollections(organizationId);
    this.benchmark = benchmark;
    this.bulkWriter = firestore.bulkWriter({
      throttling: {
        initialOpsPerSecond: 500,
        maxOpsPerSecond: 10000
      }
    });

    // Setup bulkWriter error handling
    this.bulkWriter.onWriteError((error) => {
      log.error(
        `Error writing document: ${error.message} ${error.documentRef.path}`,
        {
          context: 'setBenchmark'
        }
      );
      return error.failedAttempts < 5;
    });
  }

  async syncBenchmark(): Promise<void> {
    await this.syncIssues(this.benchmark.issues);

    const issuesByEntityId = await this.syncIssuesCountByEntityId();

    const entitiesWithIssueCounts = this.enrichEntitiesWithIssueCounts(
      this.benchmark.entities as unknown as Entity[],
      issuesByEntityId
    );
    const enrichedEntitiesWithIssueCounts = this.enrichEntitiesWithIssueCounts(
      this.benchmark.enrichedEntities as Entity[],
      issuesByEntityId
    );

    await Promise.all([
      this.syncEntities(entitiesWithIssueCounts),
      this.syncEnrichedEntities(enrichedEntitiesWithIssueCounts as EnrichedEntity[]),
      this.syncTrafficPatterns(this.benchmark.trafficPatterns),
      this.syncNotifications(this.benchmark.notifications),
      this.syncStats(this.benchmark.stats),
      this.syncLabels(this.benchmark.labels),
      this.syncLabelAssignments(this.benchmark.labelAssignments),
      syncAllLabelsToBigQuery(this.benchmark.allLabels),
      syncAllLabelAssignmentsToBigQuery(this.benchmark.allLabelAssignments),
      this.syncSecurityGroups(this.benchmark.securityGroups),
      this.syncAllDetectionsCollections()
    ]);

    await syncDetectionsHitCountsForOrganization(config.Global.organizationId);
  }

  private enrichEntitiesWithIssueCounts(
    entities: Entity[],
    issuesByEntityId: Record<string, number>
  ): Entity[] {
    return entities.map((entity) => ({
      ...entity,
      insightsCount: issuesByEntityId[entity.id] || 0
    }));
  }

  private async syncAllDetectionsCollections(): Promise<void> {
    const logger = log.child({
      context: 'syncAllDetectionsCollections'
    });
    try {
      if (this.benchmark.detections.length > 0) {
        await this.syncDetections(this.benchmark.detections);
        await syncAllDetectionsToBigQuery(this.benchmark.detections);
      }
    } catch (error) {
      logger.error(`Error syncing detections collections`, { error });

      throw error;
    }
  }

  private async syncSecurityGroups(
    securityGroups: SQLAwsSecurityGroup[]
  ): Promise<void> {
    if (securityGroups.length > 0) {
      await this.syncCollection({
        collectionRef: this.collections.securityGroups,
        items: securityGroups,
        keyingFunction: (securityGroup) => securityGroup.id
      });
    }
  }

  private async syncIssuesCountByEntityId(): Promise<Record<string, number>> {
    const allIssues = await getIssues(config.Global.organizationId);

    const firestore = getFirestore();
    const benchmarkRef = firestore.doc(
      `organizations/${config.Global.organizationId}/dashboards/NDR`
    );

    const entitiesIssuesCountCollection = benchmarkRef.collection(
      'entitiesIssuesCount'
    );

    const issuesByEntityId = getIssuesCountByEntityId(allIssues);

    const docRef = entitiesIssuesCountCollection.doc('summary');
    await docRef.set(issuesByEntityId);

    return issuesByEntityId;
  }

  private async syncDetections(
    detections: DetectionWithControlResults[]
  ): Promise<void> {
    if (detections.length > 0) {
      // Strip controlResults from detection objects for main collection sync
      const detectionsWithoutControlResults = detections.map(
        ({ controlResults, ...detectionWithoutControlResults }) =>
          detectionWithoutControlResults
      );

      // Sync stripped detection documents using existing bulkWriter-based function
      await this.syncCollection({
        collectionRef: this.collections.detections,
        items: detectionsWithoutControlResults,
        keyingFunction: (detection) => detection.id
      });

      // Add controlResults to subcollection documents using bulkWriter
      detections.forEach((detection) => {
        const controlResultsRef = this.collections.detections
          .doc(detection.id)
          .collection('controlResults')
          .doc('data');

        this.bulkWriter.set(controlResultsRef, {
          data: detection.controlResults
        });
      });
    }
  }
  private async getCategoryCountMap(): Promise<Record<string, number>> {
    // Get the summary document reference
    const summaryDocRef = this.collections.detectionCategories.doc('summary');

    try {
      // Try to get existing summary document
      const summaryDoc = await summaryDocRef.get();

      if (summaryDoc.exists) {
        // If document exists, merge with existing counts
        const existingData =
          (summaryDoc.data() as Record<string, number>) || {};

        return existingData;
      } else {
        // Create initial map from existing detections in firestore
        const categoryCountMap: Record<string, number> = {};

        // Query all existing detections to build the initial category count map
        const detectionsSnapshot = await this.collections.detections.get();

        detectionsSnapshot.forEach((doc) => {
          const detection = doc.data() as DetectionWithControlResults;
          if (detection.category) {
            categoryCountMap[detection.category] =
              (categoryCountMap[detection.category] || 0) + 1;
          }
        });

        return categoryCountMap;
      }
    } catch (error) {
      log.error('Error getting category counts:', error);
      throw error;
    }
  }

  private async syncEntities(entities: Entity[]): Promise<void> {
    if (entities.length > 0) {
      await this.syncCollection({
        collectionRef: this.collections.entities,
        items: entities,
        keyingFunction: (entity) => entity.id
      });
    }
  }

  private async syncEnrichedEntities(entities: EnrichedEntity[]): Promise<void> {
    if (entities.length > 0) {
      await this.syncCollection({
        collectionRef: this.collections.enrichedEntities,
        items: entities,
        keyingFunction: (entity) => entity.id
      });
    }
  }

  private async syncTrafficPatterns(
    newTrafficPatterns: TrafficPattern[]
  ): Promise<void> {
    if (newTrafficPatterns.length === 0) return;

    log.info(`Fetching previous traffic patterns`, {
      context: 'syncTrafficPatterns'
    });
    const existingTrafficPatternsSnapshot =
      await this.collections.trafficPatterns
        .select('time', 'id', 'sessionCount')
        .where('remoteAddr', '!=', null)
        .get();

    const existingTrafficPatterns = new Map<string, TrafficPattern>();

    const count = {
      added: 0,
      removed: 0,
      updated: 0
    };

    log.debug(
      `Transforming ${existingTrafficPatternsSnapshot.size} existing traffic patterns to map`,
      { context: 'syncTrafficPatterns' }
    );
    existingTrafficPatternsSnapshot.forEach((doc) => {
      const data = doc.data();
      existingTrafficPatterns.set(doc.id, data as TrafficPattern);
    });

    for (const trafficPattern of newTrafficPatterns) {
      if (!trafficPattern.id) {
        log.warn(
          `Traffic pattern with missing ID ${JSON.stringify(trafficPattern)}`
        );
        continue;
      }

      const existingTrafficPattern = existingTrafficPatterns.get(
        trafficPattern.id
      );

      if (!existingTrafficPattern) {
        // New traffic pattern - Need to insert him into the collection

        this.bulkWriter.set(
          this.collections.trafficPatterns.doc(trafficPattern.id),
          trafficPattern
        );
        count.added++;
        continue;
      }

      // Traffic pattern already exists
      // Delete from existing map - later on we will delete what remains
      existingTrafficPatterns.delete(trafficPattern.id);

      // Check if need to update the traffic pattern in collection
      if (
        existingTrafficPattern.sessionCount !== trafficPattern.sessionCount ||
        existingTrafficPattern.time !== trafficPattern.time
      ) {
        this.bulkWriter.update(
          this.collections.trafficPatterns.doc(trafficPattern.id),
          {
            sessionCount: trafficPattern.sessionCount,
            time: trafficPattern.time
          }
        );
        count.updated++;
      }
    }

    for (const [id] of existingTrafficPatterns) {
      this.bulkWriter.delete(this.collections.trafficPatterns.doc(id));
      count.removed++;
    }

    log.info(
      `Traffic patterns: added ${count.added}, updated ${count.updated}, removed ${count.removed}`,
      {
        context: 'syncTrafficPatterns',
        added: count.added,
        removed: count.removed,
        updated: count.updated
      }
    );
  }

  // private async syncCollectionWithPersistOptions<
  //   T extends { [key: string]: any },
  //   K extends keyof T
  // >({
  //   collectionRef,
  //   items,
  //   keyingFunction,
  //   persistedFields = [],
  //   defaultPersistedFieldsValues = {}
  // }: {
  //   collectionRef: CollectionReference;
  //   items: T[];
  //   keyingFunction?: (item: T) => string;
  //   persistedFields?: K[];
  //   defaultPersistedFieldsValues?: Partial<Pick<T, K>>;
  // }) {
  //   const ops = items.map(async (item) => {
  //     const docId = keyingFunction ? keyingFunction(item) : undefined;
  //     if (!docId) return;

  //     const docRef = collectionRef.doc(docId);
  //     const existingSnap = await docRef.get();
  //     const existingData = existingSnap.exists
  //       ? (existingSnap.data() as T)
  //       : undefined;

  //     if (existingData) {
  //       for (const field of persistedFields) {
  //         if (existingData[field] !== undefined) {
  //           item[field] = existingData[field];
  //         }
  //       }
  //     } else {
  //       for (const field of persistedFields) {
  //         if (defaultPersistedFieldsValues?.[field] !== undefined) {
  //           item[field] = defaultPersistedFieldsValues[field];
  //         }
  //       }
  //     }

  //     this.bulkWriter.set(docRef, item, { merge: true });
  //   });

  //   await Promise.all(ops);
  // }

  private async syncCollection({
    collectionRef,
    items,
    keyingFunction
  }: {
    collectionRef: CollectionReference;
    items: any[];
    keyingFunction?: (item: any) => string;
  }) {
    items.forEach((item) => {
      const subcollectionName = item.subcollectionName;
      if (!subcollectionName) {
        const docRef = keyingFunction
          ? collectionRef.doc(keyingFunction(item))
          : collectionRef.doc();
        this.bulkWriter.set(docRef, item);
      } else {
        const { data, subcollectionName, ...itemWithoutData } = item;
        const docRef = keyingFunction
          ? collectionRef.doc(keyingFunction(itemWithoutData))
          : collectionRef.doc();
        this.bulkWriter.set(docRef, itemWithoutData);

        if (Array.isArray(data)) {
          data.forEach((entry) => {
            const subDocRef = docRef.collection(subcollectionName).doc(); // auto ID
            this.bulkWriter.set(subDocRef, entry);
          });
        } else {
          const subDocRef = docRef.collection(subcollectionName).doc();
          this.bulkWriter.set(subDocRef, data);
        }
      }
    });
  }

  private async syncNotifications(
    notifications: Notification[]
  ): Promise<void> {
    if (notifications.length > 0) {
      await this.syncCollection({
        collectionRef: this.collections.notifications,
        items: notifications,
        keyingFunction: hash
      });
    }
  }

  private async syncStats(stats: Stat[]): Promise<void> {
    if (stats.length > 0) {
      await this.syncCollection({
        collectionRef: this.collections.stats,
        items: stats,
        keyingFunction: (stat) => stat.type
      });
    }
  }

  private async syncLabels(labels: Label[]): Promise<void> {
    if (labels.length > 0) {
      await this.syncCollection({
        collectionRef: this.collections.labels,
        items: labels,
        keyingFunction: (label) => label.id
      });
    }
  }

  private async syncLabelAssignments(
    labelAssignments: LabelAssignment[]
  ): Promise<void> {
    if (labelAssignments.length > 0) {
      const docsToSync = [];
      for (const labelAssignment of labelAssignments) {
        if (labelAssignment.labelIds.length) {
          docsToSync.push(labelAssignment);
        } else {
          this.bulkWriter.delete(
            this.collections.labelAssignments.doc(labelAssignment.entityId)
          );
        }
      }

      await this.syncCollection({
        collectionRef: this.collections.labelAssignments,
        items: docsToSync,
        keyingFunction: (labelAssignment) => labelAssignment.entityId
      });
    }
  }

  private async syncIssueCategories(
    newIssues: Issue[],
    existingIssuesMap: Map<string, any>
  ): Promise<void> {
    const logger = log.child({
      context: 'syncIssueCategories'
    });
    try {
      const categories = new Set<string>();

      // Process all existing issues
      for (const [_, doc] of existingIssuesMap) {
        const existingIssue = doc.data() as Issue;
        if (existingIssue.category) {
          categories.add(existingIssue.category);
        }
      }

      // Process all new issues
      for (const issue of newIssues) {
        categories.add(issue.category);
      }

      // Sync the categories list to Firestore
      const summaryDocRef = this.collections.issueCategories.doc('summary');
      this.bulkWriter.set(summaryDocRef, {
        categories: Array.from(categories)
      });

      log.info('Issue categories summary updated', {
        context: 'syncIssueCategories',
        categoriesCount: categories.size
      });
    } catch (error) {
      logger.error('Error syncing issue categories', { error });
      throw error;
    }
  }

  private async syncIssues(newIssues: Issue[]): Promise<void> {
    const existingIssuesSnapshot = await this.collections.issues.get();
    const existingIssuesMap = new Map<string, any>();

    existingIssuesSnapshot.forEach((doc) => {
      existingIssuesMap.set(doc.id, doc);
    });

    await this.syncIssueCategories(newIssues, existingIssuesMap);

    for (const issue of newIssues) {
      const issueId = hashIssue(issue);
      const issueDocRef = this.collections.issues.doc(issueId);
      const existingIssueDoc = existingIssuesMap.get(issueId);

      if (!existingIssueDoc) {
        issue.createdTime = new Date();
        issue.updatedTime = new Date();

        // Only secrurity group issues have type
        if (Object.prototype.hasOwnProperty.call(issue, 'type')) {
          const securityGroupIssue = issue as unknown as SecurityGroupIssue;

          const { trafficPatterns, ...issueWithoutTrafficPatterns } =
            securityGroupIssue;

          this.bulkWriter.set(issueDocRef, {
            ...issueWithoutTrafficPatterns,
            trafficPatterns: []
          });

          const relatedTrafficCollection =
            issueDocRef.collection('relatedTraffic');

          Object.entries(trafficPatterns).forEach(
            ([entityId, trafficPatterns]) => {
              const entityDocRef = relatedTrafficCollection.doc(entityId);

              // enforcing limit of max traffic patterns per entity
              const limitedTrafficPatterns =
                trafficPatterns.length > MAX_TRAFFIC_PATTERNS_PER_ENTITY
                  ? trafficPatterns.slice(0, MAX_TRAFFIC_PATTERNS_PER_ENTITY)
                  : trafficPatterns;

              this.bulkWriter.set(entityDocRef, {
                trafficPatterns: limitedTrafficPatterns
              });
            }
          );
        } else {
          this.bulkWriter.set(issueDocRef, issue);
        }
      } else {
        const existingData = existingIssueDoc.data();
        const isSecurityGroupIssue = Object.prototype.hasOwnProperty.call(
          issue,
          'type'
        );
        const hasChanged = isAwsSecurityGroupIssueHasChanged(
          existingData,
          issue
        );

        if (isSecurityGroupIssue && hasChanged) {
          const securityGroupIssue = issue as unknown as SecurityGroupIssue;
          const { trafficPatterns, ...issueWithoutTrafficPatterns } =
            securityGroupIssue;

          issue.updatedTime = new Date();

          this.bulkWriter.update(issueDocRef, {
            ...issueWithoutTrafficPatterns,
            updatedTime: issue.updatedTime
          });

          const relatedTrafficCollection =
            issueDocRef.collection('relatedTraffic');

          Object.entries(trafficPatterns).forEach(
            ([entityId, trafficPatterns]) => {
              const entityDocRef = relatedTrafficCollection.doc(entityId);
              const limitedTrafficPatterns =
                trafficPatterns.length > MAX_TRAFFIC_PATTERNS_PER_ENTITY
                  ? trafficPatterns.slice(0, MAX_TRAFFIC_PATTERNS_PER_ENTITY)
                  : trafficPatterns;

              this.bulkWriter.set(entityDocRef, {
                trafficPatterns: limitedTrafficPatterns
              });
            }
          );
        }

        // non security group issues
        existingIssuesMap.delete(issueId);
      }
    }
  }

  public async invalidateIssues(): Promise<void> {
    log.info(`Invalidating issues`, {
      context: 'invalidateIssues'
    });

    try {
      const existingIssuesMap = await getOpenVertexIssues(
        this.collections.issues
      );

      for (const [id, issue] of existingIssuesMap) {
        const issueDoc = issue.data();

        const entityTrafficPatterns = await getEntityTrafficPatterns(
          this.collections.trafficPatterns,
          issueDoc.entityId
        );

        if (isIssueValid(issueDoc, entityTrafficPatterns)) continue;

        log.info(`Dismissing invalid issue ${id}`, {
          context: 'invalidateIssues'
        });

        this.bulkWriter.update(issue.ref, {
          status: ISSUE_STATUS.DISMISSED
        });
      }

      log.info(`Finished invalidating issues successfully`);
    } catch (error: any) {
      log.error(`Error invalidating issues: ${error.name}`);
      throw new Error(`Error invalidating issues: ${error.name}`);
    }
  }

  async close(): Promise<void> {
    await this.bulkWriter.close();
  }

  public static async setBenchmark(
    benchmark: Benchmark,
    organizationId: string
  ): Promise<void> {
    const benchmarkManager = new BenchmarkFirestoreWriter(
      benchmark,
      organizationId
    );

    log.info(`Starting benchmark data sync`, {
      context: 'setBenchmark'
    });

    await benchmarkManager.syncBenchmark();

    // Commit all bulkWriter operations
    log.info(`Commiting benchmark data to firestore`, {
      context: 'setBenchmark'
    });

    await benchmarkManager.close();
    await BenchmarkFirestoreWriter.logBenchmarkCount(organizationId);
  }

  public static async logBenchmarkCount(organizationId: string): Promise<void> {
    const collections = this.getBenchmarkCollections(organizationId);

    const [
      issuesCount,
      notificationsCount,
      entitiesCount,
      enrichedEntitiesCount,
      trafficPatternsCount,
      statsCount,
      labelsCount,
      labelAssignmentsCount,
      detectionsCount,
      detectionControlMetricsCount,
      securityGroupsCount
    ] = await Promise.all([
      collections.issues.count().get(),
      collections.notifications.count().get(),
      collections.entities.count().get(),
      collections.enrichedEntities.count().get(),
      collections.trafficPatterns.count().get(),
      collections.stats.count().get(),
      collections.labels.count().get(),
      collections.labelAssignments.count().get(),
      collections.detections.count().get(),
      collections.detectionControlMetrics.count().get(),
      collections.securityGroups.count().get()
    ]);

    log.info(
      `Benchmark data set successfully: 
          entities: ${entitiesCount.data().count}
          enrichedEntities: ${enrichedEntitiesCount.data().count}
          issues: ${issuesCount.data().count}
          traffic patterns: ${trafficPatternsCount.data().count}
          notifications: ${notificationsCount.data().count}
          stats: ${statsCount.data().count}
          labels: ${labelsCount.data().count}
          label assignments: ${labelAssignmentsCount.data().count}
          detections: ${detectionsCount.data().count}
          detection control metrics: ${detectionControlMetricsCount.data().count}
          security groups: ${securityGroupsCount.data().count}`,
      {
        context: 'logBenchmarkCount',
        entities: entitiesCount.data().count,
        enrichedEntities: enrichedEntitiesCount.data().count,
        issues: issuesCount.data().count,
        trafficPatterns: trafficPatternsCount.data().count,
        notifications: notificationsCount.data().count,
        stats: statsCount.data().count,
        labels: labelsCount.data().count,
        labelAssignments: labelAssignmentsCount.data().count,
        detections: detectionsCount.data().count,
        detectionControlMetrics: detectionControlMetricsCount.data().count,
        securityGroups: securityGroupsCount.data().count
      }
    );
  }

  private static getBenchmarkCollections(
    organizationId: string
  ): BenchmarkCollections {
    const firestore = getFirestore();
    const benchmarkRef = firestore.doc(
      `organizations/${organizationId}/dashboards/NDR`
    );

    return {
      entities: benchmarkRef.collection('entities'),
      enrichedEntities: benchmarkRef.collection('enrichedEntities'),
      trafficPatterns: benchmarkRef.collection('trafficPatterns'),
      issues: benchmarkRef.collection('issues'),
      notifications: benchmarkRef.collection('notifications'),
      stats: benchmarkRef.collection('stats'),
      labels: benchmarkRef.collection('labels'),
      labelAssignments: benchmarkRef.collection('labelAssignments'),
      detections: benchmarkRef.collection('detections'),
      detectionControlMetrics: benchmarkRef.collection(
        'detectionControlMetrics'
      ),
      securityGroups: benchmarkRef.collection('securityGroups'),
      detectionCategories: benchmarkRef.collection('detectionCategories'),
      issueCategories: benchmarkRef.collection('issueCategories')
    };
  }
}
