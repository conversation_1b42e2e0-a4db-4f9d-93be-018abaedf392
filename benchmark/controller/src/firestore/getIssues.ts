import getFirestore from './getFirestore';
import {
  Issue,
  ISSUE_STATUS
} from '../globalTypes/ndrBenchmarkTypes/issueTypes';
import { CollectionReference } from 'firebase-admin/firestore';
import log from '../logger';
/**
 * Fetches all issues for a given organization from Firestore.
 * @param organizationId - The ID of the organization.
 * @returns A promise that resolves to an array of Issue objects.
 */
export default async function getIssues(
  organizationId: string
): Promise<Issue[]> {
  const firestore = getFirestore();
  const issues: Issue[] = [];

  try {
    // Reference to the issues collection
    const issuesCollectionRef = firestore.collection(
      `organizations/${organizationId}/dashboards/NDR/issues`
    );

    // Fetch issues from Firestore
    const snapshot = await issuesCollectionRef.get();

    // Convert the Firestore snapshot to an array of Issue objects
    snapshot.forEach((doc) => {
      const issue = doc.data() as Issue;
      // Fix the issue.createdTime and issue.updatedTime to be a Date object
      issue.createdTime = doc.data().createdTime?.toDate();
      issue.updatedTime = doc.data().updatedTime?.toDate();
      issues.push(issue);
    });

    return issues;
  } catch (error) {
    throw new Error(
      `Error fetching issues for org ${organizationId}: ${error}`
    );
  }
}

export async function getOpenVertexIssues(
  issuesCollectionRef: CollectionReference
): Promise<Map<string, any>> {
  try {
    const allExistingIssuesSnapshot = await issuesCollectionRef
      .where('status', '==', ISSUE_STATUS.OPEN)
      .get();

    const existingIssuesMap = new Map<string, any>();
    allExistingIssuesSnapshot.forEach((doc) => {
      existingIssuesMap.set(doc.id, doc);
    });

    return existingIssuesMap;
  } catch (error: any) {
    log.error(`Error getting existing issues: ${error.name}`);
    throw new Error(`Error getting existing issues: ${error.name}`);
  }
}
