import { CollectionReference } from 'firebase-admin/firestore';
import { TrafficPattern } from '../globalTypes/ndrBenchmarkTypes/trafficPatternTypes';
import log from '../logger';

export async function getEntityTrafficPatterns(
  trafficPatternsCollectionRef: CollectionReference,
  entityId: string
): Promise<TrafficPattern[]> {
  try {
    const entityTrafficPatternsSnapshot = await trafficPatternsCollectionRef
      .where('entityId', '==', entityId)
      .get();

    const trafficPatterns = entityTrafficPatternsSnapshot.docs.map(
      (doc) => doc.data() as TrafficPattern
    );
    return trafficPatterns;
  } catch (error: any) {
    log.error(`Error getting entity traffic patterns: ${error.name}`);
    throw new Error(`Error getting entity traffic patterns: ${error.name}`);
  }
}
