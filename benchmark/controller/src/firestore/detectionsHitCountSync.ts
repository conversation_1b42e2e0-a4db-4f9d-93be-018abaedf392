import { Detection } from '@globalTypes/ndrBenchmarkTypes';
import { getFirestore } from 'firebase-admin/firestore';
import log from '../logger';
import { arrayToMap, buildNDRCollectionPath } from '../utils/detectionUtils';
import { getDetectionControls, getDetectionMetrics } from './getDetections';

/**
 * Syncs a single detection metric by comparing its hitCount with the actual count of detections.
 * @param organizationId - The ID of the organization.
 * @param params - Object containing either metric or controlId, and logger.
 * @param logger - The logger instance to use for logging.
 */
async function syncMetric({
  organizationId,
  metricId,
  hitCount,
  logger
}: {
  organizationId: string;
  metricId: string;
  hitCount?: number;
  logger: any;
}): Promise<void> {
  try {
    const currentCount = await countDetectionsByControlId(
      organizationId,
      metricId
    );

    // If param includes 'metric', compare and update if needed
    if (!hitCount || hitCount !== currentCount) {
      logger.debug(
        `Updating metric ${metricId}: hitCount ${hitCount} -> ${currentCount}`
      );
      await updateDetectionMetricHitCount(
        organizationId,
        metricId,
        currentCount
      );
    } else {
      logger.debug(`Metric ${metricId} is up to date`);
    }
  } catch (error) {
    logger.error('Error processing detection metric', {
      error,
      organizationId,
      metricId
    });
  }
}

export async function syncDetectionsHitCountsForOrganization(
  organizationId: string
) {
  const logger = log.child({
    context: 'syncDetectionsHitCountsForOrganization',
    organizationId
  });

  try {
    logger.debug(`Syncing detections hit counts`);

    const metrics = await getDetectionMetrics(organizationId);
    const metricsMap = arrayToMap(metrics);

    const existingDetectionControls =
      await getDetectionControls(organizationId);

    await Promise.all([
      ...existingDetectionControls.map(async (control) => {
        await syncMetric({
          organizationId,
          metricId: control.id,
          hitCount: metricsMap[control.id]?.hitCount,
          logger
        });
      })
    ]);
  } catch (error) {
    logger.error(`Error syncing detections hit counts`, {
      error,
      organizationId
    });
  }
}

/**
 * Fetches all detections for a given organization and controlId from Firestore.
 * @param organizationId - The ID of the organization.
 * @param controlId - The controlId to filter detections by.
 * @returns A promise that resolves to an array of Detection objects.
 */
export async function getDetectionsByControlId(
  organizationId: string,
  controlId: string
): Promise<Detection[]> {
  const firestore = getFirestore();
  const detections: Detection[] = [];

  try {
    // Reference to the detections collection
    const detectionsCollectionRef = firestore.collection(
      buildNDRCollectionPath(organizationId, 'detections')
    );

    // Fetch detections with the specified controlId from Firestore
    const snapshot = await detectionsCollectionRef
      .where('controlId', '==', controlId)
      .get();

    snapshot.forEach((doc) => {
      const detection = doc.data() as Detection;
      detections.push(detection);
    });

    return detections;
  } catch (error) {
    throw new Error(
      `Error fetching detections for org ${organizationId} and controlId ${controlId}: ${error}`
    );
  }
}

/**
 * Fetches all organization IDs from Firestore.
 * @returns A promise that resolves to an array of organization IDs.
 */
export async function getAllOrganizationIds(): Promise<string[]> {
  const firestore = getFirestore();
  const snapshot = await firestore.collection('organizations').get();

  return snapshot.docs.map((doc) => doc.id);
}

/**
 * Counts all detections for a given organization and controlId using Firestore aggregation (fast, no doc fetch).
 * @param organizationId - The ID of the organization.
 * @param controlId - The controlId to filter detections by.
 * @returns A promise that resolves to the count of Detection objects.
 */
export async function countDetectionsByControlId(
  organizationId: string,
  controlId: string
): Promise<number> {
  const firestore = getFirestore();
  const detectionsCollectionRef = firestore.collection(
    buildNDRCollectionPath(organizationId, 'detections')
  );
  const countQuery = detectionsCollectionRef
    .where('controlId', '==', controlId)
    .count();
  const snapshot = await countQuery.get();
  return snapshot.data().count;
}

/**
 * Updates the hitCount of a detectionControlMetric document.
 * @param organizationId - The ID of the organization.
 * @param metricId - The id of the metric (document id).
 * @param newHitCount - The new hitCount value.
 */
export async function updateDetectionMetricHitCount(
  organizationId: string,
  metricId: string,
  newHitCount: number
): Promise<void> {
  const firestore = getFirestore();

  const metricRef = firestore
    .collection(
      buildNDRCollectionPath(organizationId, 'detectionControlMetrics')
    )
    .doc(metricId);

  await metricRef.set({ hitCount: newHitCount });
}

async function syncDetectionsHitCountsForAllOrganizations() {
  const logger = log.child({
    context: 'syncDetectionsHitCountsForAllOrganizations'
  });
  const orgIds = await getAllOrganizationIds();

  logger.debug(`Found ${orgIds.length} organizations.`);

  for (const organizationId of orgIds) {
    await syncDetectionsHitCountsForOrganization(organizationId);
  }

  logger.debug('Done syncing hitCounts for all organizations.');
}
