import * as lodash from 'lodash';
import getFirestore from './getFirestore';
import { Notification } from '../globalTypes/ndrBenchmarkTypes/notificationTypes';
import log from '../logger';
import { CombineNotificationsFunction } from '../benchmark/controls/types';
import { hash } from '../utils/issueUtils';

/**
 * Fetches all notifications for a given organization from Firestore.
 * @param organizationId - The ID of the organization.
 * @returns A promise that resolves to an array of Notification objects.
 */
export async function getNotifications(
  organizationId: string
): Promise<Notification[]> {
  const firestore = getFirestore();
  const notifications: Notification[] = [];

  try {
    // Reference to the notifications collection
    const notificationsCollectionRef = firestore.collection(
      `organizations/${organizationId}/dashboards/NDR/notifications`
    );

    // Fetch notifications from Firestore
    const snapshot = await notificationsCollectionRef.get();

    // Convert the Firestore snapshot to an array of Notification objects
    snapshot.forEach((doc) => {
      const notification = doc.data() as Notification;
      notifications.push(notification);
    });

    log.info(`Successfully fetched notification for ${organizationId}`);
    return notifications;
  } catch (error) {
    log.error(
      `Error fetching notifications for org ${organizationId}: ${error}`
    );
    throw error; // Rethrow error for upstream handling
  }
}

/**
 * Combines notifications from two sources into a single array.
 * @param notifications - First array of notifications.
 * @param fireStoreNotifications - Second array of notifications from Firestore.
 * @returns Combined array of notifications.
 */
export const combineNotifications: CombineNotificationsFunction = (params) => {
  const { benchmarkNotifications, fireStoreNotifications } = params;
  const getNotificationKey = (notification: Notification) => {
    return hash(notification);
  };

  return lodash.unionBy(
    benchmarkNotifications,
    fireStoreNotifications,
    getNotificationKey
  );
};
