// Add mock for logger at the top of the file
jest.mock('../../logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }
}));

import { Issue } from '../../globalTypes/ndrBenchmarkTypes/issueTypes';
import {
  TrafficPattern,
  UniqueTraffic
} from '../../globalTypes/ndrBenchmarkTypes/trafficPatternTypes';
import {
  DIRECTION,
  FIREWALL_RULE_POLICY
} from '../../globalTypes/FirewallTypes';
import { DIRECTIONS } from '../../globalTypes/ndrBenchmarkTypes/trafficPatternTypes';
import { ISSUE_STATUS } from '../../globalTypes/ndrBenchmarkTypes/issueTypes';

// Import the functions to test - you'll need to export them from benchmark.ts
import { isPatternContained } from '../../utils/trafficPatternMatcher';
import { isIssueValid } from '../../utils/issueUtils';
import { BenchmarkFirestoreWriter } from '../benchmark';
import { Benchmark } from '../../benchmark/types/benchmarkTypes';
import { ISSUE_CATEGORIES } from '../../globalTypes/ndrBenchmarkTypes/issueTypes';

// Add this mock before the BenchmarkManager tests
jest.mock('../benchmark', () => {
  const originalModule = jest.requireActual('../benchmark');
  return {
    ...originalModule,
    getOpenVertexIssues: jest.fn()
  };
});

// Mock getFirestore at the top with other mocks
jest.mock('../getFirestore', () => ({
  __esModule: true,
  default: jest.fn()
}));

describe('arePatternsMatched', () => {
  const pattern: Partial<TrafficPattern> = {
    id: '1',
    entityId: 'entity1',
    direction: DIRECTIONS.INGRESS,
    port: '80',
    remoteAddr: '*******',
    sessionCount: 1,
    time: new Date()
  };

  it('should match single port pattern', () => {
    const matchPatterns = [
      {
        direction: DIRECTION.INBOUND,
        port: ['80']
      }
    ];

    expect(isPatternContained(pattern as TrafficPattern, matchPatterns)).toBe(
      true
    );
  });

  it('should match port range pattern', () => {
    const matchPatterns = [
      {
        direction: DIRECTION.INBOUND,
        port: ['70-90']
      }
    ];

    expect(isPatternContained(pattern as TrafficPattern, matchPatterns)).toBe(
      true
    );
  });

  it('should not match wrong direction', () => {
    const matchPatterns = [
      {
        direction: DIRECTION.OUTBOUND,
        port: ['80']
      }
    ];

    expect(isPatternContained(pattern as TrafficPattern, matchPatterns)).toBe(
      false
    );
  });

  it('should not match wrong port', () => {
    const matchPatterns = [
      {
        direction: DIRECTION.INBOUND,
        port: ['443']
      }
    ];

    expect(isPatternContained(pattern as TrafficPattern, matchPatterns)).toBe(
      false
    );
  });
});

describe('isIssueValid', () => {
  const baseIssue: Issue = {
    entityId: 'entity1',
    category: 'test',
    title: 'Test Issue',
    subTitle: 'Test Subtitle',
    explanation: 'Test Explanation',
    severity: 'medium',
    relatedEntitiesIds: [],
    trafficPatterns: [],
    status: ISSUE_STATUS.OPEN
  };

  const trafficPattern: Partial<TrafficPattern> = {
    id: '1',
    entityId: 'entity1',
    direction: DIRECTIONS.INGRESS,
    port: '80',
    remoteAddr: '*******',
    sessionCount: 1,
    time: new Date()
  };

  it('should return true for issue with no traffic patterns', () => {
    const issue = { ...baseIssue };
    expect(isIssueValid(issue, [])).toBe(true);
  });

  it('should return true when all patterns match and are included', () => {
    const issue: Partial<Issue> = {
      ...baseIssue,
      trafficPatterns: [trafficPattern as unknown as UniqueTraffic],
      remediation: {
        type: 'firewall',
        description: 'test',
        data: {
          entity1: [
            {
              direction: DIRECTION.INBOUND,
              portRanges: ['80'],
              policy: FIREWALL_RULE_POLICY.ACCEPT,
              remoteIdentifiers: [],
              alert: false,
              notes: '',
              localIdentifier: {
                type: 'local IP',
                value: '*******'
              }
            }
          ]
        }
      }
    };

    expect(
      isIssueValid(issue as Issue, [trafficPattern as TrafficPattern])
    ).toBe(true);
  });

  it('should return false when matching pattern is not included in issue', () => {
    const issue: Partial<Issue> = {
      ...baseIssue,
      trafficPatterns: [
        { ...trafficPattern, id: '2', port: '81' } as unknown as UniqueTraffic
      ],
      remediation: {
        type: 'firewall',
        description: 'test',
        data: {
          entity1: [
            {
              direction: DIRECTION.INBOUND,
              portRanges: ['80'],
              policy: FIREWALL_RULE_POLICY.ACCEPT,
              remoteIdentifiers: [],
              alert: false,
              notes: '',
              localIdentifier: {
                type: 'local IP',
                value: '*******'
              }
            }
          ]
        }
      }
    };

    expect(
      isIssueValid(issue as Issue, [trafficPattern as TrafficPattern])
    ).toBe(false);
  });
});

describe('BenchmarkManager', () => {
  // Mock traffic pattern
  const mockTrafficPatternObj = {
    id: '1',
    entityId: 'entity1',
    direction: DIRECTIONS.INGRESS,
    port: '80',
    remoteAddr: '*******',
    sessionCount: 1,
    time: new Date()
  };

  // Mock traffic pattern that should invalidate the issue
  const mockTrafficPattern = {
    data: () => mockTrafficPatternObj
  };

  const mockBulkWriter = {
    update: jest.fn(),
    set: jest.fn(),
    delete: jest.fn(),
    close: jest.fn(),
    onWriteError: jest.fn()
  };

  const mockCollections = {
    issues: {
      where: jest.fn().mockReturnThis(),
      get: jest.fn(),
      doc: jest.fn()
    },
    trafficPatterns: {
      where: jest.fn().mockReturnThis(),
      get: jest.fn(),
      doc: jest.fn()
    }
  };

  const mockBenchmarkRef = {
    collection: jest.fn(
      (name: string) => mockCollections[name as keyof typeof mockCollections]
    )
  };

  const mockFirestore = {
    bulkWriter: jest.fn().mockReturnValue(mockBulkWriter),
    doc: jest.fn().mockImplementation((path: string) => {
      if (path.includes('NDR')) {
        return mockBenchmarkRef;
      }
      return {
        collection: jest.fn()
      };
    })
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset all mock implementations
    mockCollections.issues.where.mockReturnThis();
    mockCollections.trafficPatterns.where.mockReturnThis();

    // Setup getFirestore mock
    const getFirestore = require('../getFirestore').default;
    getFirestore.mockReturnValue(mockFirestore);
  });

  describe('invalidateIssues', () => {
    it('should dismiss invalid issue when new traffic pattern is added', async () => {
      // Mock the issues collection query chain
      mockCollections.issues.where.mockImplementation(
        (field: string, operator: string, value: any) => {
          if (field === 'category' && value === ISSUE_CATEGORIES.VERTEX_ISSUE) {
            return mockCollections.issues;
          }
          if (field === 'status' && value === ISSUE_STATUS.OPEN) {
            return mockCollections.issues;
          }
          return mockCollections.issues;
        }
      );

      // Mock issue that will be invalid
      const mockIssue = {
        id: 'issue1',
        ref: { id: 'issue1' },
        data: () => ({
          entityId: 'entity1',
          category: ISSUE_CATEGORIES.VERTEX_ISSUE,
          status: ISSUE_STATUS.OPEN,
          trafficPatterns: [
            { ...mockTrafficPatternObj, port: '81' } as TrafficPattern
          ],
          remediation: {
            data: {
              entity1: [
                {
                  direction: DIRECTION.INBOUND,
                  portRanges: ['80'],
                  policy: FIREWALL_RULE_POLICY.ACCEPT
                }
              ]
            }
          }
        })
      };

      mockCollections.trafficPatterns.get.mockResolvedValue({
        docs: [mockTrafficPattern]
      });

      mockCollections.issues.get.mockResolvedValue([mockIssue]);

      const benchmark = new BenchmarkFirestoreWriter(
        {} as Benchmark,
        'testOrgId'
      );
      await benchmark.invalidateIssues();

      expect(mockBulkWriter.update).toHaveBeenCalledWith(mockIssue.ref, {
        status: ISSUE_STATUS.DISMISSED
      });
    });

    it('should not dismiss valid issues', async () => {
      // Mock the issues collection query chain
      mockCollections.issues.where.mockImplementation(
        (field: string, operator: string, value: any) => {
          if (field === 'category' && value === ISSUE_CATEGORIES.VERTEX_ISSUE) {
            return mockCollections.issues;
          }
          if (field === 'status' && value === ISSUE_STATUS.OPEN) {
            return mockCollections.issues;
          }
          return mockCollections.issues;
        }
      );

      // Mock valid issue
      const mockIssue = {
        id: 'issue1',
        ref: { id: 'issue1' },
        data: () => ({
          entityId: 'entity1',
          category: ISSUE_CATEGORIES.VERTEX_ISSUE,
          status: ISSUE_STATUS.OPEN,
          trafficPatterns: [mockTrafficPatternObj],
          remediation: {
            data: {
              entity1: [
                {
                  direction: DIRECTION.INBOUND,
                  portRanges: ['80'],
                  policy: FIREWALL_RULE_POLICY.ACCEPT
                }
              ]
            }
          }
        })
      };

      mockCollections.trafficPatterns.get.mockResolvedValue({
        docs: [mockTrafficPattern]
      });

      mockCollections.issues.get.mockResolvedValue([mockIssue]);

      const benchmark = new BenchmarkFirestoreWriter(
        {} as Benchmark,
        'testOrgId'
      );
      await benchmark.invalidateIssues();

      expect(mockBulkWriter.update).not.toHaveBeenCalled();
    });
  });
});
