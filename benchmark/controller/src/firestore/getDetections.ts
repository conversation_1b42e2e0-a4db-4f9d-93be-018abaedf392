import {
  DetectionControl,
  DetectionControlMetric,
  ORIGIN_TYPES
} from '@globalTypes/ndrBenchmarkTypes/detectionTypes';
import getFirestore from './getFirestore';
import { buildNDRCollectionPath } from '../utils/detectionUtils';

/**
 * Fetches all active detection controls for a given organization from Firestore.
 * @param organizationId - The ID of the organization.
 * @returns A promise that resolves to an array of Detection objects.
 */
export async function getActiveDetectionControls(
  organizationId: string
): Promise<DetectionControl[]> {
  const firestore = getFirestore();
  const activeDetectionControls: DetectionControl[] = [];

  try {
    // Reference to the detection controls collection
    const detectionControlsCollectionRef = firestore.collection(
      buildNDRCollectionPath(organizationId, 'detectionControls')
    );

    // Fetch detection controls from Firestore
    const snapshot = await detectionControlsCollectionRef
      .where('active', '==', true)
      .get();

    const now = new Date();
    // Map the Firestore snapshot to an array of active Detection control objects that are due to run
    snapshot.forEach((doc) => {
      const control = doc.data() as DetectionControl;
      const intervalMs = (control.interval ?? 10) * 60 * 1000;
      const lastRunAtDate = control.lastRunAt
        ? new Date((control.lastRunAt as any).seconds * 1000)
        : undefined;

      if (
        !lastRunAtDate ||
        now.getTime() - lastRunAtDate.getTime() >= intervalMs
      ) {
        control.interval ??= 10;
        activeDetectionControls.push(control);
      }
    });

    return activeDetectionControls;
  } catch (error) {
    throw new Error(
      `Error fetching detection controls for org ${organizationId}: ${error}`
    );
  }
}

/**
 * Fetches all detection controls for a given organization from Firestore.
 * @param organizationId - The ID of the organization.
 * @returns A promise that resolves to an array of DetectionControl objects.
 */
export async function getPort0DetectionControls(
  organizationId: string
): Promise<DetectionControl[]> {
  const firestore = getFirestore();

  try {
    const detectionControlsRef = firestore.collection(
      buildNDRCollectionPath(organizationId, 'detectionControls')
    );

    const snapshot = await detectionControlsRef
      .where('origin', '==', ORIGIN_TYPES.PORT0)
      .get();

    return snapshot.docs.map((doc) => doc.data() as DetectionControl);
  } catch (error) {
    throw new Error(
      `Error fetching detection controls for org ${organizationId}: ${error}`
    );
  }
}

/**
 * Fetches all detection metrics for a given organization from Firestore.
 * @param organizationId - The ID of the organization.
 * @returns A promise that resolves to an array of DetectionMetric objects.
 */
export async function getDetectionMetrics(
  organizationId: string
): Promise<DetectionControlMetric[]> {
  const firestore = getFirestore();

  try {
    const detectionMetricsRef = firestore.collection(
      buildNDRCollectionPath(organizationId, 'detectionControlMetrics')
    );

    const snapshot = await detectionMetricsRef.get();

    return snapshot.docs.map(
      (doc) =>
        ({
          ...doc.data(),
          id: doc.id
        }) as DetectionControlMetric
    );
  } catch (error) {
    throw new Error(
      `Error fetching detection metrics for org ${organizationId}: ${error}`
    );
  }
}

/**
 * Fetches all detection controls for a given organization from Firestore.
 * @param organizationId - The ID of the organization.
 * @returns A promise that resolves to an array of DetectionControl objects.
 */
export async function getDetectionControls(
  organizationId: string
): Promise<DetectionControl[]> {
  const firestore = getFirestore();

  try {
    const detectionControlsRef = firestore.collection(
      buildNDRCollectionPath(organizationId, 'detectionControls')
    );

    const snapshot = await detectionControlsRef.get();

    return snapshot.docs.map((doc) => doc.data() as DetectionControl);
  } catch (error) {
    throw new Error(
      `Error fetching detection controls for org ${organizationId}: ${error}`
    );
  }
}

/**
 * Updates the detection categories for a given organization in Firestore.
 * @param organizationId - The ID of the organization.
 * @param categories - Array of category strings to be stored.
 * @returns A promise that resolves when the categories are updated.
 */
export async function updateDetectionCategories(
  organizationId: string,
  categories: string[]
): Promise<void> {
  const firestore = getFirestore();

  try {
    const detectionCategoriesRef = firestore.doc(
      buildNDRCollectionPath(organizationId, 'detectionCategories/summary')
    );

    await detectionCategoriesRef.set({
      categories
    });
  } catch (error) {
    throw new Error(
      `Error updating detection categories for org ${organizationId}: ${error}`
    );
  }
}

/**
 * Checks if a specific detection control is active for a given organization.
 * @param organizationId - The ID of the organization.
 * @param controlId - The ID of the detection control to check.
 * @returns A promise that resolves to true if the control is active, false otherwise.
 */
export async function isDetectionControlActive(
  organizationId: string,
  controlId: string
): Promise<boolean> {
  const firestore = getFirestore();

  try {
    const detectionControlRef = firestore.doc(
      buildNDRCollectionPath(organizationId, `detectionControls/${controlId}`)
    );

    const doc = await detectionControlRef.get();

    if (!doc.exists) {
      return false;
    }

    const control = doc.data() as DetectionControl;
    return control.active === true;
  } catch (error) {
    throw new Error(
      `Error checking detection control status for org ${organizationId}, control ${controlId}: ${error}`
    );
  }
}
export async function getDetectionCategories(
  organizationId: string
): Promise<string[]> {
  const firestore = getFirestore();

  try {
    const detectionCategoriesRef = firestore.doc(
      buildNDRCollectionPath(organizationId, 'detectionCategories/summary')
    );

    const doc = await detectionCategoriesRef.get();
    const data = doc.data();

    const categories = data?.categories ?? Object.keys(data ?? {});

    return categories ?? [];
  } catch (error) {
    throw new Error(
      `Error fetching detection categories for org ${organizationId}: ${error}`
    );
  }
}
