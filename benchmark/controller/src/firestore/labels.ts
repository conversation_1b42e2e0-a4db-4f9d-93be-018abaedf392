import { Label, LabelAssignment } from '@globalTypes/ndrBenchmarkTypes';
import getFirestore from './getFirestore';
import log from '../logger';

export async function getLabels(organizationId: string): Promise<Label[]> {
  const firestore = getFirestore();
  const labels: Label[] = [];

  try {
    const labelsCollectionRef = firestore.collection(
      `organizations/${organizationId}/dashboards/NDR/labels`
    );

    const snapshot = await labelsCollectionRef.get();

    snapshot.forEach((doc) => {
      const label = doc.data() as Label;
      labels.push(label);
    });

    log.info(
      `Successfully fetched labels ${labels.length} for ${organizationId}`
    );
    return labels;
  } catch (error) {
    log.error(`Error fetching labels for org ${organizationId}: ${error}`);
    throw error;
  }
}

export async function getLabelAssignments(
  organizationId: string
): Promise<LabelAssignment[]> {
  const firestore = getFirestore();
  const labelAssignments: LabelAssignment[] = [];

  try {
    const labelAssignmentsCollectionRef = firestore.collection(
      `organizations/${organizationId}/dashboards/NDR/labelAssignments`
    );

    const snapshot = await labelAssignmentsCollectionRef.get();

    snapshot.forEach((doc) => {
      const labelAssignment = doc.data() as LabelAssignment;
      labelAssignments.push(labelAssignment);
    });

    log.info(
      `Successfully fetched labelAssignments ${labelAssignments.length} for ${organizationId}`
    );
    return labelAssignments;
  } catch (error) {
    log.error(`Error fetching labels for org ${organizationId}: ${error}`);
    throw error;
  }
}
