import getFirestore from '../firestore/getFirestore';
import { DetectionControl } from '@globalTypes/ndrBenchmarkTypes/detectionTypes';


interface DetectionCount {
 controlId: string;
 controlName: string;
 count: number;
 organizationId: string;
}


interface OrganizationDetectionSummary {
 organizationId: string;
 totalDetections: number;
 detectionCounts: DetectionCount[];
 over40kControls: DetectionCount[];
}


async function getAllOrganizations(): Promise<string[]> {
 const db = getFirestore();
 const snapshot = await db.collection('organizations').get();
 return snapshot.docs.map((doc) => doc.id);
}


async function getDetectionControlsForOrganization(
 organizationId: string
): Promise<DetectionControl[]> {
 const db = getFirestore();
 const controlsCollection = db.collection(
   `organizations/${organizationId}/dashboards/NDR/detectionControls`
 );


 const snapshot = await controlsCollection.get();
 return snapshot.docs.map((doc) => doc.data() as DetectionControl);
}


async function countDetectionsByControlId(
 organizationId: string,
 controlId: string
): Promise<number> {
 const db = getFirestore();
 const detectionsCollection = db.collection(
   `organizations/${organizationId}/dashboards/NDR/detections`
 );


 const countQuery = detectionsCollection
   .where('controlId', '==', controlId)
   .count();


 const snapshot = await countQuery.get();
 return snapshot.data().count;
}


async function setControlActiveToFalse(
 organizationId: string,
 controlId: string
): Promise<void> {
 const db = getFirestore();
 const controlRef = db.doc(
   `organizations/${organizationId}/dashboards/NDR/detectionControls/${controlId}`
 );


 await controlRef.update({
   active: false,
   updatedAt: new Date()
 });
}


async function analyzeOrganizationDetections(
 organizationId: string
): Promise<OrganizationDetectionSummary> {
 try {
   // Get all detection controls for this organization
   const controls = await getDetectionControlsForOrganization(organizationId);


   if (controls.length === 0) {
     return {
       organizationId,
       totalDetections: 0,
       detectionCounts: [],
       over40kControls: []
     };
   }


   // Count detections for each control
   const detectionCounts: DetectionCount[] = [];
   let totalDetections = 0;


   for (const control of controls) {
     const count = await countDetectionsByControlId(
       organizationId,
       control.id
     );
     detectionCounts.push({
       controlId: control.id,
       controlName: control.name,
       count,
       organizationId
     });
     totalDetections += count;
   }


   // Filter controls with over 40k detections
   const over40kControls = detectionCounts.filter(
     (count) => count.count > 40000
   );


   return {
     organizationId,
     totalDetections,
     detectionCounts,
     over40kControls
   };
 } catch (error) {
   console.error(`❌ ${organizationId}: Error analyzing -`, error);
   return {
     organizationId,
     totalDetections: 0,
     detectionCounts: [],
     over40kControls: []
   };
 }
}


export const analyzeAndDeactivateOver40kControls = async (
 organizationIds?: string[]
) => {
 const orgs = organizationIds || (await getAllOrganizations());
 const startTime = Date.now();


 console.log(`🚀 Analyzing ${orgs.length} organizations...`);


 // Gather all organizations data asynchronously
 const allResults = await Promise.all(
   orgs.map((orgId) => analyzeOrganizationDetections(orgId))
 );


 // Collect all controls with over 40k detections
 const allOver40kControls: DetectionCount[] = [];
 allResults.forEach((result) => {
   allOver40kControls.push(...result.over40kControls);
 });


 const totalDuration = Math.round((Date.now() - startTime) / 1000);


 // Summary report
 console.log(`\n📊 ANALYSIS COMPLETE`);
 console.log(`⏱️  Total time: ${totalDuration}s`);
 console.log(`🏢 Organizations analyzed: ${orgs.length}`);
 console.log(
   `📈 Total detections across all orgs: ${allResults.reduce((sum, r) => sum + r.totalDetections, 0).toLocaleString()}`
 );


 if (allOver40kControls.length > 0) {
   console.log(
     `\n🚨 DETECTION CONTROLS WITH OVER 40K DETECTIONS (${allOver40kControls.length}):`
   );
   allOver40kControls
     .sort((a, b) => b.count - a.count)
     .forEach((control) => {
       console.log(
         `   • ${control.controlName} (${control.controlId}) in ${control.organizationId}: ${control.count.toLocaleString()} detections`
       );
     });


   console.log(`\n🔄 Deactivating ${allOver40kControls.length} controls...`);


   // Deactivate all controls with over 40k detections
   const deactivationPromises = allOver40kControls.map((control) =>
     setControlActiveToFalse(control.organizationId, control.controlId)
   );


   await Promise.all(deactivationPromises);


   console.log(
     `✅ Successfully deactivated ${allOver40kControls.length} controls!`
   );
 } else {
   console.log(`\n✅ No detection controls found with over 40k detections`);
 }


 return {
   allResults,
   over40kControls: allOver40kControls
 };
};


// Run the analysis and deactivation
const run = async () => {
 try {
   console.log('Starting detection analysis and deactivation...\n');
   await analyzeAndDeactivateOver40kControls();
   console.log('\n✅ Analysis and deactivation completed successfully!');
 } catch (error) {
   console.error('❌ Error running analysis:', error);
 }
};


// Execute if this file is run directly
if (require.main === module) {
 run();
}



