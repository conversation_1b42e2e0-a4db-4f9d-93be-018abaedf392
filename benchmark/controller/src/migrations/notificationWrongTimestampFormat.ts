import { CloudEntity } from '@globalTypes/ndrBenchmarkTypes';
import getFirestore from '../firestore/getFirestore';
import { FieldValue } from 'firebase-admin/firestore';

export const migrateNotificationWrongTimestampFormat = async () => {
  const db = getFirestore();

  try {
    const orgsSnapshot = await db.collection('organizations').get();

    for (const orgDoc of orgsSnapshot.docs) {
      const orgId = orgDoc.id;
      // const orgId = 'org_2xzt3V17HEyWVAOW8NNiGLqAc1R';
      const notificationsPath = `organizations/${orgId}/dashboards/NDR/notifications`;
      const notificationsCountSnapshot = await db
        .collection(notificationsPath)
        .count()
        .get();
      const notificationsCount = notificationsCountSnapshot.data().count;
      console.log(`→ ${orgId}: ${notificationsCount} notifications`);

      const notificationsSnapshot = await db
        .collection(notificationsPath)
        .orderBy('time', 'desc')
        .get();

      if (notificationsSnapshot.empty) {
        console.log(
          `→ ${orgId}: no notifications collection found or it's empty.`
        );
        continue;
      }

      let batch = db.batch();
      let batchCount = 0;

      for (const notificationsDoc of notificationsSnapshot.docs) {
        const data = notificationsDoc.data();
        const time = data.time;

        if (typeof time === 'object' && time !== null && 'value' in time) {
          batch.update(notificationsDoc.ref, { time: time.value });
          batchCount++;

          if (batchCount === 500) {
            await batch.commit();
            console.log(`Committed a batch of 500 updates for org ${orgId}.`);
            batch = db.batch();
            batchCount = 0;
          }
        }
      }

      if (batchCount > 0) {
        await batch.commit();
        console.log(
          `Committed final batch of ${batchCount} updates for org ${orgId}.`
        );
      }
    }

    console.log('✅ Migration complete.');
  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
};
