import getFirestore from '../firestore/getFirestore';
import {
  getEntitiesMetrics,
  EntityMetricsDocument
} from '../firestore/entityMetrics';

/**
 * Migration to copy entity metrics from the separate entityMetrics collection
 * into the entity.metrics property for all entities across all organizations.
 * This function:
 * 1. Fetches all entity metrics for each organization
 * 2. Fetches all entities for each organization
 * 3. Copies the metrics data into each entity's metrics property
 * 4. Handles entities that don't have metrics (sets metrics to null)
 *
 * @param targetOrgId - Optional organization ID to run migration for a single org only
 */
export const migrateEntityMetrics = async (targetOrgId?: string) => {
  const db = getFirestore();
  const startTime = Date.now();

  try {
    if (targetOrgId) {
      console.log(
        `🚀 Starting entity metrics migration for single organization: ${targetOrgId}`
      );
    } else {
      console.log(
        '🚀 Starting entity metrics migration for all organizations...'
      );
    }

    // Get organizations to process
    let orgIds: string[];

    if (targetOrgId) {
      // Verify the target organization exists
      const targetOrgDoc = await db
        .collection('organizations')
        .doc(targetOrgId)
        .get();
      if (!targetOrgDoc.exists) {
        console.log(`❌ Organization ${targetOrgId} not found.`);
        return;
      }
      orgIds = [targetOrgId];
    } else {
      // Get all organizations
      const orgsSnapshot = await db.collection('organizations').get();
      if (orgsSnapshot.empty) {
        console.log('ℹ️  No organizations found.');
        return;
      }
      orgIds = orgsSnapshot.docs.map((doc) => doc.id);
    }

    const totalOrgs = orgIds.length;
    console.log(
      `📋 Found ${totalOrgs} organization${totalOrgs > 1 ? 's' : ''} to process`
    );

    let totalEntitiesUpdated = 0;

    // Process each organization
    for (let i = 0; i < orgIds.length; i++) {
      const orgId = orgIds[i];

      console.log(
        `\n📂 [${i + 1}/${totalOrgs}] Processing organization: ${orgId}`
      );

      try {
        const orgStartTime = Date.now();

        // Get all entity metrics for this organization
        const entityMetrics = await getEntitiesMetrics(orgId);
        const entityIdsWithMetrics = Object.keys(entityMetrics);
        console.log(
          `   📊 Found metrics for ${entityIdsWithMetrics.length} entities`
        );

        // Get entities collection for this organization
        const entitiesCollectionRef = db.collection(
          `organizations/${orgId}/dashboards/NDR/entities`
        );

        // Fetch all entities
        const entitiesSnapshot = await entitiesCollectionRef.get();

        if (entitiesSnapshot.empty) {
          console.log(`   ⚠️  No entities found for organization ${orgId}`);
          continue;
        }

        console.log(`   📦 Found ${entitiesSnapshot.docs.length} entities`);

        // Use BulkWriter for batch updates
        const bulkWriter = db.bulkWriter();
        let updatedCount = 0;

        // Update each entity with its metrics
        entitiesSnapshot.docs.forEach((entityDoc) => {
          const entityId = entityDoc.id;
          const metrics = entityMetrics[entityId] || null;

          // Update the entity document with metrics
          bulkWriter.update(entityDoc.ref, {
            metrics: metrics
          });

          updatedCount++;
        });

        // Execute all updates
        await bulkWriter.close();

        const orgDuration = Math.round((Date.now() - orgStartTime) / 1000);
        totalEntitiesUpdated += updatedCount;

        console.log(
          `   ✅ Updated ${updatedCount} entities in ${orgDuration}s`
        );
        console.log(
          `   📈 Entities with metrics: ${entityIdsWithMetrics.length}, without metrics: ${updatedCount - entityIdsWithMetrics.length}`
        );
      } catch (orgError) {
        console.error(
          `   ❌ Error processing organization ${orgId}:`,
          orgError
        );
        // Continue with next organization instead of failing completely
      }
    }

    const totalDuration = Math.round((Date.now() - startTime) / 1000);
    console.log(`\n🎉 Entity metrics migration completed successfully!`);
    console.log(`📈 Total entities updated: ${totalEntitiesUpdated}`);
    console.log(`⏱️  Total time: ${totalDuration}s`);
  } catch (error) {
    console.error('❌ Entity metrics migration failed:', error);
    throw error;
  }
};

/**
 * Helper function to run the entity metrics migration for a single organization.
 * @param organizationId - The ID of the organization to migrate
 */
export const migrateEntityMetricsForOrg = async (organizationId: string) => {
  return migrateEntityMetrics(organizationId);
};
