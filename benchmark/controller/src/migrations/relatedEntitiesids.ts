import { CloudEntity } from '@globalTypes/ndrBenchmarkTypes';
import getFirestore from '../firestore/getFirestore';
import { FieldValue } from 'firebase-admin/firestore';

export const migrateRelatedEntitiesInsights = async () => {
  const db = getFirestore();

  try {
    const orgsSnapshot = await db.collection('organizations').get();

    for (const orgDoc of orgsSnapshot.docs) {
      const orgId = orgDoc.id;
      const issuesPath = `organizations/${orgId}/dashboards/NDR/issues`;
      const issuesSnapshot = await db.collection(issuesPath).get();

      if (issuesSnapshot.empty) {
        console.log(`→ ${orgId}: no issues collection found or it's empty.`);
        continue;
      }

      console.log(`→ ${orgId}: ${issuesSnapshot.size} issues`);

      for (const issueDoc of issuesSnapshot.docs) {
        const data = issueDoc.data();
        const relatedEntities = data.relatedEntities;

        if (!Array.isArray(relatedEntities)) {
          console.log(`  Skipping ${issueDoc.id}: no relatedEntities array.`);
          continue;
        }

        const relatedEntitiesIds = relatedEntities
          .filter((entity: CloudEntity) => typeof entity?.id === 'string')
          .map((entity: CloudEntity) => entity.id);

        await issueDoc.ref.update({
          relatedEntitiesIds,
          relatedEntities: FieldValue.delete()
        });

        console.log(
          `  Migrated issue ${issueDoc.id}: ${relatedEntitiesIds.length} IDs`
        );
      }
    }

    console.log('✅ Migration complete.');
  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
};

export const migrateRelatedEntitiesDetections = async () => {
  const db = getFirestore();

  try {
    const orgsSnapshot = await db.collection('organizations').get();

    for (const orgDoc of orgsSnapshot.docs) {
      const orgId = orgDoc.id;
      const detectionsPath = `organizations/${orgId}/dashboards/NDR/detections`;
      const snapshot = db.collection(detectionsPath);

      const BATCH_SIZE = 1000;
      const BULKWRITER_LIMIT = 5000; // Close and recreate BulkWriter after this many writes
      let lastDoc = null;
      let totalFetched = 0;
      let totalQueued = 0;
      let bulkWriterQueued = 0;

      let bulkWriter = db.bulkWriter();
      console.log(`starting to migrate detection for org: ${orgId}`);

      while (true) {
        // Only fetch documents where 'relatedEntities' is not null
        let query = snapshot
          .where('relatedEntities', '!=', null)
          .orderBy('relatedEntities')
          .orderBy('__name__')
          .limit(BATCH_SIZE);
        if (lastDoc) {
          query = query.startAfter(lastDoc);
        }
        const batchSnapshot = await query.get();

        if (batchSnapshot.empty) {
          if (totalFetched === 0) {
            console.log(
              `→ ${orgId}: no detections collection found or it's empty.`
            );
          } else {
            console.log(`finished migration for org: ${orgId}`);
          }
          break;
        }

        for (const detectionDoc of batchSnapshot.docs) {
          const data = detectionDoc.data();
          const relatedEntities = data.relatedEntities;

          if (!Array.isArray(relatedEntities)) {
            console.log(
              `  Skipping ${detectionDoc.id}: no relatedEntities array.`
            );
            continue;
          }

          const relatedEntitiesIds = relatedEntities
            .filter((entity: CloudEntity) => typeof entity?.id === 'string')
            .map((entity: CloudEntity) => entity.id);

          // Queue the update in BulkWriter
          bulkWriter.update(detectionDoc.ref, {
            relatedEntitiesIds,
            relatedEntities: FieldValue.delete()
          });

          totalQueued++;
          bulkWriterQueued++;
          console.log(
            `  Queued migration for detection ${detectionDoc.id}: ${relatedEntitiesIds.length} IDs`
          );

          // Close and recreate BulkWriter after BULKWRITER_LIMIT writes
          if (bulkWriterQueued >= BULKWRITER_LIMIT) {
            await bulkWriter.close();
            console.log(
              `→ ${orgId}: BulkWriter closed after queuing ${bulkWriterQueued} updates (intermediate).`
            );
            bulkWriter = db.bulkWriter();
            bulkWriterQueued = 0;
          }
        }

        totalFetched += batchSnapshot.docs.length;
        lastDoc = batchSnapshot.docs[batchSnapshot.docs.length - 1];

        if (batchSnapshot.docs.length < BATCH_SIZE) {
          console.log(`finished migration for org: ${orgId}`);
          break; // No more docs
        }
      }

      // Close the BulkWriter after all updates are queued for this org (if not already closed)
      if (bulkWriterQueued > 0) {
        await bulkWriter.close();
        console.log(
          `→ ${orgId}: BulkWriter closed after queuing ${bulkWriterQueued} updates (final).`
        );
      }
      console.log(
        `→ ${orgId}: Migration complete, total queued: ${totalQueued} updates.`
      );
    }

    console.log('✅ Migration complete.');
  } catch (error: any) {
    console.error('❌ Migration failed:', error);
  }
};
