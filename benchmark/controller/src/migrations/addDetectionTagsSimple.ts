import { UniqueTraffic } from '@globalTypes/ndrBenchmarkTypes/trafficPatternTypes';

import getFirestore from '../firestore/getFirestore';
import { generateDetectionTags } from '../utils/detectionTagUtils';

async function getAllOrganizations(): Promise<string[]> {
  const db = getFirestore();
  const snapshot = await db.collection('organizations').get();
  return snapshot.docs.map((doc) => doc.id);
}

async function processDetectionChunk(
  detectionDocs: any[],
  forceUpdate: boolean
): Promise<Array<{ doc: any; tags: string[] }>> {
  return Promise.all(
    detectionDocs.map(async (doc) => {
      const detectionData = doc.data();

      // Skip if already has non-empty tags (unless forced)
      if (
        !forceUpdate &&
        Array.isArray(detectionData.tags) &&
        detectionData.tags.length > 0
      ) {
        return { doc, tags: detectionData.tags };
      }

      try {
        // Get traffic patterns
        const controlResultsDoc = await doc.ref
          .collection('controlResults')
          .doc('data')
          .get();

        if (!controlResultsDoc.exists) {
          return { doc, tags: [] };
        }

        const trafficPatterns = controlResultsDoc.data()
          ?.data as UniqueTraffic[];

        if (!Array.isArray(trafficPatterns) || trafficPatterns.length === 0) {
          return { doc, tags: [] };
        }

        // Generate tags
        const tags = generateDetectionTags(trafficPatterns);
        return { doc, tags: Array.isArray(tags) ? tags : [] };
      } catch (error) {
        return { doc, tags: [] }; // Always ensure tags exist
      }
    })
  );
}

export const migrateDetectionTags = async (
  concurrency: number = 10,
  forceUpdate: boolean = false,
  organizationIds?: string[]
) => {
  const orgs = organizationIds || (await getAllOrganizations());
  const startTime = Date.now();

  console.log(
    `🚀 Migrating ${orgs.length} organizations with concurrency ${concurrency}`
  );

  let totalUpdated = 0;

  for (let i = 0; i < orgs.length; i++) {
    const orgId = orgs[i];
    console.log(`\n📋 [${i + 1}/${orgs.length}] Processing ${orgId}...`);

    try {
      const orgStartTime = Date.now();
      const updated = await migrateDetectionTagsForOrganization(
        orgId,
        concurrency,
        forceUpdate
      );
      const orgDuration = Math.round((Date.now() - orgStartTime) / 1000);

      totalUpdated += updated;
      console.log(
        `✅ ${orgId}: ${updated} detections updated in ${orgDuration}s`
      );
    } catch (error) {
      console.error(`❌ ${orgId}: Failed -`, error);
    }
  }

  const totalDuration = Math.round((Date.now() - startTime) / 1000);
  console.log(
    `\n🎉 Migration complete: ${totalUpdated} total detections updated across ${orgs.length} orgs in ${totalDuration}s`
  );
};

export const migrateDetectionTagsForOrganization = async (
  organizationId: string,
  concurrency: number = 10,
  forceUpdate: boolean = false
): Promise<number> => {
  const db = getFirestore();
  const detectionsCollection = db.collection(
    `organizations/${organizationId}/dashboards/NDR/detections`
  );

  let totalUpdated = 0;
  let totalProcessed = 0;
  let lastDoc = null;
  const BATCH_SIZE = 1000;
  const startTime = Date.now();

  while (true) {
    // Get batch of detections
    let query = detectionsCollection.orderBy('__name__').limit(BATCH_SIZE);
    if (lastDoc) query = query.startAfter(lastDoc);

    const snapshot = await query.get();
    if (snapshot.empty) {
      if (totalProcessed === 0) {
        console.log(`   📭 ${organizationId}: No detections found`);
      }
      break;
    }

    // Process in chunks for parallel processing
    const docs = snapshot.docs;
    totalProcessed += docs.length;

    const chunks = [];
    for (let i = 0; i < docs.length; i += concurrency) {
      chunks.push(docs.slice(i, i + concurrency));
    }

    console.log(
      `   🔄 ${organizationId}: Processing batch of ${docs.length} detections (${totalProcessed} total so far)`
    );

    // Process all chunks in parallel for maximum performance
    const chunkResults = await Promise.all(
      chunks.map(async (chunk, chunkIndex) => {
        // Process chunk in parallel
        const results = await processDetectionChunk(chunk, forceUpdate);

        // Update detections that need updating
        const bulkWriter = db.bulkWriter();
        let batchCount = 0;

        for (const { doc, tags } of results) {
          const currentTags = doc.data().tags;

          // Only update if tags changed or missing
          if (
            !forceUpdate &&
            Array.isArray(currentTags) &&
            currentTags.length > 0 &&
            JSON.stringify(currentTags.sort()) === JSON.stringify(tags.sort())
          ) {
            continue;
          }

          bulkWriter.update(doc.ref, {
            tags
            // // Clean up old fields
            // src_type: null,
            // src_name: null,
            // src_process: null,
            // src_addr: null,
            // dst_type: null,
            // dst_name: null,
            // dst_process: null,
            // dst_addr: null,
            // src_entity: null,
            // dst_entity: null
          });

          batchCount++;
        }

        if (batchCount > 0) {
          await bulkWriter.close();
          console.log(
            `   ✏️  ${organizationId}: Chunk ${chunkIndex + 1}/${chunks.length} updated ${batchCount} detections`
          );
        }

        return batchCount;
      })
    );

    // Sum up all updates from parallel chunks
    const batchUpdated = chunkResults.reduce((sum, count) => sum + count, 0);
    totalUpdated += batchUpdated;
    console.log(
      `   📦 ${organizationId}: Batch complete - ${batchUpdated} detections updated (${totalUpdated} total)`
    );

    lastDoc = docs[docs.length - 1];
    if (docs.length < BATCH_SIZE) break;
  }

  const duration = Math.round((Date.now() - startTime) / 1000);
  console.log(
    `   🏁 ${organizationId}: Completed - ${totalProcessed} processed, ${totalUpdated} updated in ${duration}s`
  );

  return totalUpdated;
};

export const findDetectionsMissingTags = async (
  organizationId: string
): Promise<number> => {
  const db = getFirestore();
  const detectionsCollection = db.collection(
    `organizations/${organizationId}/dashboards/NDR/detections`
  );

  let missingCount = 0;
  let lastDoc = null;
  const BATCH_SIZE = 1000;

  while (true) {
    let query = detectionsCollection.orderBy('__name__').limit(BATCH_SIZE);
    if (lastDoc) query = query.startAfter(lastDoc);

    const snapshot = await query.get();
    if (snapshot.empty) break;

    for (const doc of snapshot.docs) {
      const data = doc.data();
      if (!Array.isArray(data.tags)) {
        missingCount++;
      }
    }

    lastDoc = snapshot.docs[snapshot.docs.length - 1];
    if (snapshot.docs.length < BATCH_SIZE) break;
  }

  return missingCount;
};
