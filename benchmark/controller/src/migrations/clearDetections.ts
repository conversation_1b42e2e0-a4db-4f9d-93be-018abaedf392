import getFirestore from '../firestore/getFirestore';
import { buildNDRCollectionPath } from '../utils/detectionUtils';

// Helper function to create a visual progress bar
function createProgressBar(
  current: number,
  total: number,
  width: number = 30
): string {
  const percentage = current / total;
  const filledWidth = Math.round(width * percentage);
  const emptyWidth = width - filledWidth;

  const filled = '█'.repeat(filledWidth);
  const empty = '░'.repeat(emptyWidth);

  return `[${filled}${empty}]`;
}

interface OrganizationClearSummary {
  organizationId: string;
  detectionsCount: number;
  success: boolean;
  error?: string;
}

async function getAllOrganizations(): Promise<string[]> {
  const db = getFirestore();
  const snapshot = await db.collection('organizations').get();
  return snapshot.docs.map((doc) => doc.id);
}



async function deleteCollectionWithProgress(
  collectionRef: FirebaseFirestore.CollectionReference,
  collectionName: string,
  initialCount: number
): Promise<void> {
  // Start deletion in background
  const deletionPromise =
    collectionRef.firestore.recursiveDelete(collectionRef);

  // Track progress with periodic count checks
  const progressInterval = setInterval(async () => {
    try {
      const currentCount = await collectionRef.count().get();
      const remaining = currentCount.data().count;
      const deleted = initialCount - remaining;
      const progress = ((deleted / initialCount) * 100).toFixed(1);

      console.log(
        `🗑️  ${collectionName}: ${deleted.toLocaleString()}/${initialCount.toLocaleString()} (${progress}%)`
      );

      // Stop checking when deletion is complete
      if (remaining === 0) {
        clearInterval(progressInterval);
      }
    } catch (error) {
      // Collection might be deleted already, stop checking
      clearInterval(progressInterval);
    }
  }, 10000); // Check every 2 seconds

  // Wait for deletion to complete
  await deletionPromise;
  clearInterval(progressInterval);
}

async function clearDetectionsForOrganization(
  organizationId: string
): Promise<OrganizationClearSummary> {
  const db = getFirestore();

  try {
    console.log(
      `🔄 Starting to clear detections for organization: ${organizationId}`
    );

    // Get references to collections we need to clear
    const detectionsRef = db.collection(
      buildNDRCollectionPath(organizationId, 'detections')
    );
    const detectionControlMetricsRef = db.collection(
      buildNDRCollectionPath(organizationId, 'detectionControlMetrics')
    );

    // Get counts before clearing
    const detectionsSnapshot = await detectionsRef.count().get();
    const detectionsCount = detectionsSnapshot.data().count;
    const metricsSnapshot = await detectionControlMetricsRef.count().get();
    const metricsCount = metricsSnapshot.data().count;

    console.log(
      `📊 Found ${detectionsCount} detections and ${metricsCount} metrics to clear for organization: ${organizationId}`
    );

    // Delete collections with progress tracking
    const deleteDetectionsPromise = deleteCollectionWithProgress(
      detectionsRef,
      'Detections',
      detectionsCount
    );
    const deleteMetricsPromise = deleteCollectionWithProgress(
      detectionControlMetricsRef,
      'Metrics',
      metricsCount
    );

    // Run both deletions in parallel
    await Promise.all([deleteDetectionsPromise, deleteMetricsPromise]);

    console.log(
      `✅ Successfully cleared ${detectionsCount} detections and metrics for organization: ${organizationId}`
    );

    return {
      organizationId,
      detectionsCount,
      success: true
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(
      `❌ Error clearing detections for organization ${organizationId}:`,
      error
    );

    return {
      organizationId,
      detectionsCount: 0,
      success: false,
      error: errorMessage
    };
  }
}

export const clearAllOrganizationsDetections = async (
  organizationIds?: string[]
) => {
  const orgs = organizationIds || (await getAllOrganizations());
  const startTime = Date.now();

  console.log(
    `🚀 Starting to clear detections for ${orgs.length} organizations...`
  );

  // Process all organizations with progress bar
  const results: OrganizationClearSummary[] = [];

  for (let i = 0; i < orgs.length; i++) {
    const orgId = orgs[i];
    const progress = (((i + 1) / orgs.length) * 100).toFixed(1);
    const progressBar = createProgressBar(i + 1, orgs.length);

    console.log(
      `\n${progressBar} [${progress}%] Processing ${i + 1}/${orgs.length}: ${orgId}`
    );

    const result = await clearDetectionsForOrganization(orgId);
    results.push(result);
  }

  const totalDuration = Math.round((Date.now() - startTime) / 1000);

  // Summary report
  console.log(`\n📊 CLEAR OPERATION COMPLETE`);
  console.log(`⏱️  Total time: ${totalDuration}s`);
  console.log(`🏢 Organizations processed: ${orgs.length}`);

  const successfulResults = results.filter((r) => r.success);
  const failedResults = results.filter((r) => !r.success);
  const totalDetectionsCleared = successfulResults.reduce(
    (sum, r) => sum + r.detectionsCount,
    0
  );

  console.log(
    `✅ Successfully cleared: ${successfulResults.length} organizations`
  );
  console.log(
    `📈 Total detections cleared: ${totalDetectionsCleared.toLocaleString()}`
  );

  if (failedResults.length > 0) {
    console.log(`❌ Failed to clear: ${failedResults.length} organizations`);
    failedResults.forEach((result) => {
      console.log(`   • ${result.organizationId}: ${result.error}`);
    });
  }

  return {
    results,
    summary: {
      totalOrganizations: orgs.length,
      successful: successfulResults.length,
      failed: failedResults.length,
      totalDetectionsCleared,
      totalDuration
    }
  };
};

// Run the clear operation
const run = async () => {
  try {
    console.log('Starting detection clearing operation...\n');

    // Uncomment the line below to run for all organizations
    await clearAllOrganizationsDetections();

    // Or specify specific organization IDs
    // await clearAllOrganizationsDetections(['org_2nFGdRrwmhl0rlsJl7dzyN8Te75']);

    console.log(
      '\n⚠️  Please uncomment the desired operation in the run function before executing!'
    );
    console.log('✅ Clear operation completed successfully!');
  } catch (error) {
    console.error('❌ Error running clear operation:', error);
  }
};

// Execute if this file is run directly
if (require.main === module) {
  run();
}
