import getFirestore from '../firestore/getFirestore';
import getIssues from '../firestore/getIssues';
import { getIssuesCountByEntityId } from '../services/issues/utils';

/**
 * Migration to add issueCount property to all entities across all organizations.
 * This function:
 * 1. Fetches all issues for each organization
 * 2. Calculates issue counts per entity ID
 * 3. Updates each entity document with its corresponding issue count
 *
 * @param targetOrgId - Optional organization ID to run migration for a single org only
 */
export const addingIssueCountToEntity = async (targetOrgId?: string) => {
  const db = getFirestore();
  const startTime = Date.now();

  try {
    if (targetOrgId) {
      console.log(
        `🚀 Starting migration for single organization: ${targetOrgId}`
      );
    } else {
      console.log(
        '🚀 Starting migration: Adding issueCount to entities for all organizations...'
      );
    }

    // Get organizations to process
    let orgIds: string[];

    if (targetOrgId) {
      // Verify the target organization exists
      const targetOrgDoc = await db
        .collection('organizations')
        .doc(targetOrgId)
        .get();
      if (!targetOrgDoc.exists) {
        console.log(`❌ Organization ${targetOrgId} not found.`);
        return;
      }
      orgIds = [targetOrgId];
    } else {
      // Get all organizations
      const orgsSnapshot = await db.collection('organizations').get();
      if (orgsSnapshot.empty) {
        console.log('ℹ️  No organizations found.');
        return;
      }
      orgIds = orgsSnapshot.docs.map((doc) => doc.id);
    }

    const totalOrgs = orgIds.length;
    console.log(
      `📋 Found ${totalOrgs} organization${totalOrgs > 1 ? 's' : ''} to process`
    );

    let totalEntitiesUpdated = 0;

    // Process each organization
    for (let i = 0; i < orgIds.length; i++) {
      const orgId = orgIds[i];

      console.log(
        `\n📂 [${i + 1}/${totalOrgs}] Processing organization: ${orgId}`
      );

      try {
        const orgStartTime = Date.now();

        // Get all issues for this organization
        const issues = await getIssues(orgId);
        console.log(`   📊 Found ${issues.length} issues`);

        // Calculate issue counts by entity ID
        const issueCountsByEntityId = getIssuesCountByEntityId(issues);
        const entityIdsWithIssues = Object.keys(issueCountsByEntityId);
        console.log(
          `   🎯 Found issues for ${entityIdsWithIssues.length} entities`
        );

        // Get entities collection for this organization
        const entitiesCollectionRef = db.collection(
          `organizations/${orgId}/dashboards/NDR/entities`
        );

        // Fetch all entities
        const entitiesSnapshot = await entitiesCollectionRef.get();

        if (entitiesSnapshot.empty) {
          console.log(`   ⚠️  No entities found for organization ${orgId}`);
          continue;
        }

        console.log(`   📦 Found ${entitiesSnapshot.docs.length} entities`);

        // Use BulkWriter for batch updates
        const bulkWriter = db.bulkWriter();
        let updatedCount = 0;

        // Update each entity with its issue count
        entitiesSnapshot.docs.forEach((entityDoc) => {
          const entityId = entityDoc.id;
          const insightsCount = issueCountsByEntityId[entityId] || 0;

          // Update the entity document with issueCount
          bulkWriter.update(entityDoc.ref, {
            insightsCount
          });

          updatedCount++;
        });

        // Execute all updates
        await bulkWriter.close();

        const orgDuration = Math.round((Date.now() - orgStartTime) / 1000);
        totalEntitiesUpdated += updatedCount;

        console.log(
          `   ✅ Updated ${updatedCount} entities in ${orgDuration}s`
        );
      } catch (orgError) {
        console.error(
          `   ❌ Error processing organization ${orgId}:`,
          orgError
        );
        // Continue with next organization instead of failing completely
      }
    }

    const totalDuration = Math.round((Date.now() - startTime) / 1000);
    console.log(`\n🎉 Migration completed successfully!`);
    console.log(`📈 Total entities updated: ${totalEntitiesUpdated}`);
    console.log(`⏱️  Total time: ${totalDuration}s`);
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
};

/**
 * Helper function to run the migration for a single organization.
 * @param organizationId - The ID of the organization to migrate
 */
export const addingIssueCountToEntityForOrg = async (
  organizationId: string
) => {
  return addingIssueCountToEntity(organizationId);
};
