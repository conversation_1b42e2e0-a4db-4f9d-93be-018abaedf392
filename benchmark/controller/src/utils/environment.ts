import { readFileSync } from 'fs';

export function getProjectID(): string {
  if (process.env.GCLOUD_PROJECT) {
    return process.env.GCLOUD_PROJECT;
  }

  const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
  if (!credentialsPath) {
    throw new Error('GOOGLE_APPLICATION_CREDENTIALS not set');
  }

  const credentials = JSON.parse(readFileSync(credentialsPath, 'utf-8'));
  if (!credentials.project_id) {
    throw new Error('Project ID not found in credentials');
  }

  return credentials.project_id;
}

export function isProductionEnv() {
  return getProjectID() === 'port0-6567b';
}
