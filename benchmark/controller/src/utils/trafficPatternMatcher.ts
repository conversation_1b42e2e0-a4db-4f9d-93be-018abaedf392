import { TrafficPattern } from '../globalTypes/ndrBenchmarkTypes/trafficPatternTypes';
import { MatchPattern } from '../benchmark/controls/types';
import { DIRECTION, PortRange } from '../globalTypes/FirewallTypes';
import { DIRECTIONS } from '../globalTypes/ndrBenchmarkTypes';

function extractPorts(matchPattern: MatchPattern): PortRange[] {
  return matchPattern.port.map((port) => {
    if (port.includes('-')) {
      return {
        start: parseInt(port.split('-')[0]),
        end: parseInt(port.split('-')[1])
      };
    } else {
      return {
        start: parseInt(port),
        end: parseInt(port)
      };
    }
  });
}

export function isPatternContained(
  pattern: TrafficPattern,
  matchPatterns: MatchPattern[]
): boolean {
  for (const matchPattern of matchPatterns) {
    const ports = extractPorts(matchPattern);

    const direction =
      pattern.direction == DIRECTIONS.INGRESS
        ? DIRECTION.INBOUND
        : DIRECTION.OUTBOUND;

    const port = parseInt(pattern.port);
    const portMatch = ports.some((p) => p.start <= port && p.end >= port);

    if (portMatch && direction === matchPattern.direction) {
      return true;
    }
  }

  return false;
}

export function formatPatternToString(pattern: TrafficPattern): string {
  return `${pattern.direction}-${pattern.port}-${pattern.remoteAddr}`;
}
