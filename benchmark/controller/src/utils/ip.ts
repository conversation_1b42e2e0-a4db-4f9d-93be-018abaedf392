import ip from 'ip';
import { isValidCIDR } from 'ipaddr.js';
import ipRangeCheck from 'ip-range-check';

export function isIpInCidr(ip: string, cidr: string): boolean {
  try {
    if (!isValidCIDR(cidr)) {
      throw new Error(`Invalid CIDR ${cidr}`);
    }

    return ipRangeCheck(ip, cidr);
  } catch (e) {
    console.log(`Error checking if IP is in CIDR ${cidr} ${ip}`, e);
    return false;
  }
}

export function isIpInRange(
  ipToCheck: string,
  startIp: string,
  endIp: string
): boolean {
  // Validate IPs
  if (
    !(
      ip.isV4Format(ipToCheck) &&
      ip.isV4Format(startIp) &&
      ip.isV4Format(endIp)
    )
  ) {
    return false;
  }

  const ipNum = ip.toLong(ipToCheck);
  const startNum = ip.toLong(startIp);
  const endNum = ip.toLong(endIp);

  // Ensure correct order of range
  if (startNum > endNum) {
    return false;
  }

  return startNum <= ipNum && ipNum <= endNum;
}
