import { Parser } from 'node-sql-parser';
import crypto from 'crypto';
import Handlebars from 'handlebars';

import {
  IDetectionControlChanges,
  ORIGIN_TYPES,
  StaticDetectionControl
} from '@globalTypes/ndrBenchmarkTypes/detectionTypes';
import { DetectionControl } from '@globalTypes/ndrBenchmarkTypes/detectionTypes';
import { SEVERITY_MAP } from '@globalTypes/ndrBenchmarkTypes';
import log from '@logger';

import {
  DETECTIONS_TABLES,
  RECENT_TRAFFIC_TABLE_NAME,
  SELECT_FIELDS
} from '../benchmark/consts';
import { getProjectID } from './environment';
import * as predefinedDetectionControls from '../benchmark/controls/detections';
import getFirestore from '../firestore/getFirestore';
import { getPort0DetectionControls } from '../firestore/getDetections';

export function calculateChanges(
  existingControlsMap: Record<string, DetectionControl>,
  staticControlsMap: Record<string, StaticDetectionControl>
): IDetectionControlChanges {
  const changes: IDetectionControlChanges = {
    create: [],
    update: [],
    delete: []
  };

  for (const id in staticControlsMap) {
    const existingControl = existingControlsMap[id];
    const staticControl = staticControlsMap[id];

    if (!existingControl) {
      changes.create.push(
        generateDetectionControl({
          ...staticControl,
          id: id,
          subTitle: staticControl.subTitle ?? '',
          explanation: staticControl.explanation ?? ''
        })
      );
    } else {
      // if predefined controls were updated (severity or query changed), update them for the org
      changes.update.push({
        ...existingControl,
        query: staticControl.query,
        severity: staticControl.severity ?? SEVERITY_MAP.LOW,
        subTitle: staticControl.subTitle ?? '',
        explanation: staticControl.explanation ?? '',
        updatedAt: new Date(),
        interval: Math.min(staticControl.interval ?? 10, 180)
      });
    }
  }

  // if predefined controls were removed, remove them for the org
  for (const id in existingControlsMap) {
    if (!staticControlsMap[id]) {
      changes.delete.push(existingControlsMap[id]);
    }
  }

  return changes;
}

export async function syncPort0DetectionControls(
  organizationId: string
): Promise<void> {
  try {
    const staticDetectionControls: StaticDetectionControl[] = Object.values(
      predefinedDetectionControls
    ).flat() as StaticDetectionControl[];

    for (const control of staticDetectionControls) {
      control.id = control.id || generateDetectionControlId(control);
    }

    const staticControlsMap: Record<string, StaticDetectionControl> =
      arrayToMap(staticDetectionControls);

    const existingDetectionControls =
      await getPort0DetectionControls(organizationId);

    const existingControlsMap: Record<string, DetectionControl> = arrayToMap(
      existingDetectionControls
    );

    const changes = calculateChanges(existingControlsMap, staticControlsMap);

    // apply the changes to the firestore
    await syncDetectionControls(organizationId, changes);
  } catch (err) {
    log.error(`Error running benchmarks: ${(err as Error).message || ''}`);
  }
}

async function syncDetectionControls(
  organizationId: string,
  changes: IDetectionControlChanges
): Promise<void> {
  // Bulk write operations for detection controls
  const bulkWriter = getFirestore().bulkWriter();
  const detectionControlsPath = buildNDRCollectionPath(
    organizationId,
    'detectionControls'
  );
  const detectionControlMetricsPath = buildNDRCollectionPath(
    organizationId,
    'detectionControlMetrics'
  );

  changes.delete.forEach((control) => {
    bulkWriter.delete(
      getFirestore().doc(`${detectionControlsPath}/${control.id}`)
    );
    bulkWriter.delete(
      getFirestore().doc(`${detectionControlMetricsPath}/${control.id}`)
    );
  });

  changes.update.forEach((control) => {
    bulkWriter.update(
      getFirestore().doc(`${detectionControlsPath}/${control.id}`),
      control
    );
  });

  changes.create.forEach((control) => {
    bulkWriter.set(
      getFirestore().doc(`${detectionControlsPath}/${control.id}`),
      control
    );
  });

  await bulkWriter.close();
}

export function arrayToMap(array: any[]) {
  return array.reduce(
    (acc, item) => {
      acc[item.id] = item;
      return acc;
    },
    {} as Record<string, any>
  );
}

export function buildNDRCollectionPath(
  organizationId: string,
  collectionName: string
): string {
  return `organizations/${organizationId}/dashboards/NDR/${collectionName}`;
}

export function wrapFinalSelectWithParser(baseQuery: string): string {
  const parser = new Parser();

  const cleanedQuery = baseQuery.replace(/;\s*$/, '');
  const ast: any = parser.astify(cleanedQuery, { database: 'bigquery' });

  // Handle multi-statement: take last SELECT
  const mainAst: any = Array.isArray(ast) ? ast[ast.length - 1] : ast;

  if (!mainAst || mainAst.type !== 'select') {
    throw new Error('Expected a single top-level SELECT statement');
  }

  // Extract existing WITH info or set defaults
  const existingWith: any = mainAst.with;
  let existingCtes: any[] = [];
  let recursive: boolean = false;

  if (existingWith) {
    if (Array.isArray(existingWith)) {
      existingCtes = existingWith;
    } else if (existingWith.ctes && Array.isArray(existingWith.ctes)) {
      existingCtes = existingWith.ctes;
      recursive = existingWith.recursive === true;
    } else if (existingWith.type === 'with') {
      existingCtes = existingWith.ctes || [];
      recursive = existingWith.recursive === true;
    }
  }

  // Create a new SELECT statement that represents the final SELECT (without WITH)
  const finalSelect: any = {
    type: 'select',
    columns: mainAst.columns,
    from: mainAst.from,
    where: mainAst.where,
    groupby: mainAst.groupby,
    having: mainAst.having,
    orderby: mainAst.orderby,
    limit: mainAst.limit
  };

  // Create new CTE list with base_query containing the final SELECT
  const updatedCtes: any[] = [
    ...existingCtes,
    {
      name: { type: 'default', value: 'base_query' },
      stmt: { tableList: [], columnList: [], ast: finalSelect }
    }
  ];

  // Build wrapped AST with just the CTEs, no final SELECT
  const wrappedAst: any = {
    type: 'select',
    with: updatedCtes,
    columns: [
      {
        expr: { type: 'column_ref', table: 'base_query', column: '*' },
        as: null
      }
    ],
    from: [
      {
        db: null,
        table: 'base_query',
        as: null
      }
    ]
  };

  // Convert AST back to SQL and extract just the WITH clause
  const fullSql = parser.sqlify(wrappedAst, { database: 'bigquery' });

  // Extract only the WITH clause part (everything before the final SELECT)
  const withMatch = fullSql.match(
    /WITH\s+([\s\S]*?)(?=\s*SELECT\s+base_query\.\*)/
  );
  if (!withMatch) {
    throw new Error('Failed to extract WITH clause from generated SQL');
  }

  return `WITH ${withMatch[1]}`;
}

export async function enrichPort0Query(baseQuery: string): Promise<string> {
  const parser = new Parser();
  const ast = parser.astify(baseQuery, { database: 'bigquery' });

  const mainAst = Array.isArray(ast) ? ast[ast.length - 1] : ast;

  if (mainAst.type !== 'select') {
    throw new Error('Not a SELECT statement');
  }

  // Extract column info
  const selectColumns = mainAst.columns.map((col) => {
    if (col.as) {
      return col.as;
    }

    if (col.expr?.type === 'column_ref') {
      const colRef = col.expr.column;

      if (colRef?.expr?.value) {
        return colRef.expr.value;
      }

      if (typeof colRef === 'string') {
        return colRef;
      }
    }

    throw new Error(
      `Failed to extract column name from column: ${JSON.stringify(col)}`
    );
  });

  const columnNames = selectColumns;

  // Build WHERE conditions for the join
  const joinConditionsArray = columnNames
    .filter((c) => SELECT_FIELDS.includes(c) && c !== 'session_count') // Only columns that exist in deduplicated_sessions
    .map((c) => `ds.${c} = base.${c}`);

  if (joinConditionsArray.length === 0) {
    throw new Error(
      'No valid join columns found between base query output and deduplicated_sessions fields.'
    );
  }
  const joinConditions = joinConditionsArray.join(' AND ');

  const dsSelectFields = SELECT_FIELDS.map((f) => `ds.${f}`).join(',\n      ');
  const formattedBaseQuery = wrapFinalSelectWithParser(baseQuery);

  // Return the enriched query
  return `
  ${formattedBaseQuery}

,
deduplicated_sessions AS (
  SELECT
    DISTINCT
    id,
    src_id, src_name, src_process, src_cmd, dst_cmd, src_type, src_addr,
    dst_id, dst_name, dst_process, dst_type, dst_addr,
    session_count,
    CAST(port AS INT64) AS port
  FROM traffic_logs
)
SELECT 
  base.*,
  ARRAY(
    SELECT AS STRUCT
      ${dsSelectFields}
    FROM deduplicated_sessions ds
    WHERE ${joinConditions}
  ) AS results
FROM base_query base
`.trim();
}

export async function updateLastRunAt(
  organizationId: string,
  controls: DetectionControl[]
): Promise<void> {
  const firestore = getFirestore();
  const now = new Date();
  const ms = now.getTime();

  const batch = firestore.batch();

  for (const control of controls) {
    // Round down to the nearest interval
    const intervalMs = control.interval * 60 * 1000;
    const roundedMs = Math.floor(ms / intervalMs) * intervalMs;
    const roundedDate = new Date(roundedMs);
    const docRef = firestore.doc(
      `${buildNDRCollectionPath(organizationId, 'detectionControls')}/${control.id}`
    );

    batch.set(
      docRef,
      {
        lastRunAt: roundedDate,
        interval: control.interval
      },
      { merge: true }
    );
  }

  await batch.commit();
}
export function generateDetectionQuery({
  organizationId,
  query,
  interval
}: {
  organizationId: string;
  query: string;
  interval: number;
}): string {
  const processedQuery = Handlebars.compile(query)({
    organizationId: organizationId,
    projectId: getProjectID(),
    tableName: DETECTIONS_TABLES.TRAFFIC,
    table: DETECTIONS_TABLES.TRAFFIC_LOGS,
    completedTableName: DETECTIONS_TABLES.COMPLETED_TRAFFIC,
    completed30DaysTableName: DETECTIONS_TABLES.COMPLETED_30_DAYS_TRAFFIC,
    completed30DaysTrafficExceptCurrentIntervalTableName:
      DETECTIONS_TABLES.COMPLETED_30_DAYS_TRAFFIC_EXCEPT_CURRENT_INTERVAL,
    completed30DaysTrafficExceptCurrentIntervalSrcDstIdsTableName:
      DETECTIONS_TABLES.COMPLETED_30_DAYS_TRAFFIC_EXCEPT_CURRENT_INTERVAL_SRC_DST_IDS,
    interval: interval
  });

  const trafficLogsCTE =
    interval === 10
      ? `SELECT * from ${organizationId}.${RECENT_TRAFFIC_TABLE_NAME}`
      : `SELECT * from completed_traffic WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL ${interval} MINUTE)`;

  return `WITH completed_traffic AS (
        SELECT * FROM ${organizationId}.${DETECTIONS_TABLES.COMPLETED_TRAFFIC}
      ),
      traffic_logs AS (
        ${trafficLogsCTE}
      ),
      completed_30_days_traffic AS (
        SELECT * FROM completed_traffic
        WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
      ),
      completed_30_days_traffic_except_current_interval AS (
        SELECT * FROM completed_30_days_traffic
        WHERE created_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL ${interval} MINUTE)
      ),
      completed_30_days_traffic_except_current_interval_src_dst_ids AS (
        SELECT distinct src_id, dst_id FROM ${organizationId}.${DETECTIONS_TABLES.COMPLETED_TRAFFIC}
        WHERE created_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND created_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL ${interval} MINUTE)
      )
     ${processedQuery};`;
}

export function generateDetectionControl(
  control: StaticDetectionControl
): DetectionControl {
  return {
    id: control.id ?? generateDetectionControlId(control),
    name: control.name,
    category: control.category,
    query: control.query,
    subTitle: control.subTitle ?? '',
    explanation: control.explanation ?? '',
    sequelizedQuery: control.sequelizedQuery ?? null,
    minThreshold: control.minThreshold ?? 1,
    severity: control.severity ?? SEVERITY_MAP.LOW,
    emailNotify: control.emailNotify ?? false,
    origin: control.origin ?? ORIGIN_TYPES.PORT0,
    active: control.active ?? true,
    createdBy: control.createdBy ?? ORIGIN_TYPES.PORT0,
    createdAt: control.createdAt ?? new Date(),
    updatedAt: control.updatedAt ?? new Date(),
    interval: control.interval ?? 10
  };
}

export function generateDetectionControlId(
  control: StaticDetectionControl
): string {
  const key = `${control.name}-${control.category}`;
  return crypto.createHash('md5').update(key).digest('hex');
}

export function fetchCategoriesFromDetectionControls(
  detectionControls: DetectionControl[]
): string[] {
  const uniqueCategories = new Set<string>();

  detectionControls.forEach((control) => {
    if (control.category) {
      uniqueCategories.add(control.category);
    }
  });

  return Array.from(uniqueCategories);
}
