import { DETECTION_TAG_TYPES } from '../globalTypes/ndrBenchmarkTypes/detectionTypes';
import { UniqueTraffic } from '../globalTypes/ndrBenchmarkTypes/trafficPatternTypes';

/**
 * Generates tags array from traffic patterns for a detection
 * @param trafficPatterns - Array of UniqueTraffic patterns from the detection
 * @returns Array of tag strings in "key:value" format
 */
export function generateDetectionTags(
  trafficPatterns: UniqueTraffic[]
): string[] {
  const tagSets = {
    [DETECTION_TAG_TYPES.SRC_TYPE]: new Set<string>(),
    [DETECTION_TAG_TYPES.SRC_NAME]: new Set<string>(),
    [DETECTION_TAG_TYPES.SRC_PROCESS]: new Set<string>(),
    [DETECTION_TAG_TYPES.SRC_ADDR]: new Set<string>(),
    [DETECTION_TAG_TYPES.SRC_CMD]: new Set<string>(),
    [DETECTION_TAG_TYPES.DST_TYPE]: new Set<string>(),
    [DETECTION_TAG_TYPES.DST_NAME]: new Set<string>(),
    [DETECTION_TAG_TYPES.DST_PROCESS]: new Set<string>(),
    [DETECTION_TAG_TYPES.DST_ADDR]: new Set<string>(),
    [DETECTION_TAG_TYPES.DST_CMD]: new Set<string>(),

    [DETECTION_TAG_TYPES.PORT]: new Set<string>()
  };

  // Collect unique values from traffic patterns
  trafficPatterns.forEach((pattern) => {
    // Source tags (only if value is not empty)
    if (pattern.src.type && pattern.src.type.trim() !== '') {
      tagSets[DETECTION_TAG_TYPES.SRC_TYPE].add(pattern.src.type.trim());
    }
    if (pattern.src.name && pattern.src.name.trim() !== '') {
      tagSets[DETECTION_TAG_TYPES.SRC_NAME].add(pattern.src.name.trim());
    }
    if (pattern.src.process && pattern.src.process.trim() !== '') {
      tagSets[DETECTION_TAG_TYPES.SRC_PROCESS].add(pattern.src.process.trim());
    }
    if (pattern.src.addr && pattern.src.addr.trim() !== '') {
      tagSets[DETECTION_TAG_TYPES.SRC_ADDR].add(pattern.src.addr.trim());
    }
    if (pattern.src.cmd && pattern.src.cmd.trim() !== '') {
      tagSets[DETECTION_TAG_TYPES.SRC_CMD].add(pattern.src.cmd.trim());
    }
    // Destination tags (only if value is not empty)
    if (pattern.dst.type && pattern.dst.type.trim() !== '') {
      tagSets[DETECTION_TAG_TYPES.DST_TYPE].add(pattern.dst.type.trim());
    }
    if (pattern.dst.name && pattern.dst.name.trim() !== '') {
      tagSets[DETECTION_TAG_TYPES.DST_NAME].add(pattern.dst.name.trim());
    }
    if (pattern.dst.process && pattern.dst.process.trim() !== '') {
      tagSets[DETECTION_TAG_TYPES.DST_PROCESS].add(pattern.dst.process.trim());
    }
    if (pattern.dst.addr && pattern.dst.addr.trim() !== '') {
      tagSets[DETECTION_TAG_TYPES.DST_ADDR].add(pattern.dst.addr.trim());
    }

    if (pattern.dst.cmd && pattern.dst.cmd.trim() !== '') {
      tagSets[DETECTION_TAG_TYPES.DST_CMD].add(pattern.dst.cmd.trim());
    }
    if (pattern.port) {
      tagSets[DETECTION_TAG_TYPES.PORT].add(pattern.port.toString());
    }
  });

  // Convert to key:value format
  const tags: string[] = [];

  Object.entries(tagSets).forEach(([tagType, valueSet]) => {
    Array.from(valueSet).forEach((value) => {
      tags.push(`${tagType}:${value}`);
    });
  });

  return tags;
}

/**
 * Gets unique values for a specific tag type from tags array
 * @param tags - Array of tag strings in "key:value" format
 * @param tagType - The tag type to filter by (e.g., "src_type", "dst_addr")
 * @returns Array of unique values for the specified tag type
 */
export function getUniqueTagValues(tags: string[], tagType: string): string[] {
  const values = new Set<string>();

  tags.forEach((tag) => {
    const [type, value] = tag.split(':');
    if (type === tagType && value) {
      values.add(value);
    }
  });

  return Array.from(values);
}

/**
 * Checks if a detection has any tags
 * @param tags - Array of tag strings
 * @returns True if any tags exist
 */
export function hasAnyTags(tags?: string[]): boolean {
  return Array.isArray(tags) && tags.length > 0;
}

/**
 * Counts total tags
 * @param tags - Array of tag strings
 * @returns Total count of tags
 */
export function getTotalTagCount(tags?: string[]): number {
  return Array.isArray(tags) ? tags.length : 0;
}

/**
 * Parses a tag string into type and value
 * @param tag - Tag string in "key:value" format
 * @returns Object with type and value, or null if invalid
 */
export function parseTag(tag: string): { type: string; value: string } | null {
  const colonIndex = tag.indexOf(':');
  if (colonIndex === -1) return null;

  const type = tag.substring(0, colonIndex);
  const value = tag.substring(colonIndex + 1);

  if (!type || !value) return null;

  return { type, value };
}

/**
 * Filters tags by type
 * @param tags - Array of tag strings
 * @param tagType - The tag type to filter by
 * @returns Array of tags matching the specified type
 */
export function filterTagsByType(tags: string[], tagType: string): string[] {
  return tags.filter((tag) => tag.startsWith(`${tagType}:`));
}
