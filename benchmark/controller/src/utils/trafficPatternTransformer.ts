import {
  UniqueTraffic,
  TrafficPattern,
  BaseTraffic
} from '../globalTypes/ndrBenchmarkTypes/trafficPatternTypes';
import {
  BaseEntity,
  CloudEntity
} from '../globalTypes/ndrBenchmarkTypes/entityTypes';

function getUniqueTrafficID(traffic: BaseTraffic): string {
  return [
    traffic.src.id,
    traffic.src.name,
    traffic.src.addr,
    traffic.src.process,
    traffic.dst.id,
    traffic.dst.name,
    traffic.dst.addr,
    traffic.dst.process,
    traffic.port
  ].join('-');
}
export function transfromOldTrafficPattern(
  oldTrafficPattern: TrafficPattern,
  entities: Map<string, BaseEntity>
): UniqueTraffic {
  let localEntity = entities.get(oldTrafficPattern.entityId);
  if (!localEntity) {
    // If the entity was removed from firestore (sentinelOne) but this traffic pattern happend time ago
    // so its possible for us to miss the entity in the map provided from firestore
    // That way we choose the localAddr as the name

    localEntity = {
      name: oldTrafficPattern.localAddr,
      type: 'unrecognized',
      info: {}
    };
    // console.error(`QueryTrafficPatterns: Entity with id ${oldTrafficPattern.entityId} not found`)
  }

  if (oldTrafficPattern.remoteEntity.type === 'reference') {
    oldTrafficPattern.remoteEntity.type = localEntity.type;
  }

  const localTrafficSide = {
    id: (localEntity as CloudEntity).id || localEntity.name,
    name: localEntity.name,
    addr: oldTrafficPattern.localAddr,
    process: oldTrafficPattern.providerInformation?.process || '',
    type: localEntity.type
  };

  const remoteTrafficSide = {
    id:
      (oldTrafficPattern.remoteEntity as CloudEntity).id ||
      oldTrafficPattern.remoteEntity.name,
    name: oldTrafficPattern.remoteEntity.name,
    addr: oldTrafficPattern.remoteAddr,
    process: oldTrafficPattern.remoteProviderInformation?.process || '',
    type: oldTrafficPattern.remoteEntity.type
  };

  const baseTraffic = {
    src:
      oldTrafficPattern.direction === 'egress'
        ? localTrafficSide
        : remoteTrafficSide,
    dst:
      oldTrafficPattern.direction === 'egress'
        ? remoteTrafficSide
        : localTrafficSide,
    port: parseInt(oldTrafficPattern.port)
  } as BaseTraffic;

  return {
    ...baseTraffic,
    id: oldTrafficPattern.id,
    sessionCount: oldTrafficPattern.sessionCount
  };
}
