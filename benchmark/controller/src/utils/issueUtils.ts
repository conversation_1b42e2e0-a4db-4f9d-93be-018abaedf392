import { Issue } from '../globalTypes/ndrBenchmarkTypes/issueTypes';
import log from '../logger';
import { x86 } from 'murmurhash3js';
import {
  formatPatternToString,
  isPatternContained
} from './trafficPatternMatcher';
import { FIREWALL_RULE_POLICY } from '../globalTypes/FirewallTypes';
import { FirewallRule } from '../globalTypes/FirewallTypes';
import { TrafficPattern } from '../globalTypes/ndrBenchmarkTypes/trafficPatternTypes';
import { ISSUE_TYPES } from '../services/issues/constants';

export function isIssueValid(
  issue: Issue,
  trafficPatterns: TrafficPattern[]
): boolean {
  // try {
  //   if (issue.trafficPatterns.length === 0) {
  //     log.debug(`Issue has no traffic patterns`, {
  //       context: 'matchTrafficPatternsToIssue',
  //       issue: issue
  //     });
  //     return true;
  //   }

  //   const formattedIssueTrafficPatterns = new Set(
  //     issue.trafficPatterns.map((pattern) => formatPatternToString(pattern))
  //   );

  //   const issueRemediationAcceptRules = Object.values(
  //     issue.remediation?.data?.[issue.entityId] || []
  //   ).filter(
  //     (rule: FirewallRule) => rule.policy === FIREWALL_RULE_POLICY.ACCEPT
  //   );

  //   const validTrafficPatternsIds: string[] = [];

  //   // create an array of match patterns without duplicates
  //   const matchPatterns = Array.from(
  //     new Map(
  //       issueRemediationAcceptRules.map((rule: FirewallRule) => [
  //         `${rule.direction}-${rule.portRanges.join(',')}`,
  //         {
  //           direction: rule.direction,
  //           port: rule.portRanges
  //         }
  //       ])
  //     ).values()
  //   );

  //   for (const pattern of trafficPatterns) {
  //     // Check if the pattern is valid
  //     if (isPatternContained(pattern, matchPatterns)) {
  //       // If patterns match and pattern is not in the issue - issue is invalid
  //       const formattedPattern = formatPatternToString(pattern);
  //       if (!formattedIssueTrafficPatterns.has(formattedPattern)) {
  //         return false;
  //       } else {
  //         validTrafficPatternsIds.push(pattern.id);
  //       }
  //     }
  //   }

  //   // If the issue's traffic patterns length is different from the valid traffic patterns length - issue is invalid, otherwise it's valid
  //   return issue.trafficPatterns.length === validTrafficPatternsIds.length;
  // } catch (error: any) {
  //   log.error(`Error validating issue: ${error}`);
  //   return true; // if error occured, better to keep the issue
  // }
  return true;
}

export function hashIssue(issue: Issue): string {
  if (Object.values(ISSUE_TYPES).find((key) => key === issue.category)) {
    const obj = JSON.stringify({
      category: issue.category,
      ruleId: issue.ruleId
    });

    return hash(obj);
  }

  const obj = JSON.stringify({
    entityId: issue.entityId,
    category: issue.category,
    explanation: issue.explanation
  });
  return hash(obj);
}

export function hash(obj: any): string {
  return x86.hash32(JSON.stringify(obj)).toString(16);
}
