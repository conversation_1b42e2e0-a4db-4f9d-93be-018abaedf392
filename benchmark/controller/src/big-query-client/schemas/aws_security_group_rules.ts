import { DatabaseSchema } from '../bigquery/types';
import { BIG_QUERY_TABLES } from '../definitions/constants';

const SCHEMA_FIELDS = [
  { name: 'id', type: 'STRING' },
  { name: 'region', type: 'STRING' },
  { name: 'account_id', type: 'STRING' },
  { name: 'description', type: 'STRING' },
  { name: 'security_group_id', type: 'STRING' },
  { name: 'direction', type: 'STRING' },
  { name: 'from_port', type: 'INTEGER' },
  { name: 'to_port', type: 'INTEGER' },
  { name: 'protocol', type: 'STRING' },
  { name: 'remote_type', type: 'STRING' },
  { name: 'remote', type: 'STRING' },
  { name: 'created_at', type: 'TIMESTAMP' }
];

export const AWS_SECURITY_GROUP_RULES_SCHEMA: Partial<DatabaseSchema> = {
  [BIG_QUERY_TABLES.AWS_SECURITY_GROUP_RULES]: {
    name: BIG_QUERY_TABLES.AWS_SECURITY_GROUP_RULES,
    schema: SCHEMA_FIELDS
  }
};
