import { DatabaseSchema } from '../bigquery/types';
import { BIG_QUERY_TABLES } from '../definitions/constants';

import { AWS_ENDPOINT_SCHEMA } from './aws_endpoint';
import { COMMON_ENTITY_INFO_SCHEMA } from './common_entity_info';
import { CROWDSTRIKE_INFO_SCHEMA } from './crowdstrike_info';
import { MS_DEFENDER_INFO_SCHEMA } from './ms_defender_info';
import { SENTINEL_ONE_INFO_SCHEMA } from './sentinel_one_info';

const SCHEMA_FIELDS = [
  { name: 'id', type: 'STRING' },
  { name: 'name', type: 'STRING' },
  { name: 'type', type: 'STRING' },
  { name: 'integration_type', type: 'STRING' },
  { name: 'is_workload', type: 'BOOLEAN' },
  { name: 'sub_types', type: 'STRING', mode: 'REPEATED' },
  { name: 'risk_score', type: 'FLOAT' },
  { name: 'created_at', type: 'TIMESTAMP' },
  { name: 'timestamp', type: 'TIMESTAMP' },
  { name: 'integration_id', type: 'STRING' },
  {
    name: 'common',
    type: 'RECORD',
    fields: COMMON_ENTITY_INFO_SCHEMA
  },
  {
    name: 'info',
    type: 'RECORD',
    fields: [
      {
        name: 'crowdstrike',
        type: 'RECORD',
        fields: CROWDSTRIKE_INFO_SCHEMA
      },
      {
        name: 'sentinelOne',
        type: 'RECORD',
        fields: SENTINEL_ONE_INFO_SCHEMA
      },
      {
        name: 'aws',
        type: 'RECORD',
        fields: AWS_ENDPOINT_SCHEMA
      },
      {
        name: 'msDefender',
        type: 'RECORD',
        fields: MS_DEFENDER_INFO_SCHEMA
      }
    ]
  },
  { name: 'is_hidden', type: 'BOOL' },
  { name: 'is_active', type: 'BOOL' }
];
export const ENTITIES_SCHEMA: Partial<DatabaseSchema> = {
  [BIG_QUERY_TABLES.ENTITIES]: {
    name: BIG_QUERY_TABLES.ENTITIES,
    schema: SCHEMA_FIELDS,
    clustering: {
      fields: ['id']
    }
  }
};
