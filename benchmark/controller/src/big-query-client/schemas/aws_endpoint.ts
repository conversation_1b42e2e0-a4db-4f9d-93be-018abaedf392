export const AWS_ENDPOINT_SCHEMA = [
  { name: 'endpointType', type: 'STRING' },
  { name: 'accountId', type: 'STRING' },
  { name: 'arn', type: 'STRING' },
  { name: 'privateIp', type: 'STRING' },
  { name: 'publicIp', type: 'STRING' },
  { name: 'networkInterfaceIds', type: 'STRING', mode: 'REPEATED' },
  { name: 'region', type: 'STRING' },
  { name: 'availabilityZone', type: 'STRING' },
  { name: 'vpcId', type: 'STRING' },
  { name: 'subnetId', type: 'STRING' },
  {
    name: 'securityGroups',
    type: 'RECORD',
    fields: [
      { name: 'id', type: 'STRING' },
      { name: 'name', type: 'STRING' }
    ],
    mode: 'REPEATED'
  },
  { name: 'ips', type: 'STRING', mode: 'REPEATED' }
];
