export const SENTINEL_ONE_INFO_SCHEMA = [
  { name: 'id', type: 'STRING' },
  { name: 'accountId', type: 'STRING' },
  { name: 'accountName', type: 'STRING' },
  { name: 'computerName', type: 'STRING' },
  { name: 'agentVers<PERSON>', type: 'STRING' },
  { name: 'osType', type: 'STRING' },
  { name: 'osName', type: 'STRING' },
  { name: 'osRevision', type: 'STRING' },
  { name: 'createdAt', type: 'TIMESTAMP' },
  { name: 'updatedAt', type: 'TIMESTAMP' },
  { name: 'activeThreats', type: 'INTEGER' },
  { name: 'isActive', type: 'BOOLEAN' },
  { name: 'infected', type: 'BOOLEAN' },
  { name: 'lastActiveDate', type: 'TIMESTAMP' },
  {
    name: 'activeDirectory',
    type: 'RECORD',
    fields: [
      { name: 'computerDistinguishedName', type: 'STRING' },
      { name: 'computerMemberOf', type: 'STRING', mode: 'REPEATED' },
      { name: 'lastUserDistinguishedName', type: 'STRING' },
      { name: 'lastUserMemberOf', type: 'STRING', mode: 'REPEATED' },
      { name: 'userPrincipalName', type: 'STRING' }
    ]
  },
  { name: 'appsVulnerabilityStatus', type: 'STRING' },
  {
    name: 'cloudProviders',
    type: 'STRING'
  },
  { name: 'containerizedWorkloadCounts', type: 'INTEGER' },
  { name: 'coreCount', type: 'INTEGER' },
  { name: 'cpuCount', type: 'INTEGER' },
  { name: 'cpuId', type: 'STRING' },
  { name: 'domain', type: 'STRING' },
  { name: 'externalId', type: 'STRING' },
  { name: 'externalIp', type: 'STRING' },
  { name: 'firewallEnabled', type: 'BOOLEAN' },
  { name: 'groupId', type: 'STRING' },
  { name: 'groupIp', type: 'STRING' },
  { name: 'groupName', type: 'STRING' },
  { name: 'isDecommissioned', type: 'BOOLEAN' },
  { name: 'isUninstalled', type: 'BOOLEAN' },
  { name: 'lastLoggedInUserName', type: 'STRING' },
  { name: 'lastSuccessfulScanDate', type: 'TIMESTAMP' },
  { name: 'locationType', type: 'STRING' },
  { name: 'machineSid', type: 'STRING' },
  { name: 'machineType', type: 'STRING' },
  { name: 'modelName', type: 'STRING' },
  { name: 'networkQuarantineEnabled', type: 'BOOLEAN' },
  { name: 'networkStatus', type: 'STRING' },
  { name: 'osArch', type: 'STRING' },
  { name: 'osUsername', type: 'STRING' },
  { name: 'siteId', type: 'STRING' },
  { name: 'siteName', type: 'STRING' },
  { name: 'totalMemory', type: 'INTEGER' },
  { name: 'uuid', type: 'STRING' },
  { name: 'ips', type: 'STRING', mode: 'REPEATED' }
];
