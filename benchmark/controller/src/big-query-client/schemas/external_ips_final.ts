import { DatabaseSchema } from '../bigquery/types';
import { BIG_QUERY_TABLES, DATASETS } from '../definitions/constants';
import { SCHEMA_FIELDS } from './external_ips_staging';

export const EXTERNAL_IPS_FINAL_SCHEMA: Partial<DatabaseSchema> = {
  [BIG_QUERY_TABLES.EXTERNAL_IPS_FINAL]: {
    name: BIG_QUERY_TABLES.EXTERNAL_IPS_FINAL,
    schema: SCHEMA_FIELDS,
    clustering: {
      fields: ['start_ip', 'end_ip']
    },
    datasetId: DATASETS.COMMON
  }
};
