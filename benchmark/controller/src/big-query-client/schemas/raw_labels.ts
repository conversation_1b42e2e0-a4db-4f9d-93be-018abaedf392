import { DatabaseSchema } from '../bigquery/types';
import { BIG_QUERY_TABLES } from '../definitions/constants';

export const VERSIONING_FIELDS = [
  { name: 'created_at', type: 'TIMESTAMP' },
  { name: 'action', type: 'STRING' }
];

const SCHEMA_FIELDS = [
  { name: 'id', type: 'STRING' },
  { name: 'type', type: 'STRING' },
  { name: 'key', type: 'STRING' },
  { name: 'description', type: 'STRING' },
  { name: 'icon', type: 'STRING', mode: 'NULLABLE' },
  { name: 'color', type: 'STRING', mode: 'NULLABLE' },
  { name: 'values', type: 'STRING', mode: 'REPEATED' },
  ...VERSIONING_FIELDS
];

export const RAW_LABELS_SCHEMA: Partial<DatabaseSchema> = {
  [BIG_QUERY_TABLES.RAW_LABELS]: {
    name: BIG_QUERY_TABLES.RAW_LABELS,
    schema: SCHEMA_FIELDS
  }
};
