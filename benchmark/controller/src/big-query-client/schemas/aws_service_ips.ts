import { DatabaseSchema } from '../bigquery/types';
import { BIG_QUERY_TABLES, DATASETS } from '../definitions/constants';

const SCHEMA_FIELDS = [
  { name: 'prefix', type: 'STRING', mode: 'NULLABLE' },
  { name: 'region', type: 'STRING', mode: 'REQUIRED' },
  { name: 'service', type: 'STRING', mode: 'REQUIRED' },
  { name: 'sync_token', type: 'STRING', mode: 'REQUIRED' },
  { name: 'created_at', type: 'TIMESTAMP' },
  { name: 'start_ip', type: 'INTEGER', mode: 'REQUIRED' },
  { name: 'end_ip', type: 'INTEGER', mode: 'REQUIRED' },
  { name: 'mask_len', type: 'INTEGER', mode: 'REQUIRED' }
];

export const AWS_SERVICES_IPS_SCHEMA: Partial<DatabaseSchema> = {
  [BIG_QUERY_TABLES.AWS_IPS_RANGES]: {
    name: BIG_QUERY_TABLES.AWS_IPS_RANGES,
    schema: SCHEMA_FIELDS,
    clustering: {
      fields: ['start_ip', 'mask_len']
    },
    datasetId: DATASETS.COMMON
  }
};
