import { DatabaseSchema } from '../bigquery/types';
import { BIG_QUERY_TABLES, DATASETS } from '../definitions/constants';
import { VERSIONING_FIELDS } from './raw_labels';

const SCHEMA_FIELDS = [
  { name: 'entityId', type: 'STRING', mode: 'REQUIRED' },
  {
    name: 'labelIds',
    type: 'STRING',
    mode: 'REPEATED'
  },
  ...VERSIONING_FIELDS
];

export const RAW_LABELS_ASSIGNMENTS_SCHEMA: Partial<DatabaseSchema> = {
  [BIG_QUERY_TABLES.RAW_LABEL_ASSIGNMENTS]: {
    name: BIG_QUERY_TABLES.RAW_LABEL_ASSIGNMENTS,
    schema: SCHEMA_FIELDS
  }
};
