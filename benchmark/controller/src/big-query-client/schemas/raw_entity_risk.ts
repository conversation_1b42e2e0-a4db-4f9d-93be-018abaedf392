import { DatabaseSchema } from '../bigquery/types';
import { BIG_QUERY_TABLES } from '../definitions/constants';

const SCHEMA_FIELDS = [
  { name: 'entity_id', type: 'STRING', mode: 'REQUIRED' },
  { name: 'risk_score', type: 'FLOAT64', mode: 'REQUIRED' },
  { name: 'created_at', type: 'TIMESTAMP', mode: 'REQUIRED' }
];

export const RAW_ENTITY_RISK_SCHEMA: Partial<DatabaseSchema> = {
  [BIG_QUERY_TABLES.RAW_ENTITY_RISK]: {
    name: BIG_QUERY_TABLES.RAW_ENTITY_RISK,
    schema: SCHEMA_FIELDS,
    clustering: {
      fields: ['created_at']
    }
  }
};
