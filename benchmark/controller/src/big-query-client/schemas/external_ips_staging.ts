import { DatabaseSchema } from '../bigquery/types';
import { BIG_QUERY_TABLES, DATASETS } from '../definitions/constants';

export const SCHEMA_FIELDS = [
  // Common fields for all IP types
  { name: 'ip_range', type: 'STRING', mode: 'REQUIRED' },
  { name: 'start_ip', type: 'STRING', mode: 'REQUIRED' },
  { name: 'end_ip', type: 'STRING', mode: 'REQUIRED' },
  { name: 'ip_version', type: 'INTEGER', mode: 'NULLABLE' },

  // VPN fields
  { name: 'is_vpn', type: 'BOOLEAN', mode: 'NULLABLE' },
  { name: 'is_proxy', type: 'BOOLEAN', mode: 'NULLABLE' },
  { name: 'service_name', type: 'STRING', mode: 'NULLABLE' },
  { name: 'service_url', type: 'STRING', mode: 'NULLABLE' },
  { name: 'vpn_exit_node_type', type: 'STRING', mode: 'NULLABLE' },
  { name: 'vpn_exit_node_region', type: 'STRING', mode: 'NULLABLE' },
  { name: 'last_seen', type: 'STRING', mode: 'NULLABLE' },
  { name: 'last_seen_str', type: 'STRING', mode: 'NULLABLE' },
  { name: 'hostname', type: 'STRING', mode: 'NULLABLE' },
  { name: 'country_code', type: 'STRING', mode: 'NULLABLE' },
  { name: 'city_name', type: 'STRING', mode: 'NULLABLE' },
  { name: 'latitude', type: 'FLOAT', mode: 'NULLABLE' },
  { name: 'longitude', type: 'FLOAT', mode: 'NULLABLE' },

  // Hosting fields
  { name: 'hosting_domain', type: 'STRING', mode: 'NULLABLE' },
  { name: 'datacenter', type: 'STRING', mode: 'NULLABLE' },

  // ASN fields
  { name: 'asn', type: 'STRING', mode: 'NULLABLE' },
  { name: 'route', type: 'STRING', mode: 'NULLABLE' },
  { name: 'descr', type: 'STRING', mode: 'NULLABLE' },
  { name: 'active', type: 'BOOLEAN', mode: 'NULLABLE' },
  { name: 'org', type: 'STRING', mode: 'NULLABLE' },
  { name: 'abuse', type: 'STRING', mode: 'NULLABLE' },
  { name: 'asn_type', type: 'STRING', mode: 'NULLABLE' },
  { name: 'asn_created', type: 'STRING', mode: 'NULLABLE' },
  { name: 'asn_updated', type: 'STRING', mode: 'NULLABLE' },
  { name: 'rir', type: 'STRING', mode: 'NULLABLE' },
  { name: 'country', type: 'STRING', mode: 'NULLABLE' },
  { name: 'asn_domain', type: 'STRING', mode: 'NULLABLE' },
  { name: 'asn_abuser_score', type: 'STRING', mode: 'NULLABLE' },

  // Abuser fields
  { name: 'is_abuser', type: 'BOOLEAN', mode: 'NULLABLE' },
  { name: 'is_tor', type: 'BOOLEAN', mode: 'NULLABLE' },

  { name: 'created_at', type: 'TIMESTAMP' }
];

export const EXTERNAL_IPS_STAGING_SCHEMA: Partial<DatabaseSchema> = {
  [BIG_QUERY_TABLES.EXTERNAL_IPS_STAGING]: {
    name: BIG_QUERY_TABLES.EXTERNAL_IPS_STAGING,
    schema: SCHEMA_FIELDS,
    clustering: {
      fields: ['start_ip', 'end_ip']
    },
    datasetId: DATASETS.STAGING
  }
};
