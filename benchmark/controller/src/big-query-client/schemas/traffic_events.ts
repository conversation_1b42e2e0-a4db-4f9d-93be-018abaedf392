import { DatabaseSchema } from '../bigquery/types';
import { BIG_QUERY_TABLES } from '../definitions/constants';

const SCHEMA_FIELDS = [
  { name: 'src_id', type: 'STRING' },
  { name: 'src_name', type: 'STRING' },
  { name: 'src_addr', type: 'STRING' },
  { name: 'src_process', type: 'STRING' },
  { name: 'src_cmd', type: 'STRING' },
  { name: 'src_type', type: 'STRING' },
  { name: 'dst_id', type: 'STRING' },
  { name: 'dst_name', type: 'STRING' },
  { name: 'dst_addr', type: 'STRING' },
  { name: 'dst_process', type: 'STRING' },
  { name: 'dst_cmd', type: 'STRING' },
  { name: 'dst_type', type: 'STRING' },
  { name: 'port', type: 'INT64' },
  { name: 'time', type: 'TIMESTAMP' },
  { name: 'id', type: 'STRING' },
  { name: 'session_count', type: 'INT64' },
  { name: 'created_at', type: 'TIMESTAMP' },
  { name: 'action', type: 'STRING' },
  { name: 'alert', type: 'BOOLEAN' }
];

export const TRAFFIC_EVENTS_SCHEMA: Partial<DatabaseSchema> = {
  [BIG_QUERY_TABLES.TRAFFIC_EVENTS]: {
    name: BIG_QUERY_TABLES.TRAFFIC_EVENTS,
    schema: SCHEMA_FIELDS,
    clustering: {
      fields: ['src_id', 'dst_id', 'src_addr', 'dst_addr']
    }
  }
};
