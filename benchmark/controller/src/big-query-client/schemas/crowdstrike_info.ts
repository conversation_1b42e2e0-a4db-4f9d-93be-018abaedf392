export const CROWDSTRIKE_INFO_SCHEMA = [
  { name: 'agentLoadFlags', type: 'STRING' },
  { name: 'agentLocalTime', type: 'TIMESTAMP' },
  { name: 'agentVersion', type: 'STRING' },
  { name: 'biosManufacturer', type: 'STRING' },
  { name: 'biosVersion', type: 'STRING' },
  { name: 'cid', type: 'STRING' },
  { name: 'configIdBase', type: 'STRING' },
  { name: 'configIdBuild', type: 'STRING' },
  { name: 'configIdPlatform', type: 'STRING' },
  { name: 'cpuSignature', type: 'STRING' },
  { name: 'deviceId', type: 'STRING' },
  { name: 'externalIp', type: 'STRING' },
  { name: 'firstSeen', type: 'TIMESTAMP' },
  { name: 'hostname', type: 'STRING' },
  { name: 'instanceId', type: 'STRING' },
  { name: 'lastLoginTimestamp', type: 'TIMESTAMP' },
  { name: 'localIp', type: 'STRING' },
  { name: 'mac<PERSON>dd<PERSON>', type: 'STRING' },
  { name: 'majorVersion', type: 'STRING' },
  { name: 'minorVersion', type: 'STRING' },
  { name: 'osVersion', type: 'STRING' },
  { name: 'platformId', type: 'STRING' },
  { name: 'platformName', type: 'STRING' },
  { name: 'productTypeDesc', type: 'STRING' },
  { name: 'provisionStatus', type: 'STRING' },
  { name: 'firewallEnabled', type: 'BOOLEAN' },
  { name: 'reducedFunctionalityMode', type: 'STRING' },
  { name: 'serialNumber', type: 'STRING' },
  { name: 'serviceProvider', type: 'STRING' },
  { name: 'serviceProviderAccountId', type: 'STRING' },
  { name: 'status', type: 'STRING' },
  { name: 'systemManufacturer', type: 'STRING' },
  { name: 'systemProductName', type: 'STRING' },
  { name: 'zoneGroup', type: 'STRING' },
  { name: 'ips', type: 'STRING', mode: 'REPEATED' },
  { name: 'machineDomain', type: 'STRING' },
  { name: 'podId', type: 'STRING' },
  { name: 'title', type: 'STRING' },
  { name: 'slowChangingModifiedTimestamp', type: 'TIMESTAMP' },
  { name: 'notes', type: 'STRING' },
  { name: 'siteName', type: 'STRING' },
  { name: 'email', type: 'STRING' },
  { name: 'buildNumber', type: 'STRING' },
  { name: 'ou', type: 'STRING', mode: 'REPEATED' } // could be possibly empty
];
