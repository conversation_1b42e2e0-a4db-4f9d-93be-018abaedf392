export const MS_DEFENDER_INFO_SCHEMA = [
  { name: 'tenantId', type: 'STRING' },
  { name: 'timestamp', type: 'TIMESTAMP' },
  { name: 'deviceId', type: 'STRING' },
  { name: 'deviceName', type: 'STRING' },
  { name: 'clientVersion', type: 'STRING' },
  { name: 'publicIp', type: 'STRING' },
  { name: 'osArchitecture', type: 'STRING' },
  { name: 'osPlatform', type: 'STRING' },
  { name: 'osBuild', type: 'INTEGER' },
  { name: 'isAzureAdJoined', type: 'BOOLEAN' },
  { name: 'joinType', type: 'STRING' },
  { name: 'loggedOnUsers', type: 'STRING' },
  { name: 'registryDeviceTag', type: 'STRING' },
  { name: 'osVersion', type: 'STRING' },
  { name: 'machineGroup', type: 'STRING' },
  { name: 'reportId', type: 'INTEGER' },
  { name: 'onboardingStatus', type: 'STRING' },
  { name: 'additionalFields', type: 'STRING' },
  { name: 'deviceCategory', type: 'STRING' },
  { name: 'deviceType', type: 'STRING' },
  { name: 'deviceSubtype', type: 'STRING' },
  { name: 'model', type: 'STRING' },
  { name: 'vendor', type: 'STRING' },
  { name: 'osDistribution', type: 'STRING' },
  { name: 'osVersionInfo', type: 'STRING' },
  { name: 'mergedDeviceIds', type: 'STRING' },
  { name: 'mergedToDeviceId', type: 'STRING' },
  { name: 'isInternetFacing', type: 'BOOLEAN' },
  { name: 'sensorHealthState', type: 'STRING' },
  { name: 'isExcluded', type: 'BOOLEAN' },
  { name: 'exclusionReason', type: 'STRING' },
  { name: 'exposureLevel', type: 'STRING' },
  { name: 'assetValue', type: 'STRING' },
  { name: 'deviceManualTags', type: 'STRING' },
  { name: 'deviceDynamicTags', type: 'STRING' },
  { name: 'hardwareUuid', type: 'STRING' },
  { name: 'cloudPlatforms', type: 'STRING', mode: 'REPEATED' },
  { name: 'azureVmId', type: 'STRING' },
  { name: 'azureResourceId', type: 'STRING' },
  { name: 'azureVmSubscriptionId', type: 'STRING' },
  { name: 'gcpFullResourceName', type: 'STRING' },
  { name: 'awsResourceName', type: 'STRING' },
  { name: 'isTransient', type: 'INTEGER' },
  { name: 'osBuildRevision', type: 'STRING' },
  { name: 'hostDeviceId', type: 'STRING' },
  { name: 'mitigationStatus', type: 'STRING' },
  { name: 'connectivityType', type: 'STRING' },
  { name: 'discoverySources', type: 'STRING' },
  { name: 'macAddress', type: 'STRING' },
  { name: 'ips', type: 'STRING', mode: 'REPEATED' }
];
