import { DatabaseSchema } from '../bigquery/types';
import { BIG_QUERY_TABLES } from '../definitions/constants';

const SCHEMA_FIELDS = [
  { name: 'id', type: 'STRING' },
  { name: 'name', type: 'STRING' },
  { name: 'description', type: 'STRING' },
  { name: 'account_id', type: 'STRING' },
  { name: 'region', type: 'STRING' },
  { name: 'created_at', type: 'TIMESTAMP' }
];

export const AWS_SECURITY_GROUP_SCHEMA: Partial<DatabaseSchema> = {
  [BIG_QUERY_TABLES.AWS_SECURITY_GROUPS]: {
    name: BIG_QUERY_TABLES.AWS_SECURITY_GROUPS,
    schema: SCHEMA_FIELDS
  }
};
