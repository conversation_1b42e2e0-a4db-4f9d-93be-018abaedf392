import { DatabaseSchema } from '../bigquery/types';
import { BIG_QUERY_TABLES } from '../definitions/constants';

const SCHEMA_FIELDS = [
  // Detection info (will be repeated for each control result)
  { name: 'detection_id', type: 'STRING', mode: 'REQUIRED' },
  { name: 'control_id', type: 'STRING', mode: 'REQUIRED' },
  { name: 'category', type: 'STRING', mode: 'REQUIRED' },
  { name: 'title', type: 'STRING', mode: 'REQUIRED' },
  { name: 'sub_title', type: 'STRING', mode: 'NULLABLE' },
  { name: 'explanation', type: 'STRING', mode: 'NULLABLE' },
  { name: 'severity', type: 'STRING', mode: 'REQUIRED' },
  { name: 'created_time', type: 'TIMESTAMP', mode: 'NULLABLE' },
  { name: 'updated_time', type: 'TIMESTAMP', mode: 'NULLABLE' },
  { name: 'tags', type: 'STRING', mode: 'REPEATED' },
  { name: 'related_entities_ids', type: 'STRING', mode: 'REPEATED' },

  // Confidence info
  { name: 'confidence_score', type: 'INT64', mode: 'NULLABLE' },
  { name: 'confidence_explanation', type: 'STRING', mode: 'NULLABLE' },
  { name: 'confidence_risk_factors', type: 'STRING', mode: 'REPEATED' },
  { name: 'confidence_critical_entities', type: 'STRING', mode: 'REPEATED' },
  { name: 'confidence_entity_types', type: 'STRING', mode: 'REPEATED' },

  // Control result info (unique per row)
  { name: 'control_result_id', type: 'STRING', mode: 'REQUIRED' },
  { name: 'session_count', type: 'INT64', mode: 'REQUIRED' },
  { name: 'port', type: 'INT64', mode: 'REQUIRED' },

  // Source entity info
  { name: 'src_id', type: 'STRING', mode: 'REQUIRED' },
  { name: 'src_name', type: 'STRING', mode: 'REQUIRED' },
  { name: 'src_addr', type: 'STRING', mode: 'REQUIRED' },
  { name: 'src_process', type: 'STRING', mode: 'REQUIRED' },
  { name: 'src_cmd', type: 'STRING', mode: 'NULLABLE' },
  { name: 'src_type', type: 'STRING', mode: 'REQUIRED' },

  // Destination entity info
  { name: 'dst_id', type: 'STRING', mode: 'REQUIRED' },
  { name: 'dst_name', type: 'STRING', mode: 'REQUIRED' },
  { name: 'dst_addr', type: 'STRING', mode: 'REQUIRED' },
  { name: 'dst_process', type: 'STRING', mode: 'REQUIRED' },
  { name: 'dst_cmd', type: 'STRING', mode: 'NULLABLE' },
  { name: 'dst_type', type: 'STRING', mode: 'REQUIRED' },

  { name: 'created_at', type: 'TIMESTAMP' }
];

export const DETECTIONS_SCHEMA: Partial<DatabaseSchema> = {
  [BIG_QUERY_TABLES.DETECTIONS]: {
    name: BIG_QUERY_TABLES.DETECTIONS,
    schema: SCHEMA_FIELDS,
    clustering: {
      fields: ['detection_id', 'category', 'confidence_score', 'created_time']
    }
  }
};
