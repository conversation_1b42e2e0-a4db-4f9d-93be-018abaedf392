export const COMMON_ENTITY_INFO_SCHEMA = [
  { name: 'osName', type: 'STRING' },
  { name: 'osVersion', type: 'STRING' },
  { name: 'deviceType', type: 'STRING' },
  { name: 'cloudProvider', type: 'STRING' },
  { name: 'deployment', type: 'STRING' },
  { name: 'ipAddresses', type: 'STRING', mode: 'REPEATED' },
  { name: 'macAddresses', type: 'STRING', mode: 'REPEATED' },
  { name: 'manufacturer', type: 'STRING' },
  { name: 'cloudAccountId', type: 'STRING' },
  { name: 'cloudRegion', type: 'STRING' },
  { name: 'cloudAvailabilityZone', type: 'STRING' },
  { name: 'cloudVpc', type: 'STRING' },
  { name: 'activeDirectoryDomain', type: 'STRING' },
  { name: 'activeDirectoryOu', type: 'STRING', mode: 'REPEATED' },
  { name: 'lastLoggedInUser', type: 'STRING' },
  { name: 'tags', type: 'STRING' }
];
