import { DatabaseSchemas } from '../bigquery/types';
import {
  AWS_SERVICES_IPS_SCHEMA,
  EXTERNAL_IPS_FINAL_SCHEMA,
  EXTERNAL_IPS_STAGING_SCHEMA,
  RAW_ENTITY_RISK_SCHEMA,
  TRAFFIC_EVENTS_SCHEMA,
  ENTITIES_SCHEMA,
  AWS_SECURITY_GROUP_RULES_SCHEMA,
  AWS_SECURITY_GROUP_SCHEMA,
  RAW_LABELS_SCHEMA,
  RAW_LABELS_ASSIGNMENTS_SCHEMA,
  DETECTIONS_SCHEMA,
  SENTINEL_ONE_INFO_SCHEMA
} from '../schemas';

export const BIG_QUERY_TABLE_SCHEMAS = {
  ...AWS_SERVICES_IPS_SCHEMA,
  ...EXTERNAL_IPS_STAGING_SCHEMA,
  ...EXTERNAL_IPS_FINAL_SCHEMA,
  ...RAW_ENTITY_RISK_SCHEMA,
  ...TRAFFIC_EVENTS_SCHEMA,
  ...ENTITIES_SCHEMA,
  ...AWS_SECURITY_GROUP_RULES_SCHEMA,
  ...AWS_SECURITY_GROUP_SCHEMA,
  ...RAW_LABELS_SCHEMA,
  ...RAW_LABELS_ASSIGNMENTS_SCHEMA,
  ...DETECTIONS_SCHEMA,
  ...SENTINEL_ONE_INFO_SCHEMA
} as DatabaseSchemas;
