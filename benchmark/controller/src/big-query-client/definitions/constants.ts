export const DATASETS = {
  COMMON: 'common',
  STAGING: 'staging'
} as const;

export const BIG_QUERY_TABLES = {
  TRAFFIC_EVENTS: 'raw_traffic',
  ENTITIES: 'raw_entities',
  AWS_SECURITY_GROUP_RULES: 'raw_aws_security_group_rules',
  AWS_SECURITY_GROUPS: 'raw_aws_security_groups',
  AWS_IPS_RANGES: 'aws_ips_ranges',
  EXTERNAL_IPS_STAGING: `external_ips_staging`,
  EXTERNAL_IPS_FINAL: `external_ips`,
  RAW_ENTITY_RISK: 'raw_entity_risk',
  RAW_LABELS: 'raw_labels',
  RAW_LABEL_ASSIGNMENTS: 'raw_labels_assignments',
  DETECTIONS: 'detections'
} as const;

export const DEFAULT_BATCH_SIZE = 10000;
export const CHUNK_SIZE = DEFAULT_BATCH_SIZE - 1;
export const MAX_RETRIES = 3;
export const RETRY_DELAY = 1000;
export const CONCURRENCY = 10;
export const READ_LIMIT = 100000;
export const HYDRATION_FUNCTION_MAX_EVENTS = 100 * 1000;

export const GCLOUD_SA_SCOPES = [
  'https://www.googleapis.com/auth/bigquery',
  'https://www.googleapis.com/auth/cloud-platform',
  'https://www.googleapis.com/auth/iam'
];

export const GCLOUD_IMPERSONATED_SCOPES = [
  'https://www.googleapis.com/auth/bigquery',
  'https://www.googleapis.com/auth/cloud-platform'
];

export const ROLES = {
  TOKEN_CREATOR: 'roles/iam.serviceAccountTokenCreator',
  BQ_DATA_VIEWER: 'roles/bigquery.dataViewer'
} as const;

export const SERVICE_ACCOUNT = `steampipe-benchmark-sa@${process.env.GCLOUD_PROJECT}.iam.gserviceaccount.com`;

// BigQuery pricing constants
export const BIGQUERY_PRICING = {
  COST_PER_TB: 6.25, // $6.25 per TB processed (as of 2024)
  BYTES_PER_TB: 1024 * 1024 * 1024 * 1024 // 1 TB in bytes
} as const;
