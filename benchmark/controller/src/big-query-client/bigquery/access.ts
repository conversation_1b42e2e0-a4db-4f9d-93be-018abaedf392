import type { GaxiosResponse } from 'gaxios';
import { google } from 'googleapis';
import type { iam_v1 } from 'googleapis';
import { BigQuery, Policy, Table } from '@google-cloud/bigquery';
import type { Compute, JWT } from 'google-auth-library';
import { GoogleAuth } from 'google-auth-library';

import {
  generateServiceAccountEmail,
  generateServiceAccountResourcePath
} from './utils';
import { HandleServiceAccountSetUpErrorsParams } from './types';
import {
  ROLES,
  GCLOUD_SA_SCOPES,
  SERVICE_ACCOUNT
} from '../definitions/constants';
import { ITableAccessGrantParams } from './types';

export type AuthClient = JWT | Compute;
const iam = google.iam('v1');

export async function grantTableAccess({
  datasetId,
  tableId,
  serviceAccountEmail
}: ITableAccessGrantParams): Promise<void> {
  try {
    const bigquery = new BigQuery();

    const table: Table = bigquery.dataset(datasetId).table(`${tableId}`);
    // Get current IAM policy
    const [policy] = await table.getIamPolicy();

    // Define the new binding
    const newBinding = {
      role: ROLES.BQ_DATA_VIEWER,
      members: [`serviceAccount:${serviceAccountEmail}`]
    };

    // Ensure role binding exists
    policy.bindings = policy.bindings || [];
    const existingBindingIndex = policy.bindings.findIndex(
      (b) => b.role === newBinding.role
    );

    if (existingBindingIndex > -1) {
      // If role exists, add new member if not already included
      const members = new Set(policy.bindings[existingBindingIndex]!.members);
      if (members.has(`serviceAccount:${serviceAccountEmail}`)) {
        return;
      }

      members.add(`serviceAccount:${serviceAccountEmail}`);
      policy.bindings[existingBindingIndex]!.members = [...members];
    } else {
      // Add new role binding
      policy.bindings.push(newBinding);
    }

    // Set the updated IAM policy
    await table.setIamPolicy(policy as Policy);
  } catch (error: any) {
    throw new Error(`Failed to grant table access`);
  }
}

export const grantImpersonationRoles = async ({
  errorMessage,
  organizationId
}: HandleServiceAccountSetUpErrorsParams): Promise<boolean> => {
  try {
    if (errorMessage?.includes('unable to impersonate:')) {
      const sourceClient = await fetchSourceClient();

      // Setup impersonation
      await grantTokenCreatorRole(
        sourceClient,
        generateServiceAccountEmail(organizationId)
      );

      return true;
    } else {
      return false;
    }
  } catch (error: any) {
    throw new Error(
      `Failed to grant impersonation permissions to runner service account`
    );
  }
};

async function grantTokenCreatorRole(
  authClient: AuthClient,
  leastPrivillegedServiceAccountEmail: string
): Promise<GaxiosResponse<iam_v1.Schema$Policy>> {
  try {
    const resourcePath = generateServiceAccountResourcePath(
      leastPrivillegedServiceAccountEmail
    );

    const policy = (
      await iam.projects.serviceAccounts.getIamPolicy({
        auth: authClient,
        resource: resourcePath
      })
    ).data;

    if (!policy.bindings) policy.bindings = [];

    let binding = policy.bindings.find(
      (b: any) => b.role === ROLES.TOKEN_CREATOR
    );
    if (binding) {
      if (binding.members?.includes(`serviceAccount:${SERVICE_ACCOUNT}`)) {
        return { data: policy } as GaxiosResponse<iam_v1.Schema$Policy>;
      }
    } else {
      binding = { role: ROLES.TOKEN_CREATOR, members: [] };
      policy.bindings.push(binding);
    }

    binding.members?.push(`serviceAccount:${SERVICE_ACCOUNT}`);

    const request = {
      auth: authClient,
      resource: resourcePath,
      requestBody: {
        policy: policy
      }
    };

    return await iam.projects.serviceAccounts.setIamPolicy(request);
  } catch (error: any) {
    throw new Error(`Failed to grant token creator role: ${error.message}`);
  }
}

export async function fetchSourceClient(): Promise<AuthClient> {
  try {
    const auth = new GoogleAuth({
      scopes: GCLOUD_SA_SCOPES
    });

    const sourceClient = await auth.getClient();

    return sourceClient as AuthClient;
  } catch (error: any) {
    throw new Error(`Failed to fetch source client: ${error.message}`);
  }
}
