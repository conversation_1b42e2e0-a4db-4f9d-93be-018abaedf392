import { BigQuery } from '@google-cloud/bigquery';
import PQueue from 'p-queue';
import pRetry from 'p-retry';
import Handlebars from 'handlebars';

import { BaseClient } from '../base/client';
import {
  CONCURRENCY,
  CHUNK_SIZE,
  MAX_RETRIES,
  RETRY_DELAY,
  READ_LIMIT,
  BIGQUERY_PRICING
} from '../definitions/constants';
import {
  BigQueryWriteOptions,
  BigQueryWriterConfig,
  BigQueryWriteResult,
  TableSchema,
  TableName,
  CollectionName,
  Collections,
  QueryFilters,
  ExecuteQueryParams
} from './types';

export class BigQueryClient extends BaseClient {
  bigquery: BigQuery;
  private tableSchemas: Record<string, TableSchema>;

  constructor(config: BigQueryWriterConfig) {
    super(config);
    this.bigquery = new BigQuery({
      projectId: config.projectId,
      ...(config.authClient
        ? { authClient: config.authClient }
        : { keyFilename: config.credentialsPath })
    });
    this.tableSchemas = config.tableSchemas || {};
  }

  async initialize(): Promise<void> {
    const dataset = this.bigquery.dataset(this.config.datasetId);

    const [datasetExists] = await dataset.exists();
    if (!datasetExists) {
      await this.bigquery.createDataset(this.config.datasetId, {
        location: this.config.location || 'US'
      });
    }

    // Create tables from provided schemas
    for (const tableSchema of Object.values(this.tableSchemas)) {
      await this.createTable(tableSchema.name, tableSchema.schema);
    }
  }

  /**
   * Registers a new table schema
   * @param tableName The name of the table
   * @param schema The BigQuery schema for the table
   */
  registerTableSchema(tableName: string, schema: any[]): void {
    this.tableSchemas[tableName] = {
      name: tableName as any,
      schema
    };
  }

  /**
   * Creates a table if it doesn't exist
   * @param tableName The name of the table
   * @param schema The BigQuery schema for the table
   * @param clustering Optional clustering configuration
   */
  async createTable(tableName: string, schema: any[]): Promise<void> {
    const dataset = this.bigquery.dataset(this.config.datasetId);
    const table = dataset.table(tableName);

    const [exists] = await table.exists();
    if (!exists) {
      const tableOptions: any = { schema };
      const tableSchema = this.tableSchemas[tableName];
      if (tableSchema?.clustering) {
        tableOptions.clustering = tableSchema.clustering;
      }
      await dataset.createTable(tableName, tableOptions);
    }
  }

  /**
   * Executes a BigQuery SQL query
   * @param query The SQL query to execute
   * @param params The parameters to pass to the query
   * @returns Query results
   */
  async executeQuery(
    query: string,
    params: Record<string, any> = {}
  ): Promise<any> {
    try {
      const [job] = await this.bigquery.createQueryJob({ query, params });
      const [rows] = await job.getQueryResults();
      return rows;
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Gets a reference to a BigQuery table
   * @param tableName The name of the table
   * @returns BigQuery table object
   */
  getTable(tableName: string): any {
    const dataset = this.bigquery.dataset(this.config.datasetId);
    return dataset.table(tableName);
  }

  /**
   * Gets data from a BigQuery table
   * @param tableName The name of the table
   * @param limit Maximum number of records to retrieve (default: 100)
   * @returns Array of table records
   */
  async get(tableName: string, limit: number = 100): Promise<any[]> {
    const dataset = this.bigquery.dataset(this.config.datasetId);
    const table = dataset.table(tableName);

    const [exists] = await table.exists();
    if (!exists) {
      throw new Error(
        `Table ${tableName} does not exist in dataset ${this.config.datasetId}`
      );
    }

    const query = `SELECT * FROM \`${this.config.datasetId}.${tableName}\` ORDER BY created_at DESC LIMIT ${limit}`;

    try {
      const [rows] = await this.bigquery.query(query);
      return rows;
    } catch (error) {
      console.error(`❌ Error retrieving data from table ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Truncates a BigQuery table
   * @param tableName The name of the table to truncate
   */
  async truncate(tableName: string): Promise<void> {
    const query = `TRUNCATE TABLE \`${this.config.datasetId}.${tableName}\``;
    await this.bigquery.query(query);
    console.info(`✅ Successfully truncated table: ${tableName}`);
  }

  /**
   * Deletes a BigQuery table
   * @param tableName The name of the table to delete
   */
  async deleteTable(tableName: string): Promise<void> {
    const dataset = this.bigquery.dataset(this.config.datasetId);
    const table = dataset.table(tableName);

    const [exists] = await table.exists();
    if (!exists) {
      console.info(`Table ${tableName} does not exist, no need to delete`);
      return;
    }

    await table.delete();
    console.info(`✅ Successfully deleted table: ${tableName}`);
  }

  /**
   * Adds multiple tables to the dataset
   * @param tables Array of table configurations
   */
  async addTables(
    tables: Array<{
      tableName: string;
      schema: any[];
      clustering?: { fields: string[] };
    }>
  ): Promise<void> {
    await Promise.all(
      tables.map(async ({ tableName, schema, clustering }) => {
        await this.createTable(tableName, schema);
        console.info(
          `✅ Successfully added and initialized table: ${tableName}`
        );
      })
    );

    console.info(
      `✅ Successfully added and initialized ${tables.length} tables`
    );
  }

  /**
   * Adds a single table to the dataset
   * @param tableName The name of the table
   * @param schema The BigQuery schema for the table
   * @param clustering Optional clustering configuration
   */
  async addTable(
    tableName: string,
    schema: any[],
    clustering?: { fields: string[] }
  ): Promise<void> {
    await this.addTables([{ tableName, schema, clustering }]);
  }

  /**
   * Creates a table with a timestamp suffix
   * @param baseTableName The base name of the table
   * @param schema The BigQuery schema for the table
   * @param clustering Optional clustering configuration
   * @returns The full name of the created table
   */
  async createTimestampedTable(
    baseTableName: string,
    schema: any[],
    clustering?: { fields: string[] }
  ): Promise<string> {
    const timestamp =
      new Date().toISOString().split('T')[0]?.replace(/-/g, '') || '';
    const tableName = `${baseTableName}_${timestamp}`;

    await this.createTable(tableName, schema);
    console.info(`✅ Successfully created timestamped table: ${tableName}`);
    return tableName;
  }

  /**
   * Checks if a table exists in the dataset
   * @param tableName The name of the table
   * @returns True if the table exists, false otherwise
   */
  async tableExists(tableName: string): Promise<boolean> {
    const dataset = this.bigquery.dataset(this.config.datasetId);
    const table = dataset.table(tableName);
    const [exists] = await table.exists();
    return exists;
  }

  /**
   * Validates a BigQuery SQL query without executing it
   * @param sql The SQL query to validate
   * @returns True if valid, false otherwise
   */
  private async validateQuery(sql: string): Promise<boolean> {
    try {
      // Dry run the query to validate syntax and permissions
      await this.bigquery.createQueryJob({
        query: sql,
        dryRun: true
      });
      return true;
    } catch (error) {
      console.error(`Query validation failed: ${error}`);
      return false;
    }
  }

  /**
   * Runs a BigQuery SQL query
   * @param sql The SQL query to execute
   * @returns Query results
   */
  async runQuery(sql: string): Promise<any[]> {
    const isValid = await this.validateQuery(sql);
    if (!isValid) {
      throw new Error('Query validation failed');
    }

    const [rows] = await this.bigquery.query(sql);
    return rows;
  }

  /**
   * Private method for parallel writing with advanced error handling and retry logic
   * @param tableName The name of the table
   * @param records The records to write
   * @param options Write options
   * @returns Write result statistics
   */
  async parallelWrite(
    tableName: TableName,
    records: any[],
    options: BigQueryWriteOptions = {}
  ): Promise<BigQueryWriteResult> {
    const concurrency = options.concurrency || CONCURRENCY;
    const chunkSize = options.chunkSize || CHUNK_SIZE;
    const maxRetries = options.maxRetries || MAX_RETRIES;
    const retryDelay = options.retryDelay || RETRY_DELAY;

    const errorStats = {
      totalErrors: 0,
      failedChunks: 0,
      successfulChunks: 0,
      totalAttempts: 0,
      errorTypes: new Map<string, number>()
    };

    if (records.length === 0) {
      console.info(`No records to insert into ${tableName}`);
      return {
        totalRecords: 0,
        ...errorStats
      };
    }

    const dataset = this.bigquery.dataset(this.config.datasetId);
    const tableRef = dataset.table(tableName);

    // Use single timestamp for all records
    const createdAt = new Date().toISOString();
    const recordsWithTimestamp = records.map((record) => ({
      ...record,
      created_at: createdAt
    }));

    const totalRecords = recordsWithTimestamp.length;

    const queue = new PQueue({ concurrency });

    const writePromises: Promise<void>[] = [];

    for (let i = 0; i < recordsWithTimestamp.length; i += chunkSize) {
      const chunk = recordsWithTimestamp.slice(i, i + chunkSize);
      const chunkNumber = Math.floor(i / chunkSize) + 1;

      const taskPromise = queue.add(async () => {
        try {
          await pRetry(
            async () => {
              errorStats.totalAttempts++;
              try {
                await tableRef.insert(chunk);
                return true;
              } catch (err: any) {
                const errorType = err.name || 'UnknownError';
                errorStats.errorTypes.set(
                  errorType,
                  (errorStats.errorTypes.get(errorType) || 0) + 1
                );
                errorStats.totalErrors++;

                if (
                  err.name === 'PartialFailureError' &&
                  err.errors &&
                  err.errors.length > 0
                ) {
                  const sampleErrors = err.errors.slice(0, 3);
                  console.warn(
                    `Partial failure in chunk ${chunkNumber}. Sample errors:`
                  );
                  sampleErrors.forEach((error: any) => {
                    if (error.errors && error.errors.length > 0) {
                      error.errors.forEach((e: any) => {
                        console.warn(`  - ${e.reason}: ${e.message}`);
                      });
                    }
                  });
                  console.warn(
                    `Total errors in this chunk: ${err.errors.length}`
                  );
                }

                throw err;
              }
            },
            {
              retries: maxRetries,
              minTimeout: retryDelay,
              onFailedAttempt: (error: any) => {
                console.warn(
                  `Failed to insert chunk ${chunkNumber} to table ${tableName}. ` +
                    `Attempt ${error.attemptNumber} failed. There are ${error.retriesLeft} retries left. ` +
                    `Error: ${error.message}`
                );
              }
            }
          );

          errorStats.successfulChunks++;
        } catch (error) {
          errorStats.failedChunks++;
          console.error(
            `All retries failed for chunk ${chunkNumber}, table ${tableName}: ${error}`
          );

          return;
        }
      });

      writePromises.push(taskPromise);
    }

    await Promise.all(writePromises);

    const errorSummary = Array.from(errorStats.errorTypes.entries())
      .map(([type, count]) => `${type}: ${count}`)
      .join(', ');

    const baseMessage =
      `🎉 Finished BigQuery insert for table: ${tableName}, processed ${totalRecords} records\n` +
      `Stats: ${errorStats.successfulChunks} successful chunks, ${errorStats.failedChunks} failed chunks\n` +
      `Total attempts: ${errorStats.totalAttempts}`;

    const errorDetails =
      errorStats.totalErrors > 0 ? `\nError types: ${errorSummary}` : '';

    console.info(baseMessage + errorDetails);

    if (errorStats.failedChunks > 0) {
      console.warn(
        `Some chunks (${errorStats.failedChunks}) failed to insert, but we're continuing with what we have`
      );
    }

    return {
      totalRecords,
      ...errorStats
    };
  }

  /**
   * Reads data from a BigQuery table with optional filters
   * @param data Object containing collectionName and optional filters
   * @param useLimit Whether to apply the read limit (default: true)
   * @returns Promise resolving to the collection data
   */
  async read<T extends CollectionName>({
    collectionName,
    filters = {},
    useLimit = true
  }: {
    collectionName: T;
    filters?: QueryFilters;
    useLimit?: boolean;
  }): Promise<Collections[T]> {
    try {
      const whereClause = this.buildWhereClause(filters);
      const limitClause = useLimit ? `LIMIT ${READ_LIMIT}` : '';
      const query = `
          SELECT *
          FROM ${this.config.datasetId}.${collectionName} ${whereClause}
          ${limitClause}`;
      const rows = await this.runQuery(query);
      console.info(`BigQuery read from table`, {
        table: collectionName,
        count: rows.length
      });
      return rows as Collections[T];
    } catch (error) {
      console.error(
        `Stopping all operations for table ${collectionName} due to error: ${error}`
      );
      throw error;
    }
  }

  /**
   * Builds a WHERE clause from the provided filters
   * @param filters Object containing filter conditions
   * @returns SQL WHERE clause string
   */
  private buildWhereClause(filters: QueryFilters): string {
    const conditions = Object.entries(filters)
      .filter(([_, value]) => !!value)
      .map(([key, value]) => {
        if (Array.isArray(value)) {
          const list = value
            // number without quotes
            .map((v) => (typeof v === 'number' ? v : `'${v}'`))
            .join(', ');
          return `${key} IN (${list})`;
        }
        // number and boolean without quotes
        if (typeof value === 'number' || typeof value === 'boolean') {
          return `${key} = ${value}`;
        }

        return `${key} = '${value}'`;
      });

    return conditions.length ? `WHERE ${conditions.join(' AND ')}` : '';
  }
  async calculateQueryCost({
    query,
    params = []
  }: ExecuteQueryParams): Promise<{
    totalBytesProcessed: number;
    totalBytesBilled: number;
    estimatedCost: number;
  }> {
    try {
      // Use dry run to get cost estimate without executing the query
      const [job] = await this.bigquery.createQueryJob({
        query,
        params,
        dryRun: true
      });

      const metadata = job.metadata;
      const totalBytesProcessed = metadata.statistics?.totalBytesProcessed || 0;
      const totalBytesBilled = metadata.statistics?.totalBytesBilled || 0;

      // Convert bytes to TB and calculate cost
      const estimatedCost =
        (totalBytesProcessed / BIGQUERY_PRICING.BYTES_PER_TB) *
        BIGQUERY_PRICING.COST_PER_TB;

      return {
        totalBytesProcessed,
        totalBytesBilled,
        estimatedCost
      };
    } catch (error) {
      throw error;
    }
  }
  public getCompileQuery(
    query: string,
    context: { [key: string]: any }
  ): string {
    const getTemplateQuery = Handlebars.compile(query);
    return getTemplateQuery(context);
  }
}
