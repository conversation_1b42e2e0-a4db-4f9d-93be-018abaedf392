import crypto from 'crypto';
import { readFileSync } from 'fs';

function getProjectID(): string {
  if (process.env.GCLOUD_PROJECT) {
    return process.env.GCLOUD_PROJECT;
  }

  const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
  if (!credentialsPath) {
    throw new Error('GOOGLE_APPLICATION_CREDENTIALS not set');
  }

  const credentials = JSON.parse(readFileSync(credentialsPath, 'utf-8'));
  if (!credentials.project_id) {
    throw new Error('Project ID not found in credentials');
  }

  return credentials.project_id;
}

export function generateAccountId(organizationId: string): string {
  return (
    'p' +
    crypto.createHash('md5').update(organizationId).digest('hex').slice(0, 29)
  );
}

export function generateServiceAccountEmail(organizationId: string): string {
  return `${generateAccountId(organizationId)}@${getProjectID()}.iam.gserviceaccount.com`;
}

export function generateServiceAccountResourcePath(
  targetPrincipal: string
): string {
  return `projects/${getProjectID()}/serviceAccounts/${targetPrincipal}`;
}
