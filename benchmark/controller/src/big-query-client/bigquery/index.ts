import { Impersonated } from 'google-auth-library';

import { BigQueryClient } from './writer';
import { BigQueryClientConfig, TableSchema } from './types';
import { GCLOUD_IMPERSONATED_SCOPES } from '../definitions/constants';
import { fetchSourceClient } from './access';

/**
 * Creates a BigQuery client with the specified configuration
 * @param config BigQuery client configuration
 * @returns A configured BigQueryWriter instance
 */
export const getBigQueryClient = async (
  config: BigQueryClientConfig,
  impersonatedServiceAccountEmail: string = ''
) => {
  let auth: { credentialsPath?: string; authClient?: Impersonated } = {
    credentialsPath: config.credentialsPath
  };
  if (impersonatedServiceAccountEmail) {
    const sourceClient = await fetchSourceClient();

    const client = new Impersonated({
      sourceClient: sourceClient,
      targetPrincipal: impersonatedServiceAccountEmail,
      lifetime: 3600,
      delegates: [],
      targetScopes: GCLOUD_IMPERSONATED_SCOPES
    });
    auth = { authClient: client };
  }

  const bigQueryClient = new BigQueryClient({
    projectId: config.projectId || process.env.GCLOUD_PROJECT || 'port0-dev2',
    datasetId: config.datasetId,
    tableSchemas: config.tableSchemas,
    ...auth,
    location: config.location
  });

  if (config.initialize) {
    await bigQueryClient.initialize();
  }

  return bigQueryClient;
};

export { BigQueryClient, TableSchema };
