import { Impersonated } from 'google-auth-library';

import { BIG_QUERY_TABLES } from '../definitions/constants';

export type TableName =
  (typeof BIG_QUERY_TABLES)[keyof typeof BIG_QUERY_TABLES];

export interface Collections {
  [key: string]: any[];
}

export type CollectionName = keyof Collections;

export type QueryFilters = Record<string, any>;

export type CollectionReader<TCollections extends Collections = Collections> = <
  T extends keyof TCollections
>(data: {
  collectionName: T;
  filters?: QueryFilters;
}) => Promise<TCollections[T]>;

export interface BigQueryWriterConfig extends ClientConfig {
  credentialsPath?: string;
  authClient?: Impersonated;
  tableSchemas?: DatabaseSchemas;
}

export type TableSchema = {
  name: TableName;
  schema: any[];
  clustering?: {
    fields: string[];
  };
  datasetId?: string;
};

export type DatabaseSchema = Record<Partial<TableName>, TableSchema>;
export type DatabaseSchemas = Record<TableName, TableSchema>;

export interface ClientConfig {
  datasetId: string;
  projectId: string;
  location?: string;
}

export interface IBigQueryClient {
  initialize?(): Promise<void>;
  executeQuery<T>(query: string): Promise<T>;
}

export interface BaseBigQueryClientConfig {
  projectId?: string;
}

export interface BigQueryClientConfig extends BaseBigQueryClientConfig {
  datasetId: string;
  credentialsPath?: string;
  tableSchemas?: DatabaseSchemas;
  location?: string;
  initialize?: boolean;
}

export interface BigQueryWriteResult {
  totalRecords: number;
  successfulChunks: number;
  failedChunks: number;
  totalErrors: number;
  totalAttempts: number;
  errorTypes: Map<string, number>;
}

export interface BigQueryWriteOptions {
  concurrency?: number;
  chunkSize?: number;
  maxRetries?: number;
  retryDelay?: number;
}

export interface EntityRiskRecord {
  entity_id: string;
  risk_score: number;
}

export interface ITableAccessGrantParams {
  datasetId: string;
  tableId: string;
  serviceAccountEmail: string;
}

export type HandleServiceAccountSetUpErrorsParams = {
  errorMessage: string;
  organizationId: string;
};

export type ExecuteQueryParams = {
  query: string;
  params?: any[];
};
