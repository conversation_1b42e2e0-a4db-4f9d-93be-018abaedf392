import { IBigQueryClient } from '../bigquery/types';
import { ClientConfig } from '../bigquery/types';

/**
 * Abstract base class for BigQuery client implementations.
 * This class provides common functionality and enforces implementation of required methods
 * from the IBigQueryClient interface.
 *
 * @implements {IBigQueryClient}
 */
export abstract class BaseClient implements IBigQueryClient {
  /**
   * Configuration settings for the BigQuery client.
   * Contains project ID, dataset ID, and optional location.
   */
  protected config: ClientConfig;

  /**
   * Creates a new instance of BaseClient.
   *
   * @param {ClientConfig} config - Configuration object containing:
   *   - projectId - The Google Cloud project ID
   *   - datasetId - The BigQuery dataset ID
   *   - location - Optional. The geographic location of the dataset (e.g., "US", "EU")
   */
  constructor(config: ClientConfig) {
    this.config = config;
  }

  /**
   * Initializes the BigQuery client.
   * This method should be implemented by subclasses to handle any necessary setup (dataset, tables, etc.)
   *
   * @returns {Promise<void>} A promise that resolves when initialization is complete
   * @abstract
   */
  abstract initialize(): Promise<void>;

  /**
   * Executes a query on the BigQuery client.
   * This method should be implemented by subclasses to handle any necessary query execution,
   *
   * @returns {Promise<T>} A promise that resolves to the query result
   * @abstract
   */
  abstract executeQuery<T>(query: string): Promise<T>;
}
