import { ZodError, ZodSchema } from 'zod';
import { ValidationError } from './ValidationError';

export type ValidationSuccess<T> = {
  success: true;
  data: T;
};

export type ValidationFailed = {
  success: false;
  errors: Record<string, string[]>;
};

export type ValidationResponse<T> = ValidationSuccess<T> | ValidationFailed;

export function formatZodError(error: ZodError): Record<string, string[]> {
  const formatted = error.format();

  const flatten = (obj: any, path = ''): Record<string, string[]> => {
    const errors: Record<string, string[]> = {};

    for (const key in obj) {
      if (key === '_errors') {
        if (obj[key].length > 0) {
          errors[path || 'root'] = obj[key];
        }
      } else {
        const nested = flatten(obj[key], path ? `${path}.${key}` : key);
        Object.assign(errors, nested);
      }
    }

    return errors;
  };

  return flatten(formatted);
}

export function validateSchema<T>(
  schema: ZodSchema<T>,
  input: unknown,
  throwOnError: boolean = false
): ValidationResponse<T> {
  const result = schema.safeParse(input);

  if (result.success) {
    return { success: true, data: result.data };
  }

  const errors = formatZodError(result.error);

  if (throwOnError) {
    throw new ValidationError(errors);
  }

  return { success: false, errors };
}
