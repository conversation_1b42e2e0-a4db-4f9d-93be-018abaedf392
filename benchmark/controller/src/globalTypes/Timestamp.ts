export interface TimestampLike {
  readonly seconds: number;
  readonly nanoseconds: number;

  toDate(): Date;
  toMillis(): number;
  isEqual(other: TimestampLike): boolean;
  valueOf(): string;
}

export interface TimestampConstructorLike {
  new (seconds: number, nanoseconds: number): TimestampLike;
  now(): TimestampLike;
  fromDate(date: Date): TimestampLike;
  fromMillis(milliseconds: number): TimestampLike;
}
