import { TimestampLike as Timestamp } from './Timestamp';
import { z } from 'zod';
import { EventMode } from './http';
import {
  Entity,
  IntegrationType,
  UniqueTrafficSchema
} from './ndrBenchmarkTypes';
import { AUTO_POLICY_ACTIONS } from './ndrBenchmarkTypes/firewall';

export interface FirewallRule {
  policy: FirewallRulePolicy;
  direction: Direction;
  remoteIdentifiers: Identifier[];
  portRanges: string[];
  alert: boolean;
  notes: string;
  isActive?: boolean;
  isSuggested?: boolean;
  providerId?: string | null;
  entityId?: string | null;
  integrationId?: string | null;
  integrationType?: IntegrationType | null;
  localIdentifier: Identifier | null;
  priorityOrder?: number;
  name?: string | null;
}

export const SENTINEL_MAX_RULES_PER_PAGE = 1000;

// can be a source IP or security group
export const IDENTIFIER_TYPES = {
  LOCAL_IP: 'local IP',
  REMOTE_IP: 'remote IP',
  LOCAL_SECURITY_GROUP: 'Local Security Group',
  REMOTE_SECURITY_GROUP: 'Remote Security Group'
} as const;

export const IdentifierSchema = z.object({
  type: z.enum([
    IDENTIFIER_TYPES.LOCAL_IP,
    IDENTIFIER_TYPES.REMOTE_IP,
    IDENTIFIER_TYPES.LOCAL_SECURITY_GROUP,
    IDENTIFIER_TYPES.REMOTE_SECURITY_GROUP
  ]),
  value: z.string()
});

export type Identifier = z.infer<typeof IdentifierSchema>;

export type IdentifierType =
  (typeof IDENTIFIER_TYPES)[keyof typeof IDENTIFIER_TYPES];

export interface FirewallRulesSet {
  activeRules: FirewallRule[];
  activeSuggestedRules: FirewallRule[];
  disabledRules: FirewallRule[];
  disabledSuggestedRules: FirewallRule[];
}

// Success and error response types for better type safety
export interface FirewallRulesSuccess {
  success: true;
  rules: Record<string, FirewallRulesSet>;
}

export interface FirewallRulesError {
  success: false;
  error: string;
  rules: Record<string, FirewallRulesSet>;
}

export type FirewallRulesResponse = FirewallRulesSuccess | FirewallRulesError;

export type RuleFields = keyof FirewallRule;

export const DIRECTION = {
  INBOUND: 'inbound',
  OUTBOUND: 'outbound',
  ANY: 'any'
} as const;

export type Direction = (typeof DIRECTION)[keyof typeof DIRECTION];

export const FIREWALL_RULE_POLICY = {
  ACCEPT: 'ACCEPT',
  BLOCK: 'BLOCK'
} as const;

export type FirewallRulePolicy =
  (typeof FIREWALL_RULE_POLICY)[keyof typeof FIREWALL_RULE_POLICY];

export type PortRange = {
  start: number;
  end: number;
};

export type ApplyRulesParams = {
  desiredRules: FirewallRule[];
  rulesToDelete: FirewallRule[];
  entity: Entity;
  userId: string;
  organizationId: string;
};

export const ANY_PORT = '1-65535';
export const POLICY_RULE_ACTIONS = {
  ALLOW: 'allow',
  BLOCK: 'block',
  ENABLE_SUGGESTIONS: 'enable_suggestions'
} as const;

export type PolicyRuleAction =
  (typeof POLICY_RULE_ACTIONS)[keyof typeof POLICY_RULE_ACTIONS];

export const FirewallPolicyRuleSchema = z.object({
  ports: z.array(z.string()),
  action: z.nativeEnum(POLICY_RULE_ACTIONS)
});

export type FirewallPolicyRule = z.infer<typeof FirewallPolicyRuleSchema>;

export const PolicyScopeSchema = z.object({
  include: z.array(z.string()).min(1),
  exclude: z.array(z.string())
});

export type PolicyScope = z.infer<typeof PolicyScopeSchema>;

export const PolicyStatsSchema = z.object({
  violationsCount: z.number().optional(),
  hitCount: z.number().optional(),
  trafficCount: z.number().optional(),
  entitiesCount: z.number().optional(),
  lastModified: z.custom<Timestamp>().optional()
});

export type PolicyStats = z.infer<typeof PolicyStatsSchema>;

export const PolicySchema = z
  .object({
    id: z.string(),
    name: z.string(),
    description: z.string(),
    scope: PolicyScopeSchema,
    firewallPolicyRules: z.array(FirewallPolicyRuleSchema),
    enabled: z.boolean(),
    isDeleted: z.boolean().optional(),
    createdAt: z.custom<Timestamp>().optional(),
    enableNotifications: z.boolean().optional(),
    violationNotifications: z.object({
      emailRecipients: z.array(z.string())
    }),
    alertMode: z.boolean(),
    overrideSuggestions: z.boolean().optional(),
    deletedAt: z.custom<Timestamp>().optional(),
    isCleared: z.boolean().optional()
  })
  .strip()
  .refine(
    (data) => data.overrideSuggestions || data.firewallPolicyRules.length > 0,
    {
      message:
        'Firewall policy rules cannot be empty when overrideSuggestions is false'
    }
  )
  .refine(
    (data) =>
      !data.enableNotifications ||
      data.violationNotifications.emailRecipients.length > 0,
    {
      message: 'Email recipients cannot be empty when notifications are enabled'
    }
  );

export type Policy = z.infer<typeof PolicySchema>;

export const PolicyTrafficSchema = UniqueTrafficSchema.extend({
  action: z.nativeEnum(AUTO_POLICY_ACTIONS)
});

export type PolicyTraffic = z.infer<typeof PolicyTrafficSchema>;

export const GetPolicyImpactParamsSchema = z
  .object({
    organizationId: z.string(),
    policyId: z.string().optional(),
    policy: PolicySchema.optional()
  })
  .refine((data) => data.policyId || data.policy, {
    message: 'Either policyId or policy must be provided',
    path: ['policyId', 'policy']
  });

export type GetPolicyImpactParams = z.infer<typeof GetPolicyImpactParamsSchema>;

export const HandlePolicyParamsSchema = z.object({
  organizationId: z.string(),
  policy: PolicySchema,
  operation: z.nativeEnum(EventMode)
});

export type HandlePolicyParams = z.infer<typeof HandlePolicyParamsSchema>;

export const PolicyImpactSchema = z.object({
  entities: z.array(z.custom<Entity>()),
  traffic: z.array(PolicyTrafficSchema),
  rules: z.array(z.custom<FirewallRule>())
});

export type PolicyImpact = z.infer<typeof PolicyImpactSchema>;

export const PolicyImpactClientResponseSchema = z.object({
  entities: z.array(z.string()),
  traffic: z.array(PolicyTrafficSchema),
  totalTraffic: z.number(),
  totalEntities: z.number()
});

export type PolicyImpactClientResponse = z.infer<
  typeof PolicyImpactClientResponseSchema
>;

export type PolicyViolation = {
  id: string;
  policyId: string;
  srcIp: string;
  srcPort: number;
  dstIp: string;
  dstPort: number;
  timestamp: Date;
  createdAt?: Date;
  updatedAt?: Date;
};
