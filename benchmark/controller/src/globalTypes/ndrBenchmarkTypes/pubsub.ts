export const TOPICS = {
  FIREWALL_RISK_CALCULATION: 'calculate-firewall-risk-scores-topic',
  DATA_COLLECTOR: 'data-collector-topic',
  VIOLATIONS_PROCESSING: 'violations-processing-topic',
  CROWDSTRIKE_EVENTS_INGESTION: 'crowdstrike-events-ingestion-topic'
} as const;

export type Topic = (typeof TOPICS)[keyof typeof TOPICS];

export const MESSAGE_TYPES = {
  FIREWALL_RISK_CALCULATION: 'firewall-risk-calculation',
  DATA_COLLECTOR: 'data-collector',
  VIOLATIONS_PROCESSING: 'violations-processing',
  CROWDSTRIKE_EVENTS_INGESTION: 'crowdstrike-events-ingestion'
} as const;

export type MessageType = (typeof MESSAGE_TYPES)[keyof typeof MESSAGE_TYPES];

export const MESSAGE_TOPICS: Record<MessageType, Topic> = {
  [MESSAGE_TYPES.FIREWALL_RISK_CALCULATION]: TOPICS.FIREWALL_RISK_CALCULATION,
  [MESSAGE_TYPES.DATA_COLLECTOR]: TOPICS.DATA_COLLECTOR,
  [MESSAGE_TYPES.VIOLATIONS_PROCESSING]: TOPICS.VIOLATIONS_PROCESSING,
  [MESSAGE_TYPES.CROWDSTRIKE_EVENTS_INGESTION]:
    TOPICS.CROWDSTRIKE_EVENTS_INGESTION
};

export type MessageBodyType =
  | CalculateFirewallRiskScoreMessageBody
  | EventsCollectorMessageBody;

export type CalculateFirewallRiskScoreMessageBody = {
  organizationId: string;
};

export type EventsCollectorMessageBody = {
  organizationId: string;
  eventData: Record<string, any>[];
};

export type MessagePayload = {
  type: MessageType;
  body: MessageBodyType;
};

export type CalculateFirewallRiskScoresPayload = MessagePayload & {
  type: keyof typeof MESSAGE_TYPES.FIREWALL_RISK_CALCULATION;
  body: CalculateFirewallRiskScoreMessageBody;
};

export type ParsedMessage = {
  topicName: Topic;
  messageBody: MessageBodyType;
};
