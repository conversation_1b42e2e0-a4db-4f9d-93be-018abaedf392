import { FirewallRule } from '../../FirewallTypes';
import { EndpointEntity } from './baseTypes';

export type CrowdStrikeEntityType = 'crowdstrike_agent';

export interface CrowdStrikeRuleFormat {
  action: string;
  name: string | null | undefined;
  enabled: boolean | undefined;
  direction: string;
  address_family: string;
  temp_id?: string;
  id?: string | null | undefined;
  family?: string | null | undefined;
  local_address: IpAddress[];
  remote_address: IpAddress[];
  local_port: Port[];
  remote_port: Port[];
  protocol: string;
  description: string | null | undefined;
  monitor: { count: string; period_ms: string } | null;
}
export interface Agent extends EndpointEntity {
  ips: string[];
  agentLoadFlags: number;
  agentLocalTime: string; // ISO datetime string
  agentVersion: string;
  biosManufacturer: string | null;
  biosVersion: string | null;
  buildNumber: string | null;
  cid: string;
  configIdBase: number;
  configIdBuild: number;
  configIdPlatform: number;
  cpuSignature: number;
  detectionSuppressionStatus: string | null;
  deviceId: string;
  devicePolicies: any; // Complex nested object
  email: string | null;
  externalIp: string;
  firstLoginTimestamp: string | null; // ISO datetime string
  firstSeen: string; // ISO datetime string
  hostHiddenStatus: string | null;
  hostname: string;
  instanceId: string | null;
  lastLoginTimestamp: string | null; // ISO datetime string
  lastSeen: string; // ISO datetime string
  localIp: string | null;
  macAddress: string | null;
  machineDomain: string | null;
  majorVersion: number | null;
  minorVersion: number | null;
  managedApps: string | null;
  meta: any; // Could be refined based on structure
  notes: string | null;
  osVersion: string;
  ou: string | null;
  platformId: number;
  platformName: string;
  podId: string | null;
  podName: string | null;
  podNamespace: string | null;
  podServiceAccountName: string | null;
  podHostname: string | null;
  podAnnotations: any | null; // Complex nested object
  podLabels: any | null; // Complex nested object
  podHostIpv4: string | null;
  podHostIpv6: string | null;
  podIpv4: string | null;
  podIpv6: string | null;
  pointerSize: number | null;
  policies: any; // Complex nested object
  productType: number | null;
  productTypeDesc: string | null;
  provisionStatus: string | null;
  reducedFunctionalityMode: string | null;
  releaseGroup: string | null;
  serialNumber: string | null;
  servicePackMajor: string | null;
  servicePackMinor: string | null;
  serviceProvider: string | null;
  serviceProviderAccountId: string | null;
  siteName: string | null;
  slowChangingModifiedTimestamp: string | null; // ISO datetime string
  status: string;
  systemManufacturer: string;
  systemProductName: string;
  tags: any[]; // Array of tags or any type
  zoneGroup: string | null;
  title: string;
  spConnectionName: string;
  spCtx: any; // Context object with nested fields
  ctx: any; // Context object with nested fields
  firewallEnabled: boolean;
}

export type CrowdStrikeInfo = Agent;

export type Host = {
  deviceId: string;
  hostname: string;
  platformId: string;
  platformName: string;
  devicePolicies: DevicePolicies;
};

export type Policy = {
  policyId: string;
  platformId: string;
  defaultInbound: string;
  defaultOutbound: string;
  enforce: boolean;
  testMode: boolean;
  localLogging: boolean;
  ruleGroupIds: string[];
  tracking: string;
};

export type PolicyCombined = {
  id: string;
  cid: string;
  name: string;
  description: string;
  platformName: string;
  groups: HostGroup[];
  enabled: boolean;
};

export type HostGroup = {
  id: string;
  groupType: string;
  name: string;
  description: string;
  assignmentRule: string;
};

export type RuleGroup = {
  id: string;
  customerId: string;
  name: string;
  description: string;
  tracking: string;
  enabled: boolean;
  deleted: boolean;
  ruleIds: string[];
  policyIds: string[];
  platform: string;
};

export type CrowdStrikeRule = {
  id: string;
  family: string;
  name: string;
  description: string;
  enabled: true;
  deleted: boolean;
  direction: string;
  action: string;
  addressFamily: string;
  localAddress: {
    address: string;
    netmask: number;
  }[];
  remoteAddress: {
    address: string;
    netmask: number;
  }[];
  protocol: '6';
  localPort: Port[];
  remotePort: Port[];
  icmp: null;
  monitor: null;
  fqdnEnabled: boolean;
  fqdn: string;
  fields: string[];
  version: number;
  ruleGroup: RuleGroup;
};

export type Port = {
  start: number;
  end: number;
};

export type IpAddress = {
  address: string;
  netmask: number;
};

export type DiffOperation = {
  op: OperationType;
  path: string;
  value?: any;
};
export type handleArrayFieldsData = {
  index?: number;
  ruleIndex: number;
  diffOperations: DiffOperation[];
  newValue: any;
  oldValue: any;
  key: string;
};

export type buildDiffOperationsData = {
  index?: number;
  val?: any;
  ruleIndex: number;
  op: OperationType;
  key: string;
};

export type buildUrlParams = {
  baseUrl: string;
  endpoint: string;
  postfix?: string;
};

export type buildRequestBodyParams = {
  diffOperations?: DiffOperation[];
  ruleGroup: RuleGroup;
};

export const CROWDSTRIKE_RESOURCES_TYPES = {
  RULE_GROUP: 'RULE_GROUP',
  POLICY: 'POLICY',
  HOST_GROUP: 'HOST_GROUP',
  HOST: 'HOST',
  RULE: 'RULE'
} as const;

export type ResourceType =
  (typeof CROWDSTRIKE_RESOURCES_TYPES)[keyof typeof CROWDSTRIKE_RESOURCES_TYPES];

export const DIFF_OPERATIONS_TYPES = {
  ADD: 'add',
  REMOVE: 'remove',
  REPLACE: 'replace'
} as const;

export type OperationType =
  (typeof DIFF_OPERATIONS_TYPES)[keyof typeof DIFF_OPERATIONS_TYPES];

export const FIREWALL_POLICY_ACTIONS = {
  ENABLE: 'enable',
  DISABLE: 'disable'
} as const;

export type PolicyActionType =
  (typeof FIREWALL_POLICY_ACTIONS)[keyof typeof FIREWALL_POLICY_ACTIONS];

export type CrowdStrikeResourceResponseType = {
  resources: any[];
  meta: {
    pagination: {
      offset?: number;
      limit: number;
      total: number;
    };
  };
};

export type CrowdStrikeTokenResponseType = {
  access_token: string;
};

export type CrowdStrikeEnforcePolicyResponseType = {
  meta: {
    writes: {
      resources_affected: number;
    };
  };
};

export const ENDPOINT_OPERATIONS = {
  ENTITY: 'entity',
  ENTITY_V1: 'entity_v1',
  QUERY: 'query'
} as const;

export type EndpointOperation =
  (typeof ENDPOINT_OPERATIONS)[keyof typeof ENDPOINT_OPERATIONS];

export type CrowdStrikeRuleFormatFields = keyof CrowdStrikeRuleFormat;

export type CrowdStrikeRuleFields = keyof CrowdStrikeRuleFormat;

export type DevicePolicies = {
  firewall: any;
  prevention: any;
  contentUpdate: any;
  hostRetention: any;
  remoteResponse: any;
  sensorUpdate: any;
  systemTray: any;
  deviceControl: any;
  globalConfig: any;
};

export type BuildCrowdStrikeRuleParams = {
  action: string;
  direction: string;
  remoteHost: IpAddress[];
  localPort: Port[];
  remotePort: Port[];
  rule: FirewallRule;
  newRule?: boolean;
};
