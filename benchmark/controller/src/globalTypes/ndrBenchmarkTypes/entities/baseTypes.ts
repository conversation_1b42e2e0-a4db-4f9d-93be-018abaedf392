import { SentinelOneInfo } from './sentinelOneTypes';
import { AWSEndpointInfo } from './awsTypes';

import { InternetInfo, InternetScannerInfo } from './internetTypes';
import { CrowdStrikeInfo } from './crowdstrikeTypes';
import { IntegrationType } from './integrationTypes';

import { CrowdStrikeInfoSqlProperties } from '../entities-sql-info/crowdstrike';
import { SentinelOneInfoSqlProperties } from '../entities-sql-info/sentinelOne';
import { CommonEntityInfo } from './commonEntityInfo';
import { MSDefenderInfoSqlProperties } from '../entities-sql-info/msDefender';
import { EntityMetrics } from '../entityMetrics';
import { Label } from '../labels';

export type InternetEntityType = 'internet' | 'internet_scanner';

export type ReferenceEntityType = 'reference';

export type UnrecognizedEntityType = 'unrecognized';
export const BaseEntityTypes = {
  SENTINEL_ONE: 'sentinelOne_agent',
  CROWDSTRIKE: 'crowdstrike_agent',
  AWS_ENDPOINT: 'aws_endpoint',
  MS_DEFENDER_ENDPOINT: 'ms_defender_endpoint',
  INTERNET: 'internet',
  INTERNET_SCANNER: 'internet_scanner',
  REFERENCE: 'reference',
  UNRECOGNIZED: 'unrecognized'
} as const;

export type BaseEntityType =
  (typeof BaseEntityTypes)[keyof typeof BaseEntityTypes];

export interface BaseEntity {
  name: string;
  type: BaseEntityType;
  info:
    | AWSEndpointInfo
    | SentinelOneInfo
    | InternetInfo
    | InternetScannerInfo
    | CrowdStrikeInfo;
}

export interface EndpointEntity extends BaseEntity {
  id: string;
  info: {
    aws?: AWSEndpointInfo;
    crowdstrike?: CrowdStrikeInfoSqlProperties;
    sentinelOne?: SentinelOneInfoSqlProperties;
    msDefender?: MSDefenderInfoSqlProperties;
  };
  common: CommonEntityInfo;
  integrationType: IntegrationType;
  integrationId: string;
  isWorkload?: boolean;
  isActive: boolean;
  subTypes?: string[];
}

export interface Entity extends BaseEntity {
  id: string;
  info:
    | AWSEndpointInfo
    | CrowdStrikeInfoSqlProperties
    | SentinelOneInfoSqlProperties
    | MSDefenderInfoSqlProperties;
  common: CommonEntityInfo;
  integrationType: IntegrationType;
  integrationId: string;
  isWorkload?: boolean;
  isActive: boolean;
  subTypes?: string[];
}

export interface EnrichedEntity extends EndpointEntity {
  metrics?: EntityMetrics;
  insightsCount?: number;
  labels?: Label[];
}

export interface SQLEndpointEntity {
  id: string;
  name: string;
  type: BaseEntityType;
  common: CommonEntityInfo;
  info: {
    aws?: AWSEndpointInfo;
    msDefender?: MSDefenderInfoSqlProperties;
    crowdstrike?: CrowdStrikeInfoSqlProperties;
    sentinelOne?: SentinelOneInfoSqlProperties;
  };
  integration_type: IntegrationType;
  integration_id: string;
  is_workload?: boolean;
  is_active: boolean;
  sub_types?: string[];
  timestamp?: string;
}

// Kept for backwards compatibility
export interface CloudEntity extends EndpointEntity {}
