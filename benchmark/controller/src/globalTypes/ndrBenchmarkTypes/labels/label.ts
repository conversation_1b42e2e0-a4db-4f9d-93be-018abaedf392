export type LabelType = 'static' | 'user' | 'ai';

export const MatchTypes = {
  EQUALS: 'equals',
  CONTAINS: 'contains',
  STARTS_WITH: 'startsWith',
  ENDS_WITH: 'endsWith',
  WILDCARD: 'wildcard',
  CIDR: 'cidr',
  IP_RANGE: 'ip-range'
} as const;

export type MatchType = (typeof MatchTypes)[keyof typeof MatchTypes];

export interface Condition {
  fieldName: string;
  matchType: MatchType;
  value: string | boolean | number;
  include: boolean;
}

export interface BasicLabel {
  id: string;
  values: string[];
  type: LabelType;
  key: string;
  description?: string;
  userId?: string;

  // fe usage
  createdAt?: string;
  updatedAt?: string;
  icon?: string;
  color?: string;
}

export interface UserLabel extends BasicLabel {
  type: 'user';
  conditions?: Condition[];
}

export interface AILabel extends BasicLabel {
  type: 'ai';
}

export interface StaticLabel extends BasicLabel {
  type: 'static';
}

export type Label = UserLabel | AILabel | StaticLabel;

export interface LabelAssignment {
  labelIds: string[];
  entityId: string;
}
