export interface MSDefenderInfoSqlProperties {
  tenantId: string;
  timestamp: string; // isoString
  deviceId: string;
  deviceName: string;
  clientVersion: string;
  publicIp: string;
  osArchitecture: string;
  osPlatform: string;
  osBuild?: number;
  isAzureAdJoined: boolean;
  joinType: string;
  loggedOnUsers: string; // jsonStringifiedArray
  registryDeviceTag?: string;
  osVersion: string;
  machineGroup?: string;
  reportId: number;
  onboardingStatus: string;
  additionalFields: string; // jsonStringifiedArray
  deviceCategory: string;
  deviceType: string;
  deviceSubtype?: string;
  model?: string;
  vendor?: string;
  osDistribution: string;
  osVersionInfo: string;
  mergedDeviceIds?: string;
  mergedToDeviceId?: string;
  isInternetFacing?: boolean;
  sensorHealthState: string;
  isExcluded: boolean;
  exclusionReason?: string;
  exposureLevel: string;
  assetValue?: string;
  deviceManualTags?: string;
  deviceDynamicTags?: string;
  hardwareUuid: string;
  cloudPlatforms: string[];
  azureVmId?: string;
  azureResourceId?: string;
  azureVmSubscriptionId?: string;
  gcpFullResourceName?: string;
  awsResourceName?: string;
  isTransient?: number | null;
  osBuildRevision?: string;
  hostDeviceId?: string;
  mitigationStatus?: string;
  connectivityType: string;
  discoverySources: string;
  macAddress: string;
  ips: string[];
}
