import { FirewallPolicyRule, PolicyScope } from '../FirewallTypes';

export const EVENT_TYPES = {
  RULE_CHANGE: 'Rule Change',
  SCOPE_CHANGE: 'Scope Change',
  POLICY_CREATED: 'Policy Created'
} as const;

export const EVENT_NAMES = {
  RULE_CREATED: 'Rule Created',
  RULE_REMOVED: 'Rule Removed',
  POLICY_ENABLED: 'Policy Enabled',
  POLICY_DISABLED: 'Policy Disabled',
  POLICY_CONFIGURATION_CHANGED: 'Policy configuration changed',
  OVERRIDE_RULE_ADDED: 'Override rule added',
  OVERRIDE_RULE_REMOVED: 'Override rule removed',
  ADDED_TO_SCOPE: 'Added to scope',
  REMOVED_FROM_SCOPE: 'Removed from scope',
  ADDED_TO_EXCEPTIONS: 'Added to exceptions',
  REMOVED_FROM_EXCEPTIONS: 'Removed from exceptions',
  POLICY_CREATED: 'Policy Created'
} as const;

export type EventType = (typeof EVENT_TYPES)[keyof typeof EVENT_TYPES];
export type EventName = (typeof EVENT_NAMES)[keyof typeof EVENT_NAMES];

export type DocumentPolicyAuditsParams = {
  policyId: string;
  organizationId: string;
  eventType: EventType;
  eventName: EventName;
  userId: string;
  name?: string;
  description?: string;
  entities?: string[];
  scope?: PolicyScope;
  emailNotification?: boolean;
  emailRecipients?: string[];
  alertsEnabled?: boolean;
  rules?: FirewallPolicyRule[];
  overrideSuggestions?: boolean;
};
