import { Severity } from './issueTypes';

export type NotificationKeywordType =
  | 'entity'
  | 'issue'
  | 'port'
  | 'query'
  | 'unrecognized_entity';

export type NotificationKeyword = {
  value: string; // When the keyword is an entity, this is the entity ID
  text: string; // The text to display in the notification
  type: NotificationKeywordType;
};

export interface Notification {
  title: string;
  message: string;
  keywords: Array<NotificationKeyword>;
  mainKeyword: NotificationKeyword;
  severity: Severity;
  time: string;
}
