import { CloudEntity } from './entityTypes';
import { Severity } from './issueTypes';
import { UniqueTraffic } from './trafficPatternTypes';

export const DETECTION_STATUS = {
  OPEN: 'open',
  RESOLVED: 'resolved',
  DISMISSED: 'dismissed'
} as const;

export const ORIGIN_TYPES = {
  PORT0: 'port0',
  USER: 'user'
} as const;

export type DetectionStatus =
  (typeof DETECTION_STATUS)[keyof typeof DETECTION_STATUS];
export type OriginType = (typeof ORIGIN_TYPES)[keyof typeof ORIGIN_TYPES];

export const DETECTION_TAG_TYPES = {
  SRC_TYPE: 'src_type',
  SRC_NAME: 'src_name',
  SRC_PROCESS: 'src_process',
  SRC_ADDR: 'src_addr',
  SRC_CMD: 'src_cmd',
  DST_TYPE: 'dst_type',
  DST_NAME: 'dst_name',
  DST_PROCESS: 'dst_process',
  DST_ADDR: 'dst_addr',
  DST_CMD: 'dst_cmd',
  PORT: 'port'
} as const;

export type DetectionTagType =
  (typeof DETECTION_TAG_TYPES)[keyof typeof DETECTION_TAG_TYPES];

export type DetectionControl = {
  id: string;
  name: string;
  category: string;
  query: string;
  subTitle: string;
  explanation: string;
  sequelizedQuery: string | null;
  minThreshold: number;
  severity: Severity;
  emailNotify: boolean;
  origin: OriginType;
  active: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  interval: number;
  lastRunAt?: Date;
};

export type Detection = {
  id: string;
  controlId: string;
  category: string;
  title: string;
  subTitle: string;
  explanation: string;
  severity: Severity;
  relatedEntities?: CloudEntity[];
  relatedEntitiesIds: string[];
  status: DetectionStatus;
  createdTime?: Date;
  updatedTime?: Date;
  userId?: string;
  tags: string[];
};

export type DetectionWithControlResults = Detection & {
  controlResults: UniqueTraffic[];
};

export type DetectionControlMetric = {
  id: string;
  hitCount: number;
};

export type StaticDetectionControl = {
  id?: string;
  name: string;
  category: string;
  query: string;
  sequelizedQuery?: string;
  minThreshold?: number;
  severity?: Severity;
  subTitle?: string;
  explanation?: string;
  emailNotify?: boolean;
  origin?: OriginType;
  active?: boolean;
  createdBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
  interval?: number;
  lastRunAt?: Date;
};

export interface IDetectionControlChanges {
  create: DetectionControl[];
  update: DetectionControl[];
  delete: DetectionControl[];
}

export type EntityAnalysis = {
  criticalEntities: string[];
  entityTypes: string[];
};

export type DetectionConfidence = {
  confidenceScore: number;
  explanation: string;
  riskFactors: string[];
  entityAnalysis: EntityAnalysis;
};
export type DetectionRecord = {
  // Detection info (will be repeated for each control result)
  detection_id: string;
  control_id: string;
  category: string;
  title: string;
  sub_title?: string;
  explanation?: string;
  severity: string;
  created_time?: Date;
  updated_time?: Date;
  tags: string[];
  related_entities_ids: string[];

  // Confidence info
  confidence_score?: number;
  confidence_explanation?: string;
  confidence_risk_factors?: string[];
  confidence_critical_entities?: string[];
  confidence_entity_types?: string[];

  // Control result info (unique per row)
  control_result_id: string;
  session_count: number;
  port: number;

  // Source entity info
  src_id: string;
  src_name: string;
  src_addr: string;
  src_process: string;
  src_cmd?: string;
  src_type: string;

  // Destination entity info
  dst_id: string;
  dst_name: string;
  dst_addr: string;
  dst_process: string;
  dst_cmd?: string;
  dst_type: string;

  created_at?: Date;
};
