export type BarChartStatData = Array<{ key: string; data: number }>;

export type InstancesStatData = {
  sentinelOne: number;
  activeDirectory: number;
  aws: number;
  azure: number;
  gcp: number;
  esxi: number;
};

export type StatsType =
  | 'employee_diverse_connections'
  | 'critical_entities_to_segment'
  | 'instances'
  | 'most_connected_entities';

export type NotificationStatsType =
  | 'notifications_bar_chart'
  | 'notifications_count_last_week'
  | 'notifications_count_last_24h'
  | 'notifications_line_graph'
  | 'notifications_pie_chart'
  | 'notifications_total_count';

export const CONNECTION_STAT_TYPES = {
  DATABASE_CONNECTIONS: 'database_connections',
  REMOTE_DESKTOP: 'remote_desktop',
  INTERNET_TRAFFIC: 'internet_traffic'
} as const;

export type ConnectionStatType =
  (typeof CONNECTION_STAT_TYPES)[keyof typeof CONNECTION_STAT_TYPES];

export type Stat = {
  title: string;
  type: StatsType | NotificationStatsType | ConnectionStatType;
  chartType: 'bar' | 'instances' | 'connection';
  data: BarChartStatData | InstancesStatData | number;
  subCollectionName?: string;
};

export type NetworkRiskScoreData = {
  timestamp: Date;
  score: number;
  entity_risk_95p: number;
  conn_risk_95p: number;
  high_risk_conn_ratio: number;
  entity_count: number;
  connection_count: number;
};
