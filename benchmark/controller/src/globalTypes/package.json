{"name": "globaltypes", "version": "1.0.0", "description": "this repo includes all of the types shared between the front and the back end. This is to ensure that the types are consistent between the two.", "main": "index.js", "scripts": {"prettier-check": "prettier --check \"**/*.ts\"", "prettier-write": "prettier --write \"**/*.ts\""}, "repository": {"type": "git", "url": "git+https://github.com/port0-io/globalTypes.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/port0-io/globalTypes/issues"}, "homepage": "https://github.com/port0-io/globalTypes#readme", "devDependencies": {"prettier": "^3.4.2"}, "dependencies": {"zod": "^3.25.28"}}