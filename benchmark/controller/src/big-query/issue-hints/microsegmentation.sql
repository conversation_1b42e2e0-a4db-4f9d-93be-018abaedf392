WITH priority_categories AS (
    SELECT * FROM UNNEST([
        STRUCT(1 as priority, 'Database Ports' as category, [3306, 5432, 1433, 1434, 1521, 27017, 6379] as ports),  -- Most critical: DB ports (direct data access)
        STRUCT(2 as priority, 'SSH Access' as category, [22] as ports),  -- SSH (primary admin access)
        STRUCT(3 as priority, 'Web Services' as category, [80, 443, 3000, 8080, 8081, 8443, 8888] as ports),  -- Web services (primary attack surface)
        STRUCT(4 as priority, 'Remote Desktop' as category, [3389] as ports),  -- RDP (should be restricted)
        STRUCT(5 as priority, 'Authentication Services' as category, [88] as ports),  -- <PERSON><PERSON><PERSON> (internal auth service)
        STRUCT(6 as priority, 'Remote Management' as category, [5900, 5985, 5986] as ports),  -- VNC, WinRM (system management)
        STRUCT(7 as priority, 'File Sharing' as category, [445, 137, 138, 139] as ports),  -- SMB/NetBIOS (common target but less critical)
        STRUCT(8 as priority, 'Other Services' as category, [21, 23, 10005, 111, 5353] as ports)  -- Remaining services
    ])
),
port_priority AS (
    SELECT port, priority, category as service
    FROM priority_categories
    CROSS JOIN UNNEST(ports) as port
),
-- First, identify entities that meet the criteria
eligible_entities AS (
    SELECT 
        traffic.dst_id AS entity_id,
        MIN(p.priority) as min_priority,
        COUNT(*) as traffic_count
    FROM `{{dataset}}.traffic_patterns` AS traffic
    INNER JOIN port_priority p ON traffic.port = p.port
    WHERE traffic.dst_type != 'unrecognized'
    GROUP BY traffic.dst_id
    HAVING COUNT(*) < 20  -- Pre-filter here
),
-- Then, only aggregate for eligible entities
traffic_groups AS (
    SELECT 
        ARRAY_AGG(
            STRUCT(
                traffic.session_count,
                traffic.src_id,
                traffic.src_name,
                traffic.src_type,
                traffic.src_process,
                traffic.src_addr,
                traffic.dst_id,
                traffic.dst_name,
                traffic.dst_type,
                traffic.dst_process,
                traffic.dst_addr,
                traffic.port AS port,
                GENERATE_UUID() AS id
            )
        ) AS traffic_patterns,
        traffic.dst_id AS entity_id,
        eligible.min_priority
    FROM `{{dataset}}.traffic_patterns` AS traffic
    INNER JOIN port_priority p ON traffic.port = p.port
    INNER JOIN eligible_entities eligible ON traffic.dst_id = eligible.entity_id
    WHERE traffic.dst_type != 'unrecognized'
    GROUP BY traffic.dst_id, eligible.min_priority
)
SELECT 
    STRUCT(
        traffic_groups.traffic_patterns AS trafficPatterns,
        'Traffic on critical ports' AS hint
    ) AS issue_hint
FROM traffic_groups
JOIN `{{dataset}}.entities` AS entity ON traffic_groups.entity_id = entity.id
WHERE entity.is_workload = true
ORDER BY traffic_groups.min_priority ASC