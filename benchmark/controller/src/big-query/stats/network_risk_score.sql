-- Entity Risk Percentile
WITH entity_risk_stats AS (
    SELECT
        APPROX_QUANTILES(risk_score, 100)[OFFSET(95)] AS entity_risk_95p,
        COUNT(*) AS entity_count
    FROM
        `{{dataset}}.entity_risk`
),
-- Recent Traffic Risk Percentile and Counts
traffic_stats AS (
    SELECT
        APPROX_QUANTILES(risk_score, 100) [OFFSET(95)] AS conn_risk_95p,
        COUNTIF(risk_score >= @high_risk_threshold) AS high_risk_count,
        COUNT(*) AS total_conn_count
    FROM
        `{{dataset}}.recent_traffic`
),
-- Combine for Final Score
combined AS (
    SELECT
        entity_risk_95p,
        conn_risk_95p,
        SAFE_DIVIDE(
            LOG(high_risk_count + 1),
            LOG(total_conn_count + 1)
        ) AS risk_ratio,
        entity_count,
        total_conn_count
    FROM
        entity_risk_stats,
        traffic_stats
),
-- Final Score Computation (Normalized 0-100)
final_score AS (
    SELECT
        CURRENT_TIMESTAMP() AS timestamp,
        ROUND(
            GREATEST(
                LEAST(
                    @w1 * entity_risk_95p + @w2 * conn_risk_95p + @w3 * 100 * risk_ratio,
                    -- Scale ratio to 0–100
                    100.0
                ),
                0.0
            ),
            2
        ) AS score,
        entity_risk_95p,
        conn_risk_95p,
        ROUND(risk_ratio, 4) AS high_risk_conn_ratio,
        entity_count,
        total_conn_count AS connection_count
    FROM
        combined
) -- Output the result
SELECT
    *
FROM
    final_score;