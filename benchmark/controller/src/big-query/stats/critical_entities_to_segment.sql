WITH score_entity AS (
    SELECT
        traffic.dst_id AS entity,
        traffic.port,
        COALESCE(
            CAST(
              COALESCE(`{{dataset}}.port_score`(traffic.port), 1) AS BIGNUMERIC
            ) / NULLIF(COUNT(DISTINCT traffic.src_addr), 0),
            0
        ) AS score
    FROM
        `{{dataset}}.traffic_patterns` AS traffic
    WHERE
        traffic.dst_type != 'unrecognized'
    GROUP BY
        traffic.dst_id,
        traffic.port
),
entity_scores AS (
    SELECT 
        entity,
        CAST(SUM(score) as INT64) AS total_score
    FROM 
        score_entity
    GROUP BY 
        entity
    ORDER BY 
        total_score DESC
),
-- This part is only filtering out scores where the entity is no longer exists in sentinelOne
entity_scores_filtered AS (
    SELECT 
        entity_scores.entity,
        entity_scores.total_score
    FROM 
        entity_scores
    INNER JOIN 
        `{{dataset}}.entities` AS entity 
        ON entity.id = entity_scores.entity
    ORDER BY 
        entity_scores.total_score DESC
    LIMIT 10
)
SELECT
    STRUCT(
        'Critical Entities to Segment' AS title,
        'critical_entities_to_segment' AS type,
        'bar' AS chartType,
        ARRAY_AGG(
            STRUCT(
                entity AS key,
                CAST(total_score AS INT64) AS data
            )
        ) AS data
    ) AS stat
FROM entity_scores_filtered;
