WITH stat_table AS (
    SELECT 
        src_id AS entity,
        COUNT(DISTINCT dst_addr) AS value
    FROM `{{dataset}}.traffic_patterns` AS traffic   
    INNER JOIN 
        `{{dataset}}.entities` AS src_entity
        ON src_entity.id = traffic.src_id
    WHERE
            src_type != 'unrecognized' 
        AND dst_type != 'unrecognized'
        AND src_entity.is_workload = false
    GROUP BY 
        src_id
    ORDER BY 
        value DESC
    LIMIT 10
)
SELECT 
    STRUCT(
        'Employee With Most Diverse Connections' AS title,
        'employee_diverse_connections' AS type,
        'bar' AS chartType,
        ARRAY_AGG(
            STRUCT(
                stat_table.entity as key,
                stat_table.value as data
            )
        ) AS data
    ) AS stat
FROM stat_table;
