WITH connection_types AS (
  SELECT 'Database' AS connection_type UNION ALL
  SELECT 'Remote Desktop' UNION ALL 
  SELECT 'Internet' UNION ALL
  SELECT 'OTHER'
),
stat_table AS (
  SELECT
    ct.connection_type,
    COALESCE(COUNT(tp.port), 0) AS connection_count
  FROM connection_types ct
  LEFT JOIN `{{dataset}}.traffic_patterns` tp ON
    CASE
      WHEN tp.port IN (3306, 5432, 1433, 1434, 1521, 27017, 6379) THEN 'Database'
      WHEN tp.port = 3389 THEN 'Remote Desktop' 
      WHEN tp.src_type = 'internet' THEN 'Internet'
      ELSE 'OTHER'
    END = ct.connection_type
  GROUP BY ct.connection_type
)
SELECT 
  STRUCT(
    'connections_type' AS type,
    'Connection Types' AS title,
    'connection' AS chartType,
    ARRAY_AGG(
      STRUCT(
        CASE
          WHEN connection_type = 'Database' THEN 'database_connections'
          WHEN connection_type = 'Remote Desktop' THEN 'remote_desktop'
          WHEN connection_type = 'Internet' THEN 'internet_traffic'
          ELSE 'other'
        END AS type,
        connection_type AS key,
        connection_count AS data
      )
    ) AS data
  ) AS stat
FROM stat_table;
