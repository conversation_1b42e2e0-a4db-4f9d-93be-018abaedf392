WITH stat_table AS (
    SELECT 
        dst_id AS entity,
        COUNT(DISTINCT src_id) AS value
    FROM `{{dataset}}.traffic_patterns` AS traffic   
    WHERE
            src_type != 'unrecognized' 
        AND dst_type != 'unrecognized'
    GROUP BY 
        dst_id
    ORDER BY 
        value DESC
    LIMIT 10
)
SELECT 
    STRUCT(
        'Most Connected Entities' AS title,
        'most_connected_entities' AS type,
        'bar' AS chartType,
        ARRAY_AGG(
            STRUCT(
                stat_table.entity as key,
                stat_table.value as data
            )
        ) AS data
    ) AS stat
FROM stat_table;
