WITH entity_subtype_unnested AS (
    SELECT type, subtype
    FROM
        `{{dataset}}.entities`,  -- Table name first
        UNNEST(sub_types) AS subtype                   -- UNNEST comes after a comma
),
type_totals AS (
    SELECT type, CAST(NULL AS STRING) AS subtype, COUNT(*) AS count
    FROM `{{dataset}}.entities` 
    GROUP BY type
),
aggregated_data AS (
    SELECT 
        ARRAY_AGG(
            STRUCT(
                type,
                subtype as subType,
                count
            )
        ) AS data
    FROM (
        SELECT type, subtype, COUNT(*) AS count
        FROM entity_subtype_unnested
        GROUP BY type, subtype
        UNION ALL
        SELECT type, subtype, count
        FROM type_totals
    ) aggregated
)
SELECT 
    STRUCT(
        'Instances' AS title,
        'instances' AS type,
        'instances' AS chartType,
        data
    ) AS stat
FROM aggregated_data;
