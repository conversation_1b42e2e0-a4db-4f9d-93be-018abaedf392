WITH ip_data AS (
    SELECT entity_id, ARRAY_AGG(DISTINCT ip) AS ips
    FROM (
             SELECT src_id AS entity_id, src_addr AS ip
             FROM `{{dataset}}.complete_traffic_patterns`
             WHERE src_addr IS NOT NULL
             UNION ALL
             SELECT dst_id AS entity_id, dst_addr AS ip
             FROM `{{dataset}}.complete_traffic_patterns`
             WHERE dst_addr IS NOT NULL
         ) all_ips
             JOIN `{{dataset}}.entities` e ON all_ips.entity_id = e.id
    GROUP BY entity_id
),
    port_data AS (
        SELECT dst_id AS entity_id, ARRAY_AGG(DISTINCT port) AS ports
        FROM `{{dataset}}.complete_traffic_patterns`
        WHERE dst_type NOT IN ('unrecognized', 'internet', 'aws_service')
        GROUP BY dst_id
    ),
     process_data AS (
         SELECT dst_id AS entity_id, ARRAY_AGG(DISTINCT dst_process IGNORE NULLS) AS processes
         FROM `{{dataset}}.complete_traffic_patterns`
         WHERE dst_type NOT IN ('unrecognized', 'internet', 'aws_service')
           -- exclude common well known processes
         AND dst_process NOT IN ('svchost.exe', 'lsass.exe', 'dns.exe')
           -- threshold
         AND session_count > 10
         GROUP BY dst_id
     )

SELECT e.id AS entityId, ip_data.ips, port_data.ports, process_data.processes
FROM `{{dataset}}.entities` e
         LEFT JOIN ip_data ON e.id = ip_data.entity_id
         LEFT JOIN port_data ON e.id = port_data.entity_id
         LEFT JOIN process_data ON e.id = process_data.entity_id
WHERE ip_data.ips IS NOT NULL OR port_data.ports IS NOT NULL OR process_data.processes IS NOT NULL;
