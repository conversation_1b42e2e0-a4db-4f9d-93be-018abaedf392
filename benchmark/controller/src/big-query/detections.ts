import { DetectionWithControlResults } from '@globalTypes/ndrBenchmarkTypes';
import { DetectionWithConfidence } from '../benchmark/controls/DetectionConfidenceControl';
import { getBigQueryClient } from '@big-query-client/bigquery';
import { BIG_QUERY_TABLE_SCHEMAS } from '@big-query-client/definitions/databaseSchemas';
import { BIG_QUERY_TABLES } from '@big-query-client/definitions/constants';
import { DatabaseSchemas, TableSchema } from '@big-query-client/bigquery/types';
import * as config from '../config';
import log from '../logger';
import { DetectionRecord } from '@globalTypes/ndrBenchmarkTypes/detectionTypes';

function mapDetectionToBigQueryDetectionSchema(
  detection: DetectionWithControlResults | DetectionWithConfidence
): DetectionRecord[] {
  const records: DetectionRecord[] = [];

  // Safety check: ensure controlResults exists and is not empty
  if (!detection.controlResults || detection.controlResults.length === 0) {
    log.debug(
      `Detection ${detection.id} has no controlResults, skipping BigQuery sync`
    );
    return records;
  }

  // For each control result in the detection, create a separate row
  for (const controlResult of detection.controlResults) {
    const record: DetectionRecord = {
      // Detection info
      detection_id: detection.id,
      control_id: detection.controlId,
      category: detection.category,
      title: detection.title,
      sub_title: detection.subTitle,
      explanation: detection.explanation,
      severity: detection.severity,
      created_time: detection.createdTime
        ? (detection.createdTime as any).toDate?.() ||
          (detection.createdTime instanceof Date
            ? detection.createdTime
            : new Date(detection.createdTime as any))
        : undefined,
      updated_time: detection.updatedTime
        ? (detection.updatedTime as any).toDate?.() ||
          (detection.updatedTime instanceof Date
            ? detection.updatedTime
            : new Date(detection.updatedTime as any))
        : undefined,
      tags: detection.tags || [],
      related_entities_ids: detection.relatedEntitiesIds || [],

      // Confidence info
      confidence_score: (detection as DetectionWithConfidence).confidence
        ?.confidenceScore,
      confidence_explanation: (detection as DetectionWithConfidence).confidence
        ?.explanation,
      confidence_risk_factors:
        (detection as DetectionWithConfidence).confidence?.riskFactors || [],
      confidence_critical_entities:
        (detection as DetectionWithConfidence).confidence?.entityAnalysis
          ?.criticalEntities || [],
      confidence_entity_types:
        (detection as DetectionWithConfidence).confidence?.entityAnalysis
          ?.entityTypes || [],

      // Control result info
      control_result_id: controlResult.id,
      session_count: controlResult.sessionCount,
      port: controlResult.port,

      // Source entity info
      src_id: controlResult.src.id,
      src_name: controlResult.src.name,
      src_addr: controlResult.src.addr,
      src_process: controlResult.src.process,
      src_cmd: controlResult.src.cmd,
      src_type: controlResult.src.type,

      // Destination entity info
      dst_id: controlResult.dst.id,
      dst_name: controlResult.dst.name,
      dst_addr: controlResult.dst.addr,
      dst_process: controlResult.dst.process,
      dst_cmd: controlResult.dst.cmd,
      dst_type: controlResult.dst.type
    };

    records.push(record);
  }

  return records;
}

export async function syncAllDetectionsToBigQuery(
  detections?: (DetectionWithControlResults | DetectionWithConfidence)[]
) {
  if (detections === undefined || (detections && detections.length === 0))
    return;

  const tableName = BIG_QUERY_TABLES.DETECTIONS;
  const orgId = config.Global.organizationId;

  // Transform detections to raw records
  const records: DetectionRecord[] = [];
  for (const detection of detections) {
    const detectionRecords = mapDetectionToBigQueryDetectionSchema(detection);
    records.push(...detectionRecords);
  }

  const tableSchema = BIG_QUERY_TABLE_SCHEMAS[tableName];
  if (!tableSchema) {
    throw new Error(`Table schema not found for table: ${tableName}`);
  }

  const tableSchemas: Record<string, TableSchema> = {
    [tableName]: tableSchema
  };
  const bigQueryClient = await getBigQueryClient({
    datasetId: orgId,
    tableSchemas: tableSchemas as DatabaseSchemas,
    initialize: true
  });

  await bigQueryClient.parallelWrite(tableName, records);

  const insertedCount = records.length;
  log.info(
    `Successfully inserted ${insertedCount} detection control result records into ${tableName}`
  );
}
