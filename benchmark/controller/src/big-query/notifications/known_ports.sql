-- Known ports notification
-- This query is used to generate notifications for known ports when connection was found. 
-- SSH, RDP, MySQL, PostgreSQL, etc.

WITH traffic AS (
    SELECT *
    FROM `{{dataset}}.traffic`
    WHERE
        time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL '60' MINUTE)
        AND dst_type != 'unrecognized'
        AND port IN (
            3306, 5432, 1433, 1434, 1521, 27017, 6379, 22, 3000, 8081, 8443, 8888, 3389, 5900, 5985, 5986
        )
        ORDER BY time DESC
        LIMIT 10
)
SELECT
    STRUCT(
        FORMAT(
            '%s %s',
            `{{dataset}}.port_name`(traffic.port),
            CASE
                WHEN src_type = 'unrecognized' THEN 'from Unrecognized source'
                WHEN src_entity.is_workload = TRUE THEN 'Service Connection'
                WHEN src_type = 'internet' THEN 'Internet Connection'
                ELSE 'Employee Connection'
            END
        ) AS title,

        FORMAT( -- message
            '%s Connection was made from %s to %s',
            `{{dataset}}.port_name`(traffic.port),
            traffic.src_name,
            traffic.dst_name
        ) AS message,

        STRUCT( -- main keyword
            FORMAT(
                'operator=and&dst.name=%s&src.name=%s&port=%s',
                dst_name,
                src_name,
                CAST(traffic.port AS STRING)  -- Corrected line: CAST port to STRING
            ) AS value,
            'Traffic Query' AS text,
            'query' as type
        ) AS mainKeyword,

        [ -- keywords
            STRUCT(
                traffic.dst_id AS value,
                traffic.dst_name AS text,
                'entity' AS type
            ),
            STRUCT(
                traffic.src_id AS value,
                traffic.src_name AS text,
                'entity' AS type
            ),
            STRUCT(
                CAST(traffic.port AS STRING) AS value,
                `{{dataset}}.port_name`(traffic.port) AS text,
                'port' AS type
            )
        ] AS keywords,

        'medium' AS severity,
        traffic.time AS time
    ) as notification
FROM traffic
LEFT JOIN `{{dataset}}.entities` AS src_entity ON src_entity.id = traffic.src_id
LEFT JOIN `{{dataset}}.entities` AS dst_entity ON dst_entity.id = traffic.dst_id