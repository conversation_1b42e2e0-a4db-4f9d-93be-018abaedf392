SELECT
  STRUCT(
    g.id           AS id,
    g.name         AS name,
    g.description  AS description,
    g.account_id   AS account_id,
    g.region       AS region,
    ARRAY_AGG(
      STRUCT(
        r.id           AS id,
        r.description  AS description,
        r.direction    AS direction,
        r.from_port    AS from_port,
        r.to_port      AS to_port,
        r.protocol     AS protocol,
        r.remote_type  AS remote_type,
        r.remote       AS remote,
        r.created_at   AS created_at
      )
      ORDER BY r.created_at
    ) AS rules
  ) AS securityGroup
FROM
  `{{dataset}}.aws_security_groups` AS g
LEFT JOIN
  `{{dataset}}.aws_security_group_rules` AS r
  ON r.security_group_id = g.id
GROUP BY
  g.id,
  g.name,
  g.description,
  g.account_id,
  g.region;
