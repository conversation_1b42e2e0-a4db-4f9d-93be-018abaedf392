import { Label, LabelAssignment } from '@globalTypes/ndrBenchmarkTypes';
import BigQueryLabelControl, {
  BigQueryLabelControlConfig
} from '../benchmark/controls/BigQueryLabelControl';
import log from '../logger';

export async function syncAllLabelsToBigQuery(labels?: Label[]) {
  if (labels === undefined || (labels && labels.length === 0)) return;
  const config: BigQueryLabelControlConfig = {
    dataType: 'labels',
    data: labels,
    action: 'insert'
  };

  const labelControl = new BigQueryLabelControl(config);
  try {
    const results = await labelControl.execute();
    log.info('Labels inserted successfully to bigQuery:', results.length);
    return results;
  } catch (error) {
    log.error('Failed to insert labels to bigQuery:', error);
    throw error;
  }
}

export async function syncAllLabelAssignmentsToBigQuery(
  labelAssignments?: LabelAssignment[]
) {
  if (
    labelAssignments === undefined ||
    (labelAssignments && labelAssignments.length === 0)
  )
    return;
  const config: BigQueryLabelControlConfig = {
    dataType: 'labelAssignments',
    data: labelAssignments,
    action: 'insert'
  };

  const labelAssignmentControl = new BigQueryLabelControl(config);
  try {
    const results = await labelAssignmentControl.execute();
    log.info(
      'Label assignments inserted successfully to bigQuery:',
      results.length
    );
    return results;
  } catch (error) {
    log.error('Failed to insert label assignments to bigQuery:', error);
    throw error;
  }
}
