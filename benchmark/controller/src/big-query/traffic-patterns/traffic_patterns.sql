WITH traffic_data AS (
  SELECT 
    dst_id, dst_addr, dst_type, 
    src_id, src_addr, src_type,
    port, 
    dst_name, src_name
  FROM `{{dataset}}.complete_traffic_patterns`
),

-- Precompute UUIDs in a temp table to avoid duplication
traffic_with_uuid AS (
  SELECT 
    GENERATE_UUID() AS id,
    *
  FROM traffic_data
),

-- MATERIALIZED: Helps avoid recalculation (BigQuery will auto-optimize this)
base_ingress AS (
  SELECT 
    dst_id AS entity_id,
    ARRAY_AGG(STRUCT(id, src_id, src_addr, src_type, dst_id, dst_addr, dst_type, port, dst_name, src_name) 
              ORDER BY src_addr 
              LIMIT 50) AS tps
  FROM traffic_with_uuid
  WHERE dst_type = 'aws_endpoint'
  GROUP BY dst_id
),

base_egress AS (
  SELECT 
    src_id AS entity_id,
    ARRAY_AGG(STRUCT(id, src_id, src_addr, src_type, dst_id, dst_addr, dst_type, port, dst_name, src_name) 
              ORDER BY dst_addr 
              LIMIT 50) AS tps
  FROM traffic_with_uuid
  WHERE src_type = 'aws_endpoint'
  GROUP BY src_id
)

SELECT 'ingress' AS traffic_type, * FROM base_ingress
UNION ALL
SELECT 'egress' AS traffic_type, * FROM base_egress;
