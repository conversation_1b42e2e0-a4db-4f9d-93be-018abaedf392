SELECT
  id,
  src_id,
  src_name,
  src_addr,
  src_process,
  src_cmd,
  src_type,
  dst_id,
  dst_name,
  dst_addr,
  dst_process,
  dst_cmd,
  dst_type,
  port,
  session_count,
  action,
  time,
  created_at
FROM {{dataset}}.recent_traffic
WHERE src_process IS NOT NULL
  AND (src_cmd IS NULL OR src_cmd NOT LIKE '%Windows Defender Advanced Threat Protection%')
  AND (dst_cmd IS NULL OR dst_cmd NOT LIKE '%Windows Defender Advanced Threat Protection%')
  AND LOWER(src_process) NOT IN (
    'chrome.exe',
    'msedgewebview2.exe',
    'svchost.exe',
    'onedrive.exe',
    'msedge.exe',
    'backgroundtaskhost.exe',
    'ms-teams.exe',
    'consul.exe',
    'excel.exe',
    'microsoft.sharepoint.exe',
    'lsass.exe',
    'outlook.exe',
    'msign.fileagent.exe',
    'fleet-otel-collector.exe',
    'nissrv.exe',
    'powerpnt.exe',
    'mpdefendercoreservice.exe',
    'filesynchelper.exe',
    'microsoft.management.services.intunewindowsagent.exe',
    'filecoauth.exe',
    'onenote.exe',
    'interactionworkspace.exe',
    'winword.exe',
    'searchhost.exe',
    'taskhostw.exe',
    'officeclicktorun.exe',
    'startmenuexperiencehost.exe',
    'widgets.exe',
    'slack.exe',
    'oktaverify.exe',
    'officec2rclient.exe',
    'sdxhelper.exe',
    'olk.exe',
    'grammarly.desktop.exe',
    'explorer.exe',
    'omadmclient.exe',
    'webviewhost.exe',
    'telegraf.exe',
    'mousocoreworker.exe',
    'microsoft.tri.sensor.exe',
    'firefox.exe',
    'com.apple.webkit.networking',
    'agentexecutor.exe',
    'adp-messaging-forwarder.exe',
    'lockapp.exe',
    'xpdagent.exe',
    'officesvcmgr.exe',
    'microsoft.notes.exe',
    'microsoftedgeupdate.exe',
    'dfsrs.exe',
    'msoadfsb.exe',
    'mssense.exe',
    'onedrivestandaloneupdater.exe',
    'okta.coordinator.service.exe',
    'dnscryptproxy.exe',
    'apphostregistrationverifier.exe',
    'opushutil.exe',
    'ntoskrnl.exe',
    'acumbrellaagent.exe',
    'clienthealtheval.exe',
    'microsoft outlook',
    'compattelrunner.exe',
    'spotify.exe',
    'sihclient.exe',
    'searchprotocolhost.exe',
    'clientcertcheck.exe',
    'weixin.exe',
    'acrobat.exe',
    'smartscreen.exe',
    'uhssvc.exe',
    'trustd',
    'acrocef.exe',
    'whatsapp.exe',
    'msmpeng.exe',
    'adobecollabsync.exe',
    'protocolhandler.exe',
    'onedrive',
    'cefsharp.browsersubprocess.exe',
    'phoneexperiencehost.exe',
    'wermgr.exe',
    'postman.exe',
    'code.exe',
    'windowspackagemanagerserver.exe',
    'cursor.exe',
    'mpdlpservice.exe',
    'konea.exe',
    'backgroundtransferhost.exe',
    'systemsettings.exe',
    'serviceshell.exe',
    'crossdeviceservice.exe',
    'spoolsv.exe',
    'microsoft excel',
    'onedrivelauncher.exe',
    'sgtool.exe',
    'nsurlsessiond',
    'rundll32.exe',
    'onenoteim.exe',
    'jamf connect',
    'line.exe',
    'update.exe',
    'com.apple.geod',
    'devicecensus.exe',
    'ivanti secure access',
    'zalo.exe',
    'onedrivesetup.exe',
    'setup.exe',
    'coresync.exe',
    'microsoft update assistant',
    'textinputhost.exe',
    'creative cloud ui helper.exe',
    'zoom.exe',
    'photos.exe',
    'appidcertstorecheck.exe',
    'microsoft powerpoint',
    'mac sso extension',
    'python.exe',
    'ms-teamsupdate.exe',
    'msteams',
    'gamebar.exe',
    'inventoryservice.exe',
    'tableau.exe',
    'mobileassetd',
    'authserviceextension',
    'acrotray.exe',
    'updater.exe',
    'astrill.exe',
    'node.exe',
    'parsec-fbf',
    'cloudd',
    'okta verify',
    'microsoft.exchange.directory.topologyservice.exe',
    'pmtad.exe',
    'kakaotalk.exe',
    'softwareupdated',
    'appstoreagent',
    'dsregcmd.exe',
    'backgrounddownload.exe',
    'adobe desktop service.exe',
    'w3wp.exe',
    'viber.exe',
    'promotedcontentd',
    'com.apple.safari.safebrowsing.service',
    'logioptionsplus_agent.exe',
    'parsecd',
    'thycotic.distributedengine.service.exe',
    'microsoft word',
    'wxwork.exe',
    'pulsesecureservice.exe',
    'jamf',
    'dell.d3.winsvc.exe',
    'sgpicfacetool.exe',
    'microsoft defender helper',
    'appstored',
    'integrator.exe',
    'sqlservr.exe',
    'perfwatson2.exe',
    'uxi-agent.exe',
    'wechatappex.exe',
    'rtcreportingd',
    'cloudtelemetryservice',
    'adobearm.exe',
    'complianceauditservice.exe',
    'idea64.exe',
    'storekitagent',
    'intelconnectivitynetworkservice.exe',
    'syspolicyd',
    'apsd',
    'mdmclient',
    'edgetransport.exe',
    'identityservicesd',
    'microsoft sharepoint',
    'creative cloud helper.exe',
    'runtimebroker.exe',
    'helpd',
    'vpnsvc.exe',
    'xpcproxy',
    'companyportal.exe',
    'java.exe',
    'sogoucloud.exe',
    'amplibraryagent',
    'amsaccountsd',
    'promecefpluginhost.exe',
    'edgeupdater',
    'icloudnotificationagent',
    'winstore.app.exe',
    'lenovovantageservice.exe',
    'creative cloud.exe',
    'devenv.exe',
    'adprivacyd',
    'osqueryd.exe',
    'akd',
    'assetcachelocatorservice',
    'idleassetsd',
    'teamviewer_service.exe',
    'locationd',
    'datagrip64.exe',
    'msexchangehmworker.exe',
    'microsoft onenote',
    'zsatunnel.exe',
    'webprivacyd',
    'stockswidget',
    'time.exe',
    'sgdownload.exe',
    'microsoft.exchange.imap4.exe',
    'acrobat update helper',
    'wpscloudsvr.exe',
    'pad.console.host.exe',
    'rider64.exe',
    'configserviceagent.exe',
    'grammarly desktop',
    'todo.exe',
    'asana.exe',
    'microsoft.exchange.pop3.exe',
    'pycharm64.exe',
    'crwindowsclientservice.exe',
    'onedrive file provider',
    'sppextcomobj.exe',
    'appleaccountd',
    'microsoftstartfeedprovider.exe',
    'discord.exe',
    'wslservice.exe',
    'wpscenter.exe',
    'microsoft.aad.brokerplugin.exe',
    'notion.exe',
    'safari',
    '2xproxygateway.exe',
    'microsoft.flow.rpa.logshipper.exe',
    'sgbizlauncher.exe',
    'wmiprvse.exe',
    'bingwallpaper.exe',
    'dataaccessd',
    'assistantd',
    'remindd',
    'sqlps.exe',
    'pingsender.exe',
    'nbagent',
    'mdnsresponder',
    'generic_collector.exe',
    'figma.exe',
    'alirender.exe',
    'clickup.exe',
    'mpcmdrun.exe',
    'wwahost.exe',
    'hd-player.exe',
    'httpd.exe',
    'music',
    'biz_helper.exe',
    'com.apple.icloudhelper',
    'swcd',
    'healthattestationclientagent.exe',
    'bluestacksservices.exe',
    'microsoft.exchange.servicehost.exe',
    'orbstack',
    'nvdisplay.container.exe',
    'pinyinup.exe',
    'googleupdater',
    'dell.techhub.instrumentation.subagent.exe',
    'distrib.exe',
    'aliworkbench.exe',
    'gamingservices.exe',
    'ddm.exe',
    'self service',
    'transparencyd',
    '1password.exe',
    'wechat.exe',
    'teamviewer.exe',
    'googledrivefs.exe',
    'hxtsr.exe',
    'rhs.exe',
    'onedriveupdaterservice.exe',
    'addressbooksourcesync',
    'dns.exe',
    'consul-template.exe',
    'submitdiaginfo',
    'vmms.exe',
    'werfault.exe',
    'facefoduninstaller.exe',
    'notes',
    'sparkle-cli',
    'weatherwidget',
    'com.apple.safari.searchhelper',
    'wps.exe',
    'logtransport2.exe',
    'findmylocateagent',
    'lenovovantage-(lenovosystemupdateaddin).exe',
    'grammarlyinstaller.exe',
    'core sync',
    'alibabaprotect.exe',
    'setuphost.exe',
    'travelport.smartpoint.app.exe',
    'linemediaplayer.exe',
    'wpsupdate.exe',
    'tcupdate.exe',
    'oktaverifysetup-********-3c784ec.exe',
    'captiveagent',
    'adobe_licensing_helper.exe',
    'searchpartyuseragent',
    'dxgiadaptercache.exe',
    'dell.coreservices.client.exe',
    'securityuploadd',
    'dpmservice.exe',
    'usernetschedule.exe',
    'msexchangefrontendtransport.exe',
    'thorium.exe',
    'onedrivestandaloneupdater',
    'ksomisc.exe',
    'appleidsettings',
    'user_context',
    'adobe_licensing_wf_helper_acro.exe',
    'msexchangerepl.exe',
    'wxworkweb.exe',
    'spotlight',
    'syncreporter',
    'icarus.exe',
    'cursor',
    'adobe crash processor.exe',
    'robloxplayerbeta.exe',
    'logiluupdater.exe',
    'captureservice.exe',
    'fleet-otel-collector',
    'whatsapp',
    'lineupdater.exe',
    'raycast',
    'familycircled',
    'run-backgrounder.exe',
    'controlcenter',
    'electron',
    'lenovovantage-(genericmessagingaddin).exe',
    'mstreamd',
    'sgsmartassistant.exe',
    'run-vizqlserver.exe',
    'reportingservicesservice.exe',
    'postman',
    'telegram.exe',
    'tabprotosrv.exe',
    'sirisuggestionsbookkeepingservice',
    'prtg probe.exe',
    'cloudphotod',
    'sogoutoolkits.exe',
    'msign.engine.exe',
    'mmc.exe',
    'ruximics.exe',
    'dropbox.exe',
    'silverfortcontroller.exe',
    'linecall.exe',
    'passwordbreachagent',
    'proxyman',
    'searchpartyd',
    'ndoagent',
    'copilot-language-server.exe',
    'rave.exe',
    'sogouexe.exe',
    'expressconnectnetworkservice.exe',
    'amsondevicestoraged',
    'memoq.exe',
    'studentd',
    'mc-fw-host.exe',
    'appssoagent',
    'dashost.exe',
    'chatgpt',
    'microsoftentraprivatenetworkconnectorservice.exe',
    'jamfprocommservice',
    'accountsd',
    'qq.exe',
    'spotify',
    'razer synapse service.exe',
    'photoanalysisd',
    'self service+',
    'bluestackshelper.exe',
    'photolaunch.exe',
    'adnotificationmanager.exe',
    'winlogbeat.exe',
    'dellsupportassistremedationservice.exe',
    'app store',
    'logioptionsplus_updater.exe',
    'capcut.exe',
    'gfxdownloadwrapper.exe',
    'grr.exe',
    'msexchangedelivery.exe',
    'healthservice.exe',
    'ciphermld',
    'msexchangedagmgmt.exe',
    'vpnupdate.exe',
    'chatgpt.exe',
    'self service+ agent',
    'onexcui.exe',
    'olicenseheartbeat.exe',
    'lockoutstatus.exe',
    'razercentralservice.exe',
    'servermanager.exe',
    'figma_agent',
    'com.apple.sbd',
    'jetbrains-toolbox.exe',
    'acrord32.exe',
    'dingtalk.exe',
    'appstorecomponentsd',
    'killernetworkservice.exe',
    'prevhost.exe',
    'databasemail.exe',
    'dashboard.exe',
    'sourcetree',
    'pbidesktop.exe',
    'senseimdscollector.exe',
    'slack',
    'shellexperiencehost.exe',
    'evernote.exe',
    'creative cloud content manager.node',
    'vsixautoupdate.exe',
    'launchd',
    'git-remote-https.exe',
    'senseir.exe',
    'et.exe',
    'categoriesservice',
    'mobileactivationd',
    'msexchangemailboxreplication.exe',
    'rider.backend.exe',
    'todoist.exe',
    'systemmigrationd',
    'amplibraryagent.exe',
    'adobeacrobat',
    'acrobat updater',
    'primevideo.exe',
    'automate.exe',
    'goland64.exe',
    '2xredundancy.exe',
    'bingsvc.exe',
    'steam.exe',
    'sogoucommgr.exe',
    'mremoteng.exe',
    'wpspdf.exe',
    'lark.exe',
    'findmydeviced',
    'exchangesyncd',
    'canva.exe',
    'adobe desktop service',
    'sslclientservice.exe',
    'microsoft.exchange.rpcclientaccess.service.exe',
    'ctfmon.exe',
    'iexplore.exe',
    'sublime_text',
    'eqscheduler.exe',
    'commerce',
    'newstoday2',
    'microsoft.exchange.mitigation.service.exe',
    'ucconfigtask.exe',
    'ciscocollabhost.exe',
    'callhistorysynchelper',
    'gamed',
    'microsoft.exchange.diagnostics.service.exe',
    'com.cisco.anyconnect.macos.acsockext',
    'dsamain.exe',
    'photoshop.exe',
    'iterm2',
    'tv',
    'signal.exe',
    'hxoutlook.exe',
    'dtexec.exe',
    'figma_agent.exe',
    'servicehub.vsdetouredhost.exe',
    'device-collector.exe',
    'vpn.exe',
    'dbeaver.exe',
    'sensecncproxy.exe',
    'webstorm64.exe',
    'stocks',
    'sharingd',
    'lenovovantage-(vantagecoreaddin).exe',
    'wsmprovhost.exe',
    'microsoft.mashup.container.loader.exe',
    'dell.trusteddevice.service.exe',
    'msmdsrv.exe',
    'adobe photoshop 2025',
    'microsoftsearchinbing.exe',
    'sgwebrender.exe',
    'rancher desktop.exe',
    'rustdesk.exe',
    'articulate 360 desktop service.exe',
    'chatgpthelper',
    'powertoys.exe',
    'infra-telegraf.exe',
    'dwm.exe',
    'obsidian.exe',
    'microsoft to do',
    'msexchangemailboxassistants.exe',
    'fleet-identityd.exe',
    'microsoft.servicehub.controller.exe',
    'visio.exe',
    'illustrator.exe',
    'fork',
    'gethelp.exe',
    'msexchangesubmission.exe',
    'certsrv.exe',
    'numbers',
    'secureconnector.exe',
    'azureadpasswordprotectiondcagent.exe',
    'mcscript_inuse.exe',
    'gytpolclientfw4_6_2.exe',
    'filesender.exe',
    'xagt.exe',
    'backgroundtaskhost.exe',
    'csfalconservice.exe',
    'ccmexec.exe',
    'backupexecmanagementservice.exe',
    'jstart.exe',
    'wmiprvse.exe',
    'officeclicktorun.exe',
    'masvc.exe',
    'gpresult.exe',
    'nacl64.exe',
    'sapftp.exe',
    'sqlbackup.exe',
    'ccmeval.exe',
    'microsoftedgeupdate.exe',
    'monitoringhost.exe',
    'disp+work.exe',
    'mcdatrep.exe',
    'lansweeperservice.exe',
    'ccmsetup.exe',
    'healthservice.exe',
    'syseverservice.exe',
    'zabbix_server',
    'selfservice.exe',
    'saplogon.exe',
    'loginfo.exe',
    'zabbix_agentd.exe',
    'zabbix_agent2.exe',
    'wssad.exe',
    'csfalconcontainer.exe',
    'gatescannerdesktopautostart.exe',
    'deviceenroller.exe',
    'dashost.exe',
    'proxysrv.exe',
    'mcshield.exe',
    'saplgpad.exe',
    'python2.7',
    'sntmon.exe',
    'balashapp.exe',
    'gwrd.exe',
    'loginfo.20220216t135345.exe',
    'maptoolkit.exe',
    'wfica32.exe',
    'prowin32.exe',
    'logonui.exe',
    'hdbsql.exe',
    'repadmin.exe',
    'dcdiag.exe',
    'isserverexec.exe',
    'sap_link_index.exe',
    'policyhost.exe',
    'mstsc.exe',
    'mfeatp.exe',
    'k3s',
    'aseu.exe',
    'sshd.exe',
    'cscript.exe',
    'agent.exe',
    'cvfwd.exe',
    'cmrcservice.exe',
    'sqlbackupmaster.exe',
    'observerwmimonitoringagent.exe',
    'cache.exe',
    'qad.client.exe',
    'clienthealtheval.exe',
    'werfault.exe',
    'mousocoreworker.exe',
    'core.exe',
    'iclinterface.exe',
    'waappagent.exe',
    'hdbindexserver',
    'java',
    'client.exe',
    'swjobengineworker2x64.exe',
    'clbackup.exe',
    'sppextcomobj.exe',
    'veeam.backup.manager.exe',
    'process-agent',
    'taskhost.exe',
    'uipath.executor.exe',
    'niping.exe',
    'microsoft.mashup.container.netfx45.exe',
    'searchnetprinters.exe',
    'orchestrator.sandbox.exe',
    'citrix.authentication.virtualsmartcard.exe',
    'monagentcore.exe',
    'psmrdpclient.exe',
    'icman.exe',
    'msaccess.exe',
    'arcgisindexingserver.exe'
  )
