import fs from 'fs';
import path from 'path';

const getBigQueryLocation = (dirname: string) => {
  const localPath = path.resolve(dirname, '../../big-query');

  //for local development
  if (fs.existsSync(localPath)) {
    return localPath;
  }
  //for docker build
  if (fs.existsSync('/app/big-query')) {
    return '/app/big-query';
  }

  //for firestore build
  return path.resolve(__dirname, 'assets/big-query');
};

const AWS_CREDENTIALS_LOCATION = path.join(
  process.env.HOME || process.env.USERPROFILE || '~',
  '.aws',
  'credentials'
);

export default {
  getBigQueryLocation,
  AWS_CREDENTIALS_LOCATION
};
