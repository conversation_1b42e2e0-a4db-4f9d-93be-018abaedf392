import * as admin from 'firebase-admin';

import { AuditLog, auditLogComponent } from './types';
import { firestore } from 'firebase-admin';

const MAX_EXPIRATION_DAYS = 180 * 24 * 60 * 60 * 1000; // 180 days
const DAY = 24 * 60 * 60 * 1000; // 1 day

const COMPONENT_LOG_EXPIRATION_DATE: Record<auditLogComponent, number> = {
  [auditLogComponent.ACTIVITY]: 180 * DAY, // 180 days for activity logs
  [auditLogComponent.FW_POLICY]: 30 * DAY // 30 days for firewall policy logs
};

const DEFAULT_USER_IP = '0.0.0.0';
const DEFAULT_USER_ID = 'unknown';

class FirestoreAuditLogger {
  private db?: admin.firestore.Firestore;

  constructor() {
    if (admin.apps.length === 0) {
      console.warn(
        '[FirestoreAuditLogger] Firebase not initialized yet. Delaying Firestore setup.'
      );
    } else {
      this.db = admin.firestore();
    }
  }
  private getDb(): admin.firestore.Firestore {
    if (!this.db) {
      if (!admin.apps.length) {
        throw new Error(
          '[FirestoreAuditLogger] Firebase not initialized. Please call admin.initializeApp() first.'
        );
      }
      this.db = admin.firestore(); // initialize lazily
    }
    return this.db;
  }
  private validateOrganizationId(organizationId: string) {
    if (!organizationId || organizationId.trim() == '') {
      throw new Error('organizationId cannot be empty');
    }
  }

  private getActivityRef(organizationId: string) {
    return this.getDb()
      .collection('organizations')
      .doc(organizationId)
      .collection('dashboards')
      .doc('NDR')
      .collection('activity');
  }

  private getCollectionRef(
    collectionPath: string
  ): firestore.CollectionReference {
    if (!collectionPath || collectionPath.trim() === '') {
      throw new Error('Collection path cannot be empty');
    }
    return this.getDb().collection(collectionPath);
  }

  private getExpirationDate(component?: auditLogComponent): Date {
    const time = new Date();
    if (component && COMPONENT_LOG_EXPIRATION_DATE[component]) {
      return new Date(
        time.getTime() + COMPONENT_LOG_EXPIRATION_DATE[component]
      );
    }
    return new Date(time.getTime() + MAX_EXPIRATION_DAYS);
  }

  async writeSingle(
    organizationId: string,
    auditLogData: AuditLog,
    collectionRefPath?: string,
    component?: auditLogComponent
  ): Promise<void> {
    try {
      this.validateOrganizationId(organizationId);
      const time = new Date();
      const expirationDate = this.getExpirationDate(component);
      const collectionRef =
        collectionRefPath && collectionRefPath?.length !== 0
          ? this.getCollectionRef(collectionRefPath)
          : this.getActivityRef(organizationId);
      const auditLogDataWithTimestamp = {
        ...auditLogData,
        createdAt: time,
        expiredAt: expirationDate,
        user: {
          ip: auditLogData.user?.ip ?? DEFAULT_USER_IP,
          id: auditLogData.user?.id ?? DEFAULT_USER_ID
        }
      };

      await collectionRef.add(auditLogDataWithTimestamp);
    } catch (error) {
      console.error('Error: Audit single document writing failed', error);
      return;
    }
  }

  async writeBatch(
    organizationId: string,
    auditLogs: AuditLog[],
    collectionRefPath?: string,
    component?: auditLogComponent
  ): Promise<void> {
    try {
      this.validateOrganizationId(organizationId);
      const time = new Date();
      const expirationDate = this.getExpirationDate(component);
      const collectionRef =
        collectionRefPath && collectionRefPath?.length !== 0
          ? this.getCollectionRef(collectionRefPath)
          : this.getActivityRef(organizationId);
      const batch = this.getDb().batch();

      for (let i = 0; i < auditLogs.length; i++) {
        const docRef = collectionRef.doc();
        const { ...auditLogData } = auditLogs[i];
        batch.set(docRef, {
          ...auditLogData,
          createdAt: time,
          expiredAt: expirationDate,
          user: {
            ip: auditLogData.user?.ip ?? DEFAULT_USER_IP,
            id: auditLogData.user?.id ?? DEFAULT_USER_ID
          }
        });
      }

      await batch.commit();
    } catch (error) {
      console.error('Error in writeBatch:', error);
      return;
    }
  }
}

export default new FirestoreAuditLogger();
