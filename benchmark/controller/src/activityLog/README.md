# Activity Log Writer

A simple utility for logging user activity in Firebase Firestore.

## Features

- Log user activities with timestamps
- Organize logs by organization
- Support for various event and asset types

## Usage

```typescript
import { logActivity } from './index';
import { EventTypes, AssetsTypes } from './types';

// Log an activity
await logActivity('organization-id', {
  userId: 'user-123',
  eventType: EventTypes.Creation,
  asset: AssetsTypes.Insights
});
```

## Types

The module supports the following event and asset types:

### Event Types

- Deletion
- Modification
- Creation
- Configuration

### Asset Types

- Integration Configuration
- Webhooks Configuration
- Invited Users
- Insights
- Saved Queries
- Labels
- Entity Label
- Condition
- Detection
- Controls
