export const EVENT_TYPES = {
  DELETION: 'deletion',
  MODIFICATION: 'modification',
  CREATION: 'creation'
} as const;

export type EventTypes = (typeof EVENT_TYPES)[keyof typeof EVENT_TYPES];

export const ASSETS_TYPES = {
  INTEGRATION_CONFIGURATION: 'integration_configuration',
  WEBHOOKS_CONFIGURATION: 'webhooks_configuration',
  USERS_INVITATIONS: 'users_invitations',
  USERS_SESSIONS: 'users_sessions',
  USER_MANAGEMENT: 'user_management',
  INSIGHTS: 'insights',
  SAVED_QUERIES: 'saved_queries',
  LABELS: 'labels',
  ENTITY_LABEL: 'entity_label',
  LABEL_CONDITION: 'label_condition',
  DETECTION: 'detection',
  CONTROLS: 'controls',
  FIREWALL: 'firewall',
  FIREWALL_POLICY: 'firewall_policy'
} as const;

export type AssetsTypes = (typeof ASSETS_TYPES)[keyof typeof ASSETS_TYPES];

export type AuditLog = {
  createdAt?: Date;
  expiredAt?: Date;
  user?: { id: string; ip?: string };
  eventType: EventTypes;
  asset: AssetsTypes;
  additionalData?: Record<string, any>;
};

export const enum auditLogComponent {
  ACTIVITY = 'activity',
  FW_POLICY = 'firewall_policy'
}
