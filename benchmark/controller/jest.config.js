module.exports = {
  rootDir: 'src',
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/*.test.ts', '**/*.spec.ts'],
  transform: {
    '^.+\\.tsx?$': 'ts-jest'
  },
  moduleNameMapper: {
    '@globalTypes/ndrBenchmarkTypes': '<rootDir>/globalTypes/ndrBenchmarkTypes',
    '@logger': '<rootDir>/logger',
    '@big-query-client/(.*)': '<rootDir>/big-query-client/$1'
  },
  transformIgnorePatterns: [
    'node_modules/(?!(p-queue|p-retry|eventemitter3)/)'
  ],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node']
};
