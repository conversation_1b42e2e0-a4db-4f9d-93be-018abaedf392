name: Prettier Check

on:
  pull_request:
    branches:
      - main # ✅ Run when a PR is opened, synchronized, or updated targeting "main"

jobs:
  prettier-check:
    runs-on: ubuntu-latest
    container: node:18-slim # 🧹 Lean container: Node.js 18 slim image

    defaults:
      run:
        working-directory: benchmark/controller

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Install Git
        run: apt-get update && apt-get install -y git

      - name: Install Dependencies
        run: npm install

      - name: Run Prettier Check for .ts Files
        run: npm run prettier-check
