name: Build and Push to Artifact Registry (gcr.io)

on:
  push:
    branches:
      - dev
      - main
  workflow_dispatch: # ✅ Allows manual triggering

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name || github.head_ref }} # Dynamically select environment
    env:
      IMAGE_NAME: gcr.io/${{ secrets.GCLOUD_PROJECT }}/benchmark

    steps:
      - name: Checkout repository without submodules
        uses: actions/checkout@v3
        with:
          submodules: false # Do not fetch submodules initially
          fetch-depth: 0 # Ensure full history is fetched

      - name: Set up and fetch all submodules
        env:
          LOGGER_REPO_ACCESS: ${{ secrets.LOGGER_REPO_ACCESS }}
          GLOBALTYPES_REPO_ACCESS: ${{ secrets.GLOBALTYPES_REPO_ACCESS }}
          ACTIVITY_REPO_ACCESS: ${{ secrets.ACTIVITY_REPO_ACCESS }}
          BIG_QUERY_CLIENT_REPO_ACCESS: ${{ secrets.BIG_QUERY_CLIENT_REPO_ACCESS }}
        run: |
          submodules=(
            "LOGGER benchmark/controller/src/logger **************:port0-io/logger.git"
            "GLOBALTYPES benchmark/controller/src/globalTypes **************:port0-io/globalTypes.git"
            "ACTIVITY benchmark/controller/src/activityLog **************:port0-io/activityLog.git"
            "BIG_QUERY_CLIENT benchmark/controller/src/big-query-client **************:port0-io/big-query-client.git"
          )

          mkdir -p ~/.ssh
          ssh-keyscan -t rsa github.com >> ~/.ssh/known_hosts

          for entry in "${submodules[@]}"; do
            read -r name path repo <<< "$entry"
            echo "🔄 Setting up $name..."

            secret_var="${name}_REPO_ACCESS"
            key_value="${!secret_var}"
            key_file="${HOME}/.ssh/${name}_key"

            echo "$key_value" > "$key_file"
            chmod 600 "$key_file"

            echo "Host github.com
                  HostName github.com
                  User git
                  IdentityFile $key_file" >> ~/.ssh/config

            git submodule set-url "$path" "$repo"
            git submodule sync --recursive
            GIT_SSH_COMMAND="ssh -i \"$key_file\" -o IdentitiesOnly=yes" \
              git submodule update --init --recursive "$path"

            echo "✅ $name ready"
          done

      - name: Authenticate with Google Cloud (Skip for act)
        if: env.ACT == ''
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}

      - name: Set up Google Cloud SDK (Skip for act)
        if: env.ACT == ''
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker to use gcr.io inside Artifact Registry (Skip for act)
        if: env.ACT == ''
        run: gcloud auth configure-docker gcr.io

      - name: Build Docker Image with Buildx
        run: |
          cd benchmark &&
          docker buildx build --platform linux/amd64 \
            -t $IMAGE_NAME:latest \
            -t $IMAGE_NAME:${{ github.sha }} \
            -f Dockerfile .  # Explicitly specify Dockerfile location
          echo "✅ Build succeeded!"

      - name: Push Docker Image (Disabled for act)
        if: env.ACT == ''
        run: |
          docker push $IMAGE_NAME:latest
          docker push $IMAGE_NAME:${{ github.sha }}
