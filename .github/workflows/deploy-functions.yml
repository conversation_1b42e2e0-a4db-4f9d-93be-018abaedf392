name: Deploy Cloud Functions

on:
  push:
    branches:
      - main
      - dev
  workflow_dispatch: # allow manual deployment

jobs:
  deploy:
    name: Deploy to Firebase
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name || github.head_ref }} # Dynamically select environment

    env:
      GCLOUD_PROJECT: ${{ secrets.GCLOUD_PROJECT }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: false # Do not fetch submodules initially
          fetch-depth: 0 # Ensure full history is fetched

      - name: Set up and fetch all submodules
        env:
          LOGGER_REPO_ACCESS: ${{ secrets.LOGGER_REPO_ACCESS }}
          GLOBALTYPES_REPO_ACCESS: ${{ secrets.GLOBALTYPES_REPO_ACCESS }}
          ACTIVITY_REPO_ACCESS: ${{ secrets.ACTIVITY_REPO_ACCESS }}
          BIG_QUERY_CLIENT_REPO_ACCESS: ${{ secrets.BIG_QUERY_CLIENT_REPO_ACCESS }}
        run: |
          submodules=(
            "LOGGER benchmark/controller/src/logger **************:port0-io/logger.git"
            "GLOBALTYPES benchmark/controller/src/globalTypes **************:port0-io/globalTypes.git"
            "ACTIVITY benchmark/controller/src/activityLog **************:port0-io/activityLog.git"
            "BIG_QUERY_CLIENT benchmark/controller/src/big-query-client **************:port0-io/big-query-client.git"
          )

          mkdir -p ~/.ssh
          ssh-keyscan -t rsa github.com >> ~/.ssh/known_hosts

          for entry in "${submodules[@]}"; do
            read -r name path repo <<< "$entry"
            echo "🔄 Setting up $name..."

            secret_var="${name}_REPO_ACCESS"
            key_value="${!secret_var}"
            key_file="${HOME}/.ssh/${name}_key"

            echo "$key_value" > "$key_file"
            chmod 600 "$key_file"

            echo "Host github.com
                  HostName github.com
                  User git
                  IdentityFile $key_file" >> ~/.ssh/config

            git submodule set-url "$path" "$repo"
            git submodule sync --recursive
            GIT_SSH_COMMAND="ssh -i \"$key_file\" -o IdentitiesOnly=yes" \
              git submodule update --init --recursive "$path"

            echo "✅ $name ready"
          done

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install Firebase CLI
        run: npm install -g firebase-tools

      - name: Install Dependencies for Controller
        run: |
          cd benchmark/controller
          npm install --include=dev
          npm install -g typescript

      - name: Install Dependencies for Functions
        run: |
          cd functions
          npm install --include=dev
          npm install -g typescript

      - name: Select Service Account JSON Key
        run: |
          echo "Using GitHub Environment Secrets"
          echo '${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}' | jq -r '.' > "${HOME}/cicd-deployer-key.json"

      - name: Authenticate with Google Cloud
        run: |
          gcloud auth activate-service-account --key-file="${HOME}/cicd-deployer-key.json"
          gcloud config set project $GCLOUD_PROJECT

      - name: Deploy Cloud Functions (Skip if Running in `act`)
        if: ${{ env.ACT != 'true' }}
        run: |
          echo "Deploying to Firebase..."
          firebase deploy --only functions --token $(gcloud auth print-access-token) --project $GCLOUD_PROJECT
